// ===== DATABASE CONNECTION POOL (WITH ENVIRONMENT VARIABLES) =====
// Author: Augment Code
// Description: Centralized database connection pool for SQLite with optimized settings using environment variables

// Load environment variables
require('dotenv').config();

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DatabasePool {
  constructor() {
    this.db = null;
    this.isConnected = false;

    // Database configuration from environment variables
    this.config = {
      // Database path - use environment variable or default
      dbPath: process.env.DB_PATH ?
        (path.isAbsolute(process.env.DB_PATH) ? process.env.DB_PATH : path.join(__dirname, process.env.DB_PATH)) :
        path.join(__dirname, 'contract-app.sqlite'),

      // Connection settings
      connectionRetries: parseInt(process.env.DB_CONNECTION_RETRIES) || 3,
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 60000,

      // SQLite optimization settings from environment
      journalMode: process.env.DB_JOURNAL_MODE || 'WAL',
      synchronous: process.env.DB_SYNCHRONOUS || 'NORMAL',
      cacheSize: parseInt(process.env.DB_CACHE_SIZE) || 1000,
      tempStore: process.env.DB_TEMP_STORE || 'MEMORY',
      busyTimeout: parseInt(process.env.DB_BUSY_TIMEOUT) || 30000,
      pageSize: parseInt(process.env.DB_PAGE_SIZE) || 4096,
      foreignKeys: process.env.DB_FOREIGN_KEYS || 'ON',
      queryOnly: process.env.DB_QUERY_ONLY || 'OFF',

      // Logging settings
      logQueries: process.env.DB_LOG_QUERIES === 'true',
      logErrors: process.env.DB_LOG_ERRORS === 'true',
      logPerformance: process.env.DB_LOG_PERFORMANCE === 'true',
      verboseLogging: process.env.VERBOSE_LOGGING === 'true'
    };

    // Log configuration on startup
    if (this.config.verboseLogging) {
      console.log('🔧 Database Configuration:');
      console.log(`   - Database Path: ${this.config.dbPath}`);
      console.log(`   - Journal Mode: ${this.config.journalMode}`);
      console.log(`   - Cache Size: ${this.config.cacheSize} pages`);
      console.log(`   - Busy Timeout: ${this.config.busyTimeout}ms`);
      console.log(`   - Connection Retries: ${this.config.connectionRetries}`);
    }
  }

  // Initialize database connection with optimized settings
  async connect() {
    if (this.isConnected && this.db) {
      console.log('📊 Database already connected, reusing existing connection');
      return this.db;
    }

    return new Promise((resolve, reject) => {
      console.log('🔌 Initializing database connection pool...');
      console.log(`📁 Database path: ${this.config.dbPath}`);

      this.db = new sqlite3.Database(
        this.config.dbPath,
        sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE,
        (err) => {
          if (err) {
            console.error('❌ Database connection error:', err.message);
            reject(err);
            return;
          }

          console.log('✅ Connected to SQLite database successfully');
          this.isConnected = true;

          // Apply optimized SQLite settings for better performance
          this.optimizeDatabase()
            .then(() => {
              console.log('⚡ Database optimizations applied successfully');
              resolve(this.db);
            })
            .catch((optimizeErr) => {
              console.error('⚠️ Database optimization failed:', optimizeErr);
              // Still resolve as connection is working, just not optimized
              resolve(this.db);
            });
        }
      );
    });
  }

  // Apply SQLite optimizations for better performance using environment variables
  async optimizeDatabase() {
    return new Promise((resolve, reject) => {
      const optimizations = [
        // Enable WAL mode for better concurrency (from env)
        `PRAGMA journal_mode = ${this.config.journalMode};`,

        // Set synchronous mode (from env)
        `PRAGMA synchronous = ${this.config.synchronous};`,

        // Set cache size (from env)
        `PRAGMA cache_size = ${this.config.cacheSize};`,

        // Store temporary tables in memory for faster operations (from env)
        `PRAGMA temp_store = ${this.config.tempStore};`,

        // Enable foreign key constraints (from env)
        `PRAGMA foreign_keys = ${this.config.foreignKeys};`,

        // Set busy timeout (from env)
        `PRAGMA busy_timeout = ${this.config.busyTimeout};`,

        // Optimize for faster reads (from env)
        `PRAGMA query_only = ${this.config.queryOnly};`,

        // Set page size (from env)
        `PRAGMA page_size = ${this.config.pageSize};`
      ];

      let completed = 0;
      const total = optimizations.length;

      optimizations.forEach((pragma, index) => {
        this.db.run(pragma, (err) => {
          if (err) {
            if (this.config.logErrors) {
              console.error(`⚠️ Failed to apply optimization ${index + 1}:`, err.message);
            }
          } else {
            if (this.config.verboseLogging) {
              console.log(`✅ Applied optimization ${index + 1}/${total}: ${pragma.split(' ')[1]}`);
            }
          }

          completed++;
          if (completed === total) {
            resolve();
          }
        });
      });
    });
  }

  // Get database connection (singleton pattern)
  getConnection() {
    if (!this.isConnected || !this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db;
  }

  // Execute a query with error handling and logging
  async query(sql, params = []) {
    return new Promise((resolve, reject) => {
      const db = this.getConnection();
      
      if (this.config.logQueries) {
        console.log(`🔍 Executing query: ${sql.substring(0, 100)}${sql.length > 100 ? '...' : ''}`);
      }

      db.all(sql, params, (err, rows) => {
        if (err) {
          if (this.config.logErrors) {
            console.error('❌ Query error:', err.message);
            console.error('📝 SQL:', sql);
            console.error('📋 Params:', params);
          }
          reject(err);
        } else {
          if (this.config.logQueries) {
            console.log(`✅ Query executed successfully, returned ${rows.length} rows`);
          }
          resolve(rows);
        }
      });
    });
  }

  // Execute a single row query
  async get(sql, params = []) {
    return new Promise((resolve, reject) => {
      const db = this.getConnection();
      
      if (this.config.logQueries) {
        console.log(`🔍 Executing get query: ${sql.substring(0, 100)}${sql.length > 100 ? '...' : ''}`);
      }

      db.get(sql, params, (err, row) => {
        if (err) {
          if (this.config.logErrors) {
            console.error('❌ Get query error:', err.message);
            console.error('📝 SQL:', sql);
            console.error('📋 Params:', params);
          }
          reject(err);
        } else {
          if (this.config.logQueries) {
            console.log(`✅ Get query executed successfully, returned ${row ? 1 : 0} row`);
          }
          resolve(row);
        }
      });
    });
  }

  // Execute an insert/update/delete query
  async run(sql, params = []) {
    return new Promise((resolve, reject) => {
      const db = this.getConnection();
      
      if (this.config.logQueries) {
        console.log(`🔍 Executing run query: ${sql.substring(0, 100)}${sql.length > 100 ? '...' : ''}`);
      }

      db.run(sql, params, function(err) {
        if (err) {
          if (this.config.logErrors) {
            console.error('❌ Run query error:', err.message);
            console.error('📝 SQL:', sql);
            console.error('📋 Params:', params);
          }
          reject(err);
        } else {
          if (this.config.logQueries) {
            console.log(`✅ Run query executed successfully, changes: ${this.changes}, lastID: ${this.lastID}`);
          }
          resolve({
            changes: this.changes,
            lastID: this.lastID
          });
        }
      }.bind(this));
    });
  }

  // Execute multiple queries in a transaction
  async transaction(queries) {
    return new Promise((resolve, reject) => {
      const db = this.getConnection();
      
      console.log(`🔄 Starting transaction with ${queries.length} queries`);
      
      db.serialize(() => {
        db.run('BEGIN TRANSACTION;');
        
        let completed = 0;
        let hasError = false;
        const results = [];

        queries.forEach((query, index) => {
          if (hasError) return;

          const { sql, params = [] } = query;
          
          db.run(sql, params, function(err) {
            if (err && !hasError) {
              hasError = true;
              console.error(`❌ Transaction error at query ${index + 1}:`, err.message);
              db.run('ROLLBACK;');
              reject(err);
              return;
            }

            results.push({
              changes: this.changes,
              lastID: this.lastID
            });

            completed++;
            if (completed === queries.length && !hasError) {
              db.run('COMMIT;', (commitErr) => {
                if (commitErr) {
                  console.error('❌ Transaction commit error:', commitErr.message);
                  reject(commitErr);
                } else {
                  console.log('✅ Transaction completed successfully');
                  resolve(results);
                }
              });
            }
          });
        });
      });
    });
  }

  // Close database connection
  async close() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve();
        return;
      }

      console.log('🔌 Closing database connection...');
      
      this.db.close((err) => {
        if (err) {
          console.error('❌ Error closing database:', err.message);
          reject(err);
        } else {
          console.log('✅ Database connection closed successfully');
          this.isConnected = false;
          this.db = null;
          resolve();
        }
      });
    });
  }

  // Get database statistics
  async getStats() {
    try {
      const stats = await this.query(`
        SELECT 
          (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as tableCount,
          (SELECT page_count FROM pragma_page_count()) as pageCount,
          (SELECT page_size FROM pragma_page_size()) as pageSize,
          (SELECT journal_mode FROM pragma_journal_mode()) as journalMode,
          (SELECT synchronous FROM pragma_synchronous()) as synchronous,
          (SELECT cache_size FROM pragma_cache_size()) as cacheSize
      `);
      
      return stats[0];
    } catch (error) {
      console.error('❌ Error getting database stats:', error.message);
      return null;
    }
  }
}

// Create singleton instance
const dbPool = new DatabasePool();

// Export the pool instance and helper methods
module.exports = {
  // Main database pool instance
  dbPool,
  
  // Convenience methods that use the pool
  connect: () => dbPool.connect(),
  query: (sql, params) => dbPool.query(sql, params),
  get: (sql, params) => dbPool.get(sql, params),
  run: (sql, params) => dbPool.run(sql, params),
  transaction: (queries) => dbPool.transaction(queries),
  close: () => dbPool.close(),
  getStats: () => dbPool.getStats(),
  getConnection: () => dbPool.getConnection()
};
