// ===== SETTINGS ROUTES =====
// Author: Augment Code
// Description: Routes for managing application settings

const express = require('express');
const router = express.Router();

// Import database connection pool
const { get, run } = require('../db.cjs');

// Import validation utilities
const {
  validateSettingsData,
  sanitizeString,
  sanitizeNumber
} = require('../validation-utils.cjs');
// Unified settings endpoint - handles both GET and POST
router.route('/')
  .get(async (req, res) => {
    console.log('📖 GET /api/settings request');

    try {
      const row = await get("SELECT * FROM Settings ORDER BY id DESC LIMIT 1");

      if (row) {
        // Convert JSON strings to arrays for frontend
        try {
          row.regions = row.regions ? JSON.parse(row.regions) : [];
          row.owners = row.owners ? JSON.parse(row.owners) : [];
          row.governorates = row.governorates ? JSON.parse(row.governorates) : [];
          row.workDays = row.workDays ? JSON.parse(row.workDays) : [];
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
        }

        console.log('✅ Settings retrieved successfully');
        res.json(row);
      } else {
        console.log('⚠️ No settings found, returning defaults');
        res.json({
          companyName: 'شركة إدارة العقود المصرية',
          programName: 'نظام إدارة العقود المتقدم',
          language: 'ar',
          currency: 'جنيه مصري',
          currencySymbol: 'ج.م',
          country: 'مصر'
        });
      }
    } catch (err) {
      console.error('❌ Error getting settings:', err);
      res.status(500).json({ error: err.message });
    }
  })
  .post(async (req, res) => {
    console.log('🔥🔥🔥 POST /api/settings request received!');
    console.log('📦 Received data keys:', Object.keys(req.body));
    console.log('📊 Data size:', JSON.stringify(req.body).length, 'characters');

    // Validate settings data
    console.log('🔍 Starting validation...');
    const validation = validateSettingsData(req.body);
    console.log('🔍 Validation result:', validation);

    if (!validation.isValid) {
      console.log('❌ Settings validation failed:', validation.errors);
      return res.status(400).json({
        error: 'بيانات الإعدادات غير صحيحة',
        details: validation.errors
      });
    }

    console.log('✅ Settings validation passed');
    const settingsData = req.body;

    console.log('🔧 Starting settings processing...');
    console.log('📋 Sample data:', {
      companyName: settingsData.companyName,
      language: settingsData.language,
      currency: settingsData.currency
    });

    try {
      console.log('🔄 Entering try block...');

        // Extract core fields that we know work + UI settings + localization
        const coreFields = {
          // Company info
          companyName: settingsData.companyName || 'شركة إدارة العقود',
          programName: settingsData.programName || 'نظام إدارة العقود المتقدم',
          companyRegNo: settingsData.companyRegNo || '',
          taxId: settingsData.taxId || '',
          companyAddress: settingsData.companyAddress || '',
          companyPhone: settingsData.companyPhone || '',
          companyEmail: settingsData.companyEmail || '',
          companyWebsite: settingsData.companyWebsite || '',

          // Localization (المنطقة)
          language: settingsData.language || 'ar',
          country: settingsData.country || 'السعودية',
          currency: settingsData.currency || 'ريال سعودي',
          currencySymbol: settingsData.currencySymbol || 'ر.س',
          timeFormat: settingsData.timeFormat || '24',
          timeZone: settingsData.timeZone || 'Asia/Riyadh',
          fiscalYearStart: settingsData.fiscalYearStart || '01/01',

          // UI settings
          theme: settingsData.theme || 'system',
          fontSize: settingsData.fontSize || 'medium',
          primaryColor: settingsData.primaryColor || '#3b82f6',
          fontFamily: settingsData.fontFamily || 'Cairo',

          // Work days
          workDays: settingsData.workDays || ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس"],

          // Additional company fields
          about: settingsData.about || '',
          dateFormat: settingsData.dateFormat || 'yyyy/mm/dd',

          // Notifications
          notificationEmail: settingsData.notificationEmail || '',
          notificationFrequency: settingsData.notificationFrequency || 'daily',
          enableEmailNotifications: settingsData.enableEmailNotifications || false,
          enableSMSNotifications: settingsData.enableSMSNotifications || false,

          // Business settings
          defaultContractDuration: settingsData.defaultContractDuration || '12',
          defaultPaymentTerms: settingsData.defaultPaymentTerms || 'شهري',
          defaultTaxRate: settingsData.defaultTaxRate || '15',
          backupFrequency: settingsData.backupFrequency || 'daily',
          enableMultiCurrency: settingsData.enableMultiCurrency || false,

          // Security settings
          sessionTimeout: settingsData.sessionTimeout || '30',
          maxLoginAttempts: settingsData.maxLoginAttempts || '5',
          passwordPolicy: settingsData.passwordPolicy || 'medium',
          enableTwoFactor: settingsData.enableTwoFactor || false,
          enableAuditLog: settingsData.enableAuditLog || false,

          // System settings
          cacheTimeout: settingsData.cacheTimeout || '60',
          maxFileSize: settingsData.maxFileSize || '10',
          enableCaching: settingsData.enableCaching || true,
          enableCompression: settingsData.enableCompression || false,
          enableDebugMode: settingsData.enableDebugMode || false
        };

        console.log('💾 Core fields to save:', coreFields);

        // Check if record exists
        const existingRecord = await get("SELECT COUNT(*) as count FROM Settings");

        if (existingRecord.count === 0) {
          // Insert new record with core fields only
          console.log('✨ Inserting new record with core fields');
          const result = await run(`
            INSERT INTO Settings (
              companyName, programName, about, companyRegNo, taxId,
              language, country, currency, currencySymbol, dateFormat,
              notificationEmail, workDays,
              createdAt, updatedAt
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `, [
            coreFields.companyName,
            coreFields.programName,
            coreFields.about,
            coreFields.companyRegNo,
            coreFields.taxId,
            coreFields.language,
            coreFields.country,
            coreFields.currency,
            coreFields.currencySymbol,
            coreFields.dateFormat,
            coreFields.notificationEmail,
            JSON.stringify(coreFields.workDays)
          ]);

          console.log('✅ Settings inserted successfully with ID:', result.lastID);
          res.json({
            success: true,
            message: 'Settings saved successfully',
            id: result.lastID,
            savedData: coreFields
          });
        } else {
          // Update existing record with core fields
          console.log('🔄 Updating existing record with core fields');
          const result = await run(`
            UPDATE Settings SET
              companyName=?, programName=?, about=?, companyRegNo=?, taxId=?,
              language=?, country=?, currency=?, currencySymbol=?, dateFormat=?,
              notificationEmail=?, workDays=?,
              updatedAt=CURRENT_TIMESTAMP
            WHERE id = (SELECT id FROM Settings ORDER BY id DESC LIMIT 1)
          `, [
            coreFields.companyName,
            coreFields.programName,
            coreFields.about,
            coreFields.companyRegNo,
            coreFields.taxId,
            coreFields.language,
            coreFields.country,
            coreFields.currency,
            coreFields.currencySymbol,
            coreFields.dateFormat,
            coreFields.notificationEmail,
            JSON.stringify(coreFields.workDays)
          ]);

          console.log('✅ Settings updated successfully');
          res.json({
            success: true,
            message: 'Settings updated successfully',
            savedData: coreFields
          });
        }

      } catch (error) {
        console.error('🔥 CRITICAL ERROR in settings POST:', error);
        console.error('🔥 Error stack:', error.stack);
        res.status(500).json({
          error: 'خطأ في الخادم أثناء حفظ الإعدادات',
          details: error.message
        });
      }
    });

module.exports = router;
