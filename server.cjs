// ===== CONTRACT MANAGEMENT SYSTEM SERVER =====
// Author: Augment Code
// Version: 1.0.0
//
// DEVELOPER NOTES:
// This server now supports two contract tables:
// 1. Contracts (original table) - Used by all existing endpoints
// 2. NewContracts (new table) - Used by new endpoints at /api/new-contracts/*
//
// To use the new table:
// - Create contracts with POST /api/new-contracts
// - Get contracts with GET /api/new-contracts
// - Get a specific contract with GET /api/new-contracts/:id
//
// The new table has a cleaner schema with better organization and support for all features.
// This dual-table approach allows for gradual migration without breaking existing functionality.
// ===== END DEVELOPER NOTES =====

console.log('🚀 Starting Contract Management Server...');

const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Import validation utilities
const {
  validateClientData,
  validateContractData,
  validateProductData,
  validateSettingsData,
  sanitizeString,
  sanitizeNumber
} = require('./validation-utils.cjs');

console.log('📦 Modules loaded successfully');

const app = express();
app.use(cors());
app.use(express.json({ limit: '10mb' })); // زيادة حد حجم JSON لرفع الصور
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Global error handling middleware
app.use((err, req, res, next) => {
  console.error('🚨 Global error handler:', err);

  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json({
      error: 'تنسيق البيانات غير صحيح',
      details: ['البيانات المرسلة ليست في تنسيق JSON صحيح']
    });
  }

  res.status(500).json({
    error: 'خطأ داخلي في الخادم',
    details: ['حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى']
  });
});

console.log('⚙️ Express middleware configured');

// Log all requests
app.use((req, _res, next) => {
  console.log(`🌐 ${req.method} ${req.url} - ${new Date().toISOString()}`);
  next();
});

// Create SQLite database with better error handling and connection pooling
const dbPath = path.join(__dirname, 'contract-app.sqlite');
let db;

// Database connection with retry mechanism
function connectToDatabase(retries = 3) {
  return new Promise((resolve, reject) => {
    db = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
      if (err) {
        console.error('❌ Database connection error:', err.message);
        if (retries > 0) {
          console.log(`🔄 Retrying database connection... (${retries} attempts left)`);
          setTimeout(() => {
            connectToDatabase(retries - 1).then(resolve).catch(reject);
          }, 1000);
        } else {
          reject(err);
        }
      } else {
        console.log('✅ Connected to SQLite database successfully');

        // Enable WAL mode for better concurrency
        db.run('PRAGMA journal_mode = WAL;');
        db.run('PRAGMA synchronous = NORMAL;');
        db.run('PRAGMA cache_size = 1000;');
        db.run('PRAGMA temp_store = MEMORY;');

        resolve(db);
      }
    });
  });
}

console.log('🚀 Starting server with enhanced SQLite...');

// Enhanced function to generate receivables from contract products with smart merging
function generateContractReceivables(contract, callback) {
  console.log('🔄 Generating enhanced receivables for contract:', contract.contractNumber);

  const {
    id: contractId,
    clientId,
    contractNumber,
    startDate,
    totalContractValue,
    paymentFrequency,
    contractDurationYears
  } = contract;

  // First, try to get contract products for enhanced calculation
  db.all("SELECT * FROM ContractProducts WHERE contractId = ? AND isActive = 1", [contractId], (err, products) => {
    if (err) {
      console.error('Error getting contract products:', err);
      return generateSimpleReceivables(contract, callback);
    }

    if (!products || products.length === 0) {
      console.log('No products found, using simple calculation');
      return generateSimpleReceivables(contract, callback);
    }

    console.log(`📊 Found ${products.length} products for enhanced receivables generation`);
    return generateEnhancedReceivables(contract, products, callback);
  });
}

// Simple receivables generation (fallback)
function generateSimpleReceivables(contract, callback) {
  console.log('🔄 Generating simple receivables for contract:', contract.contractNumber);

  const {
    id: contractId,
    clientId,
    contractNumber,
    startDate,
    totalContractValue,
    paymentFrequency,
    contractDurationYears,
    contractStatus = 'نشط'
  } = contract;

  // Check contract status and adjust behavior accordingly
  let effectiveStartDate = startDate;
  let shouldGenerateRevenues = true;

  if (contractStatus === 'غير نشط') {
    // For inactive contracts: use contract creation date as start date, no revenues
    effectiveStartDate = contract.contractDate || startDate;
    shouldGenerateRevenues = false;
    console.log('⚠️ Contract is inactive - receivables will be generated without revenues');
  } else if (contractStatus === 'منتهي') {
    // For expired contracts: don't generate new receivables
    console.log('⚠️ Contract is expired - no new receivables will be generated');
    return callback(null, 0);
  }

  // Calculate number of installments and amount based on payment frequency and total contract value
  let installmentCount = 0;
  let monthsInterval = 1;
  let installmentAmount = 0;

  switch (paymentFrequency) {
    case 'شهري':
      monthsInterval = 1;
      installmentCount = contractDurationYears * 12;
      installmentAmount = shouldGenerateRevenues ? (totalContractValue / installmentCount) : 0;
      break;
    case 'ربع سنوي':
      monthsInterval = 3;
      installmentCount = contractDurationYears * 4;
      installmentAmount = shouldGenerateRevenues ? (totalContractValue / installmentCount) : 0;
      break;
    case 'نصف سنوي':
      monthsInterval = 6;
      installmentCount = contractDurationYears * 2;
      installmentAmount = shouldGenerateRevenues ? (totalContractValue / installmentCount) : 0;
      break;
    case 'سنوي':
      monthsInterval = 12;
      installmentCount = contractDurationYears;
      installmentAmount = shouldGenerateRevenues ? (totalContractValue / installmentCount) : 0;
      break;
    default:
      monthsInterval = 1;
      installmentCount = contractDurationYears * 12;
      installmentAmount = shouldGenerateRevenues ? (totalContractValue / installmentCount) : 0;
      break;
  }

  console.log(`📊 Contract ${contractNumber}: ${paymentFrequency} payments`);
  console.log(`   Installment amount: ${installmentAmount}`);
  console.log(`   ${installmentCount} installments, every ${monthsInterval} months`);

  // Generate receivables
  const receivables = [];
  const contractStartDate = new Date(effectiveStartDate);

  for (let i = 0; i < installmentCount; i++) {
    const dueDate = new Date(contractStartDate);
    dueDate.setMonth(dueDate.getMonth() + (i * monthsInterval));

    const receivableNumber = `${contractNumber}-${String(i + 1).padStart(3, '0')}`;

    // Adjust description based on contract status
    let description = `قسط ${paymentFrequency} رقم ${i + 1} من ${installmentCount}`;
    if (contractStatus === 'غير نشط') {
      description += ' (عقد غير نشط - بدون إيرادات)';
    }

    receivables.push({
      contractId,
      clientId,
      receivableNumber,
      dueDate: dueDate.toISOString().split('T')[0],
      amount: installmentAmount,
      description: description,
      status: 'لم يحن موعده',
      paymentFrequency,
      installmentNumber: i + 1,
      totalInstallments: installmentCount
    });
  }

  // Insert receivables into database
  const insertSQL = `
    INSERT INTO ContractReceivables (
      contractId, clientId, receivableNumber, invoiceCode, dueDate, amount, description,
      status, paymentFrequency, installmentNumber, totalInstallments
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  let insertedCount = 0;
  let errors = [];

  receivables.forEach((receivable, index) => {
    // توليد كود الفاتورة الفريد
    const invoiceCode = generateInvoiceCode(contractNumber, receivable.installmentNumber);

    const values = [
      receivable.contractId,
      receivable.clientId,
      receivable.receivableNumber,
      invoiceCode,
      receivable.dueDate,
      receivable.amount,
      receivable.description,
      receivable.status,
      receivable.paymentFrequency,
      receivable.installmentNumber,
      receivable.totalInstallments
    ];

    db.run(insertSQL, values, function(err) {
      if (err) {
        console.error(`Error inserting receivable ${index + 1}:`, err);
        errors.push(err);
      } else {
        insertedCount++;
      }

      // Check if all receivables have been processed
      if (insertedCount + errors.length === receivables.length) {
        console.log(`✅ Generated ${insertedCount} receivables for contract ${contractNumber}`);
        if (errors.length > 0) {
          console.error(`❌ ${errors.length} errors occurred`);
        }
        callback(errors.length > 0 ? errors[0] : null, insertedCount);
      }
    });
  });
}

// Enhanced receivables generation with product-based smart merging
function generateEnhancedReceivables(contract, products, callback) {
  console.log('🔄 Generating enhanced receivables with smart merging');

  const {
    id: contractId,
    clientId,
    contractNumber,
    startDate
  } = contract;

  const receivablesMap = new Map();
  let receivableCounter = 1;

  products.forEach(product => {
    const productStartDate = new Date(product.activationDate || startDate);
    const productEndDate = new Date(product.endDate || startDate);

    // Calculate product values
    const baseAmount = product.area * product.meterPrice;
    const taxAmount = product.taxInfo ? (baseAmount * product.taxRate) / 100 : 0;
    const totalProductValue = baseAmount + taxAmount;

    // Determine billing frequency
    let installmentsPerYear = 12;
    let monthsInterval = 1;

    switch (product.billingType) {
      case 'ربع سنوي':
        installmentsPerYear = 4;
        monthsInterval = 3;
        break;
      case 'نصف سنوي':
        installmentsPerYear = 2;
        monthsInterval = 6;
        break;
      case 'سنوي':
        installmentsPerYear = 1;
        monthsInterval = 12;
        break;
      case 'شهري':
      default:
        installmentsPerYear = 12;
        monthsInterval = 1;
        break;
    }

    // Calculate duration in years
    const durationYears = product.accountingDuration ||
      Math.ceil((productEndDate - productStartDate) / (1000 * 60 * 60 * 24 * 365));

    // Calculate yearly amounts with annual increases
    const yearlyAmounts = [];
    const baseYearValue = product.area * product.meterPrice;

    for (let year = 0; year < durationYears; year++) {
      let yearValue = baseYearValue;

      // Apply annual increase if enabled
      if (product.hasAnnualIncrease && product.increaseValue > 0) {
        const increaseStartYear = product.increaseStartYear || 2;
        if (year + 1 >= increaseStartYear) {
          const yearsWithIncrease = year + 1 - increaseStartYear + 1;
          if (product.increaseType === 'نسبة مئوية') {
            yearValue = baseYearValue * Math.pow(1 + (product.increaseValue / 100), yearsWithIncrease);
          } else {
            yearValue = baseYearValue + (product.increaseValue * yearsWithIncrease);
          }
        }
      }

      // Add tax if applicable
      if (product.taxInfo && product.taxRate > 0) {
        yearValue += (yearValue * product.taxRate) / 100;
      }

      yearlyAmounts.push(yearValue);
    }

    console.log(`📊 Product ${product.productLabel}: ${durationYears} years with yearly amounts:`, yearlyAmounts);

    // Generate installments for this product using correct yearly calculation
    let currentDate = new Date(productStartDate);
    let totalInstallmentsGenerated = 0;

    for (let year = 0; year < durationYears; year++) {
      const yearValue = yearlyAmounts[year];
      const installmentAmountForYear = yearValue / installmentsPerYear;

      console.log(`📊 Year ${year + 1}: ${yearValue.toFixed(2)} ÷ ${installmentsPerYear} = ${installmentAmountForYear.toFixed(2)} per installment`);

      // Generate installments for this year
      for (let installmentInYear = 0; installmentInYear < installmentsPerYear; installmentInYear++) {
        if (currentDate > productEndDate) break;

        const dateKey = currentDate.toISOString().split('T')[0];

        if (receivablesMap.has(dateKey)) {
          // Merge with existing receivable on same date
          const existing = receivablesMap.get(dateKey);
          existing.amount += installmentAmountForYear;
          existing.description += ` + ${product.productLabel}`;
          existing.products.push(product.productLabel);
        } else {
          // Create new receivable
          receivablesMap.set(dateKey, {
            contractId,
            clientId,
            receivableNumber: `${contractNumber}-${String(receivableCounter).padStart(3, '0')}`,
            dueDate: dateKey,
            amount: installmentAmountForYear,
            description: `استحقاق ${product.billingType} - ${product.productLabel} (السنة ${year + 1})`,
            status: 'لم يحن موعده',
            paymentFrequency: product.billingType,
            installmentNumber: receivableCounter,
            products: [product.productLabel]
          });
          receivableCounter++;
        }

        // Move to next installment date
        currentDate.setMonth(currentDate.getMonth() + monthsInterval);
        totalInstallmentsGenerated++;
      }
    }

    console.log(`📊 Generated ${totalInstallmentsGenerated} installments for ${product.productLabel}`);
  });

  // Convert map to sorted array
  const receivables = Array.from(receivablesMap.values())
    .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))
    .map((receivable, index) => ({
      ...receivable,
      installmentNumber: index + 1,
      totalInstallments: receivablesMap.size,
      description: receivable.products.length > 1
        ? `استحقاق مدمج - ${receivable.products.join(' + ')}`
        : receivable.description
    }));

  console.log(`📊 Generated ${receivables.length} merged receivables (${receivablesMap.size} unique dates)`);

  // Insert receivables into database
  const insertSQL = `
    INSERT INTO ContractReceivables (
      contractId, clientId, receivableNumber, invoiceCode, dueDate, amount, description,
      status, paymentFrequency, installmentNumber, totalInstallments
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  let insertedCount = 0;
  const totalReceivables = receivables.length;

  if (totalReceivables === 0) {
    return callback(null, 0);
  }

  receivables.forEach(receivable => {
    // توليد كود الفاتورة الفريد
    const invoiceCode = generateInvoiceCode(contractNumber, receivable.installmentNumber);

    db.run(insertSQL, [
      receivable.contractId,
      receivable.clientId,
      receivable.receivableNumber,
      invoiceCode,
      receivable.dueDate,
      Math.round(receivable.amount * 100) / 100, // Round to 2 decimals
      receivable.description,
      receivable.status,
      receivable.paymentFrequency,
      receivable.installmentNumber,
      receivable.totalInstallments
    ], function(err) {
      if (err) {
        console.error('Error inserting enhanced receivable:', err);
        return callback(err);
      }

      insertedCount++;
      if (insertedCount === totalReceivables) {
        console.log(`✅ Successfully inserted ${totalReceivables} enhanced receivables`);
        callback(null, totalReceivables);
      }
    });
  });
}

// Helper function to update receivable statuses based on current date and payments
function updateReceivableStatuses() {
  const today = new Date().toISOString().split('T')[0];

  // First, update receivables to 'مدفوع' if they have corresponding paid installments
  db.run(`
    UPDATE ContractReceivables
    SET status = 'مدفوع', updatedAt = CURRENT_TIMESTAMP
    WHERE id IN (
      SELECT r.id
      FROM ContractReceivables r
      INNER JOIN ContractInstallments ci ON r.contractId = ci.contractId
        AND r.installmentNumber = ci.installmentNumber
      WHERE ci.isPaid = 1 AND r.status != 'مدفوع' AND r.isActive = 1
    )
  `, [], function(err) {
    if (err) console.error('Error updating paid receivables:', err);
    else if (this.changes > 0) console.log(`Updated ${this.changes} paid receivables`);
  });

  // Update overdue receivables (not paid and past due date)
  db.run(`
    UPDATE ContractReceivables
    SET status = 'متأخر', updatedAt = CURRENT_TIMESTAMP
    WHERE dueDate < ? AND status NOT IN ('مدفوع', 'متأخر') AND isActive = 1
    AND id NOT IN (
      SELECT r.id
      FROM ContractReceivables r
      INNER JOIN ContractInstallments ci ON r.contractId = ci.contractId
        AND r.installmentNumber = ci.installmentNumber
      WHERE ci.isPaid = 1
    )
  `, [today], function(err) {
    if (err) console.error('Error updating overdue receivables:', err);
    else if (this.changes > 0) console.log(`Updated ${this.changes} overdue receivables`);
  });

  // Update due receivables (due today and not paid)
  db.run(`
    UPDATE ContractReceivables
    SET status = 'مستحق', updatedAt = CURRENT_TIMESTAMP
    WHERE dueDate <= ? AND status = 'لم يحن موعده' AND isActive = 1
    AND id NOT IN (
      SELECT r.id
      FROM ContractReceivables r
      INNER JOIN ContractInstallments ci ON r.contractId = ci.contractId
        AND r.installmentNumber = ci.installmentNumber
      WHERE ci.isPaid = 1
    )
  `, [today], function(err) {
    if (err) console.error('Error updating due receivables:', err);
    else if (this.changes > 0) console.log(`Updated ${this.changes} due receivables`);
  });
}

// Helper function to update receivable status when payment is made
function updateReceivableStatus(receivableId, paymentAmount, callback) {
  // Get current receivable info
  db.get(`
    SELECT r.*,
           COALESCE(
             (SELECT SUM(amount) FROM CashReceipts WHERE receivableId = r.id AND isActive = 1), 0
           ) + COALESCE(
             (SELECT SUM(amount) FROM ChequeReceipts WHERE receivableId = r.id AND isActive = 1), 0
           ) + COALESCE(
             (SELECT SUM(amount) FROM BankPayments WHERE receivableId = r.id AND isActive = 1), 0
           ) as totalPaid
    FROM ContractReceivables r
    WHERE r.id = ? AND r.isActive = 1
  `, [receivableId], (err, receivable) => {
    if (err) {
      console.error('Error getting receivable:', err);
      return callback(err);
    }

    if (!receivable) {
      console.log('Receivable not found:', receivableId);
      return callback(new Error('Receivable not found'));
    }

    const newTotalPaid = receivable.totalPaid + paymentAmount;
    let newStatus = receivable.status;

    // تحديد الحالة الجديدة
    if (newTotalPaid >= receivable.amount) {
      newStatus = 'مدفوع';
    } else if (receivable.dueDate < new Date().toISOString().split('T')[0]) {
      newStatus = 'متأخر';
    } else if (receivable.dueDate <= new Date().toISOString().split('T')[0]) {
      newStatus = 'مستحق';
    } else {
      newStatus = 'لم يحن موعده';
    }

    // تحديث حالة الاستحقاق
    db.run(`
      UPDATE ContractReceivables
      SET status = ?, updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [newStatus, receivableId], function(updateErr) {
      if (updateErr) {
        console.error('Error updating receivable status:', updateErr);
        return callback(updateErr);
      }

      console.log(`Receivable ${receivableId} status updated to: ${newStatus}`);
      callback(null);
    });
  });
}

// Helper function to update contract statuses based on end dates
function updateContractStatuses() {
  const today = new Date().toISOString().split('T')[0];

  console.log('🔄 Checking for expired contracts...');

  // Update contracts to 'منتهي' if their end date has passed and they are currently 'نشط'
  db.run(`
    UPDATE Contracts
    SET contractStatus = 'منتهي', updatedAt = CURRENT_TIMESTAMP
    WHERE endDate < ?
      AND contractStatus = 'نشط'
      AND isActive = 1
  `, [today], function(err) {
    if (err) {
      console.error('Error updating expired contracts:', err);
    } else if (this.changes > 0) {
      console.log(`✅ Updated ${this.changes} contracts to 'منتهي' status`);
    }
  });
}

// Helper function to generate unique invoice code for receivables
function generateInvoiceCode(contractNumber, installmentNumber) {
  // تنسيق: رقم_العقد-INV-رقم_القسط
  // مثال: C001-INV-001, C001-INV-002
  const paddedInstallment = installmentNumber.toString().padStart(3, '0');
  return `${contractNumber}-INV-${paddedInstallment}`;
}

// Initialize database with proper error handling
async function initializeDatabase() {
  try {
    await connectToDatabase();

    // Create tables with transaction
    await new Promise((resolve, reject) => {
      db.serialize(() => {
  // Reference Data table (updated for new structure)
  db.run(`
    CREATE TABLE IF NOT EXISTS ReferenceData (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      module TEXT NOT NULL,
      listName TEXT NOT NULL,
      itemValue TEXT NOT NULL,
      itemLabel TEXT NOT NULL,
      sortOrder INTEGER DEFAULT 0,
      isActive INTEGER DEFAULT 1,
      isDeleted INTEGER DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating ReferenceData table:', err);
    } else {
      console.log('ReferenceData table created successfully');

      // Add columns if they don't exist (for existing databases)
      db.run(`ALTER TABLE ReferenceData ADD COLUMN module TEXT`, () => {});
      db.run(`ALTER TABLE ReferenceData ADD COLUMN listName TEXT`, () => {});
      db.run(`ALTER TABLE ReferenceData ADD COLUMN itemValue TEXT`, () => {});
      db.run(`ALTER TABLE ReferenceData ADD COLUMN itemLabel TEXT`, () => {});
      db.run(`ALTER TABLE ReferenceData ADD COLUMN isDeleted INTEGER DEFAULT 0`, () => {});

      // Insert default reference data
      const defaultReferenceData = {
        banks: [
          { itemValue: 'NBE', itemLabel: 'البنك الأهلي المصري', sortOrder: 1 },
          { itemValue: 'CIB', itemLabel: 'البنك التجاري الدولي', sortOrder: 2 },
          { itemValue: 'QNB', itemLabel: 'بنك قطر الوطني الأهلي', sortOrder: 3 },
          { itemValue: 'AAIB', itemLabel: 'البنك العربي الأفريقي الدولي', sortOrder: 4 },
          { itemValue: 'ADIB', itemLabel: 'بنك أبوظبي الإسلامي', sortOrder: 5 },
          { itemValue: 'ALEXBANK', itemLabel: 'بنك الإسكندرية', sortOrder: 6 },
          { itemValue: 'CAE', itemLabel: 'بنك كريدي أجريكول', sortOrder: 7 },
          { itemValue: 'MISR', itemLabel: 'بنك مصر', sortOrder: 8 }
        ],
        governorates: [
          { itemValue: 'cairo', itemLabel: 'القاهرة', sortOrder: 1 },
          { itemValue: 'giza', itemLabel: 'الجيزة', sortOrder: 2 },
          { itemValue: 'alexandria', itemLabel: 'الإسكندرية', sortOrder: 3 },
          { itemValue: 'qalyubia', itemLabel: 'القليوبية', sortOrder: 4 },
          { itemValue: 'port_said', itemLabel: 'بورسعيد', sortOrder: 5 },
          { itemValue: 'suez', itemLabel: 'السويس', sortOrder: 6 },
          { itemValue: 'dakahlia', itemLabel: 'الدقهلية', sortOrder: 7 },
          { itemValue: 'sharqia', itemLabel: 'الشرقية', sortOrder: 8 },
          { itemValue: 'gharbia', itemLabel: 'الغربية', sortOrder: 9 },
          { itemValue: 'kafr_el_sheikh', itemLabel: 'كفر الشيخ', sortOrder: 10 }
        ],
        regions: [
          { itemValue: 'nasr_city', itemLabel: 'مدينة نصر', sortOrder: 1 },
          { itemValue: 'heliopolis', itemLabel: 'مصر الجديدة', sortOrder: 2 },
          { itemValue: 'maadi', itemLabel: 'المعادي', sortOrder: 3 },
          { itemValue: 'zamalek', itemLabel: 'الزمالك', sortOrder: 4 },
          { itemValue: 'downtown', itemLabel: 'وسط البلد', sortOrder: 5 },
          { itemValue: 'new_cairo', itemLabel: 'القاهرة الجديدة', sortOrder: 6 },
          { itemValue: 'sixth_october', itemLabel: 'السادس من أكتوبر', sortOrder: 7 },
          { itemValue: 'sheikh_zayed', itemLabel: 'الشيخ زايد', sortOrder: 8 }
        ],
        owners: [
          { itemValue: 'company', itemLabel: 'الشركة', sortOrder: 1 },
          { itemValue: 'partner1', itemLabel: 'الشريك الأول', sortOrder: 2 },
          { itemValue: 'partner2', itemLabel: 'الشريك الثاني', sortOrder: 3 },
          { itemValue: 'external', itemLabel: 'مالك خارجي', sortOrder: 4 }
        ],
        departments: [
          { itemValue: 'rentals', itemLabel: 'قسم الإيجارات', sortOrder: 1 },
          { itemValue: 'sales', itemLabel: 'قسم المبيعات', sortOrder: 2 },
          { itemValue: 'consulting', itemLabel: 'قسم الاستشارات', sortOrder: 3 },
          { itemValue: 'maintenance', itemLabel: 'قسم الصيانة', sortOrder: 4 },
          { itemValue: 'finance', itemLabel: 'القسم المالي', sortOrder: 5 }
        ]
      };

      // Check if banks already exist
      db.get("SELECT COUNT(*) as count FROM ReferenceData WHERE module = 'cheques' AND listName = 'banks'", (err, row) => {
        if (!err && row.count === 0) {
          // Insert default banks
          const stmt = db.prepare(`
            INSERT INTO ReferenceData (module, listName, itemValue, itemLabel, sortOrder, isActive, isDeleted, createdAt, updatedAt)
            VALUES ('cheques', 'banks', ?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `);

          defaultReferenceData.banks.forEach(bank => {
            stmt.run([bank.itemValue, bank.itemLabel, bank.sortOrder]);
          });

          stmt.finalize();
          console.log('✅ Default banks inserted successfully');
        }
      });

      // Insert other reference data
      Object.entries(defaultReferenceData).forEach(([listName, items]) => {
        if (listName !== 'banks') { // Banks already handled above
          db.get("SELECT COUNT(*) as count FROM ReferenceData WHERE module = 'general' AND listName = ?", [listName], (err, row) => {
            if (!err && row.count === 0) {
              console.log(`Inserting default ${listName}...`);
              const stmt = db.prepare(`
                INSERT INTO ReferenceData (module, listName, itemValue, itemLabel, sortOrder, isActive, isDeleted, createdAt, updatedAt)
                VALUES ('general', ?, ?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
              `);

              items.forEach(item => {
                stmt.run([listName, item.itemValue, item.itemLabel, item.sortOrder]);
              });

              stmt.finalize();
              console.log(`✅ Default ${listName} inserted successfully`);
            }
          });
        }
      });


    }
  });

  // Reference Lists Configuration table for managing which lists link to which pages
  db.run(`
    CREATE TABLE IF NOT EXISTS ReferenceListsConfig (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      listName TEXT NOT NULL UNIQUE,
      displayName TEXT NOT NULL,
      description TEXT,
      linkedPages TEXT, -- JSON array of page names that use this list
      fieldMapping TEXT, -- JSON object mapping page fields to this list
      isRequired BOOLEAN DEFAULT FALSE,
      isActive BOOLEAN DEFAULT TRUE,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating ReferenceListsConfig table:', err);
    } else {
      console.log('ReferenceListsConfig table created successfully');

      // Insert default configurations
      const defaultConfigs = [
        {
          listName: 'banks',
          displayName: 'البنوك',
          description: 'قائمة البنوك المستخدمة في النظام',
          linkedPages: JSON.stringify(['cheques', 'payments']),
          fieldMapping: JSON.stringify({
            'cheques': 'bankName',
            'payments': 'bankName'
          }),
          isRequired: true
        },
        {
          listName: 'governorates',
          displayName: 'المحافظات',
          description: 'قائمة محافظات مصر',
          linkedPages: JSON.stringify(['contracts', 'clients']),
          fieldMapping: JSON.stringify({
            'contracts': 'governorate',
            'clients': 'governorate'
          }),
          isRequired: false
        },
        {
          listName: 'regions',
          displayName: 'المناطق',
          description: 'قائمة المناطق والأحياء',
          linkedPages: JSON.stringify(['contracts', 'clients']),
          fieldMapping: JSON.stringify({
            'contracts': 'region',
            'clients': 'region'
          }),
          isRequired: false
        },
        {
          listName: 'owners',
          displayName: 'مالكي الأصول',
          description: 'قائمة مالكي الأصول محل العقود',
          linkedPages: JSON.stringify(['contracts']),
          fieldMapping: JSON.stringify({
            'contracts': 'assetOwner'
          }),
          isRequired: true
        },
        {
          listName: 'departments',
          displayName: 'الأقسام المسؤولة',
          description: 'قائمة الأقسام المسؤولة عن العقود',
          linkedPages: JSON.stringify(['contracts']),
          fieldMapping: JSON.stringify({
            'contracts': 'responsibleDepartment'
          }),
          isRequired: false
        }
      ];

      // Check and insert default configs
      defaultConfigs.forEach(config => {
        db.get('SELECT COUNT(*) as count FROM ReferenceListsConfig WHERE listName = ?', [config.listName], (err, row) => {
          if (!err && row.count === 0) {
            db.run(`
              INSERT INTO ReferenceListsConfig (listName, displayName, description, linkedPages, fieldMapping, isRequired, isActive)
              VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [config.listName, config.displayName, config.description, config.linkedPages, config.fieldMapping, config.isRequired, true], (err) => {
              if (err) {
                console.error(`Error inserting config for ${config.listName}:`, err);
              } else {
                console.log(`✅ Default config for ${config.listName} inserted successfully`);
              }
            });
          }
        });
      });
    }
  });

  // Settings table
  db.run(`
    CREATE TABLE IF NOT EXISTS Settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      companyName TEXT,
      programName TEXT,
      companyRegNo TEXT,
      taxId TEXT,
      about TEXT,
      companyLogo TEXT,
      language TEXT DEFAULT 'ar',
      currency TEXT,
      currencySymbol TEXT,
      country TEXT,
      dateFormat TEXT,
      calendarType TEXT DEFAULT 'gregorian',
      decimalPlaces TEXT,
      numberingFormat TEXT,
      notificationEmail TEXT,
      regions TEXT,
      owners TEXT,
      governorates TEXT,
      workDays TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Add new columns if they don't exist (for existing databases)
  db.run("ALTER TABLE Settings ADD COLUMN language TEXT DEFAULT 'ar'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN currencySymbol TEXT", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN calendarType TEXT DEFAULT 'gregorian'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN timeFormat TEXT DEFAULT '24'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN numberSeparator TEXT DEFAULT ','", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN contractNumberFormat TEXT DEFAULT 'C-{YYYY}-{####}'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN clientNumberFormat TEXT DEFAULT 'CL-{####}'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN paymentNumberFormat TEXT DEFAULT 'P-{YYYY}-{####}'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableEmailNotifications INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableSMSNotifications INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN theme TEXT DEFAULT 'system'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN primaryColor TEXT DEFAULT '#3b82f6'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN fontSize TEXT DEFAULT 'medium'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN defaultContractDuration TEXT DEFAULT '12'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN defaultPaymentTerms TEXT DEFAULT 'شهري'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN defaultTaxRate TEXT DEFAULT '15'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableMultiCurrency INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableAdvancedReports INTEGER DEFAULT 1", () => {});

  // Additional company fields
  db.run("ALTER TABLE Settings ADD COLUMN companyAddress TEXT", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN companyPhone TEXT", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN companyEmail TEXT", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN companyWebsite TEXT", () => {});

  // Additional localization fields
  db.run("ALTER TABLE Settings ADD COLUMN timeZone TEXT DEFAULT 'Asia/Riyadh'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN fiscalYearStart TEXT DEFAULT '01/01'", () => {});

  // Additional numbering fields
  db.run("ALTER TABLE Settings ADD COLUMN invoiceNumberFormat TEXT DEFAULT 'INV-{YYYY}-{####}'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN receiptNumberFormat TEXT DEFAULT 'REC-{YYYY}-{####}'", () => {});

  // Additional notification fields
  db.run("ALTER TABLE Settings ADD COLUMN enablePushNotifications INTEGER DEFAULT 1", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN notificationFrequency TEXT DEFAULT 'daily'", () => {});

  // Additional theme fields
  db.run("ALTER TABLE Settings ADD COLUMN fontFamily TEXT DEFAULT 'Cairo'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN sidebarCollapsed INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN showAnimations INTEGER DEFAULT 1", () => {});

  // Additional business fields
  db.run("ALTER TABLE Settings ADD COLUMN autoBackup INTEGER DEFAULT 1", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN backupFrequency TEXT DEFAULT 'daily'", () => {});

  // Security fields
  db.run("ALTER TABLE Settings ADD COLUMN sessionTimeout TEXT DEFAULT '30'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableTwoFactor INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN passwordPolicy TEXT DEFAULT 'medium'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableAuditLog INTEGER DEFAULT 1", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN maxLoginAttempts TEXT DEFAULT '5'", () => {});

  // System fields
  db.run("ALTER TABLE Settings ADD COLUMN enableCaching INTEGER DEFAULT 1", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN cacheTimeout TEXT DEFAULT '60'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableCompression INTEGER DEFAULT 1", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN maxFileSize TEXT DEFAULT '10'", () => {});
  db.run("ALTER TABLE Settings ADD COLUMN enableDebugMode INTEGER DEFAULT 0", () => {});

  // Note: ReferenceItems and ReferenceLists tables removed as they were duplicates of ReferenceData

  // Clients table (updated with new fields - no CHECK constraints)
  db.run(`
    CREATE TABLE IF NOT EXISTS Clients (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      clientId TEXT UNIQUE NOT NULL,
      clientType TEXT NOT NULL,
      clientName TEXT NOT NULL,
      clientAddress TEXT,
      clientPhoneWhatsapp TEXT NOT NULL,
      clientPhone2 TEXT,
      clientPhone3 TEXT,
      clientEmail TEXT,
      clientNotes TEXT,
      clientFinancialGuarantee TEXT,
      clientID_Image TEXT,
      clientFinancial_Category TEXT,
      clientLegal_Rep TEXT,
      clientPartner TEXT,
      clientReg_Number TEXT,
      clientTaxReg_Number TEXT,
      clientLegal_Status TEXT,
      clientRemarks TEXT,
      clientDocuments TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('Error creating Clients table:', err);
    } else {
      console.log('✅ Clients table ready');

  // Add clientDocuments column if it doesn't exist
  db.run(`ALTER TABLE Clients ADD COLUMN clientDocuments TEXT`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Error adding clientDocuments column:', err);
    } else {
      console.log('✅ clientDocuments column ready');
    }
  });

  // Fix existing data: convert "فرد" to "أفراد" and "شركة" to "شركات"
  db.run(`UPDATE Clients SET clientType = 'أفراد' WHERE clientType = 'فرد'`, (err) => {
    if (err) {
      console.error('Error updating clientType from فرد to أفراد:', err);
    } else {
      console.log('✅ Updated clientType from فرد to أفراد');
    }
  });

  db.run(`UPDATE Clients SET clientType = 'شركات' WHERE clientType = 'شركة'`, (err) => {
    if (err) {
      console.error('Error updating clientType from شركة to شركات:', err);
    } else {
      console.log('✅ Updated clientType from شركة to شركات');
    }
  });

  // Create new clean Contracts table
  db.run(`
    CREATE TABLE IF NOT EXISTS NewContracts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractNumber TEXT UNIQUE NOT NULL,
      contractSubject TEXT,
      contractDescription TEXT,
      clientId INTEGER NOT NULL,
      contractType TEXT DEFAULT 'عقد إيجار',
      contractStatus TEXT DEFAULT 'نشط' CHECK (contractStatus IN ('نشط', 'غير نشط', 'منتهي', 'ملغي', 'معلق')),

      -- Contract Dates
      contractSigningDate DATE,
      startDate DATE NOT NULL,
      endDate DATE,
      contractDurationYears INTEGER NOT NULL,

      -- Financial Information
      totalContractValue DECIMAL(15,2) NOT NULL,
      monthlyAmount DECIMAL(15,2) NOT NULL,
      paymentDay INTEGER DEFAULT 1,
      paymentFrequency TEXT DEFAULT 'شهري',
      firstInstallmentDate DATE,
      paymentMethod TEXT DEFAULT 'تحويل بنكي',

      -- Insurance and Advance Payment
      finalInsuranceRate DECIMAL(5,2) DEFAULT 0,
      finalInsuranceAmount DECIMAL(15,2) DEFAULT 0,
      advancePaymentMonths INTEGER DEFAULT 0,
      advancePaymentAmount DECIMAL(15,2) DEFAULT 0,

      -- Contract Management
      responsibleDepartment TEXT,
      region TEXT,
      zone TEXT,
      assetOwner TEXT,
      financialGuarantorId INTEGER,

      -- Check and Payment Status
      checkStatus TEXT DEFAULT 'لم يتقدم بالشيكات',

      -- Penalties and Fees
      lateFeeType TEXT DEFAULT 'نسبة مئوية',
      lateFeeValue DECIMAL(10,2) DEFAULT 0,
      gracePeriodDays INTEGER DEFAULT 5,
      bouncedCheckFeeType TEXT DEFAULT 'مبلغ ثابت',
      bouncedCheckFeeValue DECIMAL(10,2) DEFAULT 0,

      -- Contract Structure
      numberOfProducts INTEGER DEFAULT 1,
      hasUnifiedActivationDate INTEGER DEFAULT 1,

      -- Notes and Additional Info
      notes TEXT,
      contractNotes TEXT,
      importantNotes TEXT,
      additionalFees TEXT,

      -- System Fields
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,

      -- Foreign Keys
      FOREIGN KEY (clientId) REFERENCES Clients(id),
      FOREIGN KEY (financialGuarantorId) REFERENCES Clients(id)
    )
  `, (err) => {
    if (err) {
      console.error('Error creating NewContracts table:', err);
    } else {
      console.log('✅ NewContracts table created successfully');
    }
  });
    }
  });

  // Add new columns to existing Clients table if they don't exist
  db.run("ALTER TABLE Clients ADD COLUMN clientAddress TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientPhoneWhatsapp TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientFinancialGuarantee TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientID_Image TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientFinancial_Category TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientLegal_Rep TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientPartner TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientReg_Number TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientTaxReg_Number TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientLegal_Status TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientRemarks TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientDocuments TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientPhone2 TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientPhone3 TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientEmail TEXT", () => {});
  db.run("ALTER TABLE Clients ADD COLUMN clientNotes TEXT", () => {});

  // Add missing columns to Contracts table
  db.run("ALTER TABLE Contracts ADD COLUMN responsibleDepartment TEXT", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN region TEXT", () => {});

  // Contracts table (Enhanced for advanced contract management)
  db.run(`
    CREATE TABLE IF NOT EXISTS Contracts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractNumber TEXT UNIQUE NOT NULL,
      contractInternalId TEXT UNIQUE,
      contractDescription TEXT,
      contractSubject TEXT,
      clientId INTEGER NOT NULL,
      contractType TEXT NOT NULL,
      contractStatus TEXT DEFAULT 'نشط' CHECK (contractStatus IN ('نشط', 'غير نشط', 'منتهي', 'ملغي', 'معلق')),
      contractDate DATE,
      startDate DATE NOT NULL,
      actualStartDate DATE,
      endDate DATE,
      actualEndDate DATE,
      contractDurationYears INTEGER NOT NULL,
      assetOwner TEXT,
      responsibleDepartment TEXT,
      region TEXT,
      financialGuarantorId INTEGER,
      parentContractId INTEGER,
      numberOfProducts INTEGER DEFAULT 1,
      hasUnifiedActivationDate INTEGER DEFAULT 1,
      totalContractValue DECIMAL(15,2) NOT NULL,
      monthlyAmount DECIMAL(15,2) NOT NULL,
      paymentDay INTEGER NOT NULL,
      paymentFrequency TEXT DEFAULT 'شهري' CHECK (paymentFrequency IN ('شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي')),
      firstInstallmentDate DATE,
      paymentMethod TEXT DEFAULT 'تحويل بنكي' CHECK (paymentMethod IN ('تحويل بنكي', 'شيك', 'نقدي')),
      annualIncreaseType TEXT DEFAULT 'لا يوجد' CHECK (annualIncreaseType IN ('لا يوجد', 'مبلغ ثابت', 'نسبة مئوية', 'نسبة مركبة')),
      annualIncreaseValue DECIMAL(10,2) DEFAULT 0,
      annualIncreaseStartYear INTEGER DEFAULT 2,
      lateFeeType TEXT DEFAULT 'نسبة مئوية' CHECK (lateFeeType IN ('مبلغ ثابت', 'نسبة مئوية')),
      lateFeeValue DECIMAL(10,2) DEFAULT 0,
      gracePeriodDays INTEGER DEFAULT 0,
      bouncedCheckFeeType TEXT DEFAULT 'مبلغ ثابت' CHECK (bouncedCheckFeeType IN ('مبلغ ثابت', 'نسبة مئوية')),
      bouncedCheckFeeValue DECIMAL(10,2) DEFAULT 0,
      additionalFees TEXT,
      earlyTerminationReason TEXT,
      earlyTerminationDate DATE,
      importantNotes TEXT,
      systemFlags TEXT,
      autoTerminationSuggested INTEGER DEFAULT 0,
      consecutiveMissedPayments INTEGER DEFAULT 0,
      totalMissedPayments INTEGER DEFAULT 0,
      notes TEXT,
      contractNotes TEXT, -- Legacy field for backward compatibility
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (clientId) REFERENCES Clients(id),
      FOREIGN KEY (financialGuarantorId) REFERENCES Clients(id),
      FOREIGN KEY (parentContractId) REFERENCES Contracts(id)
    )
  `);

  // Contract Products table (for multi-product contracts)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractProducts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      productLabel TEXT NOT NULL,
      area DECIMAL(10,2) NOT NULL,
      meterPrice DECIMAL(10,2) NOT NULL,
      activationDate DATE,
      endDate DATE,
      billingType TEXT DEFAULT 'شهري' CHECK (billingType IN ('شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي')),
      taxInfo INTEGER DEFAULT 0,
      taxRate DECIMAL(5,2) DEFAULT 0,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Contract Partners table (for multiple partners/alliances)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractPartners (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      partnerId INTEGER NOT NULL,
      partnerType TEXT DEFAULT 'شريك' CHECK (partnerType IN ('شريك', 'تحالف')),
      partnershipPercentage DECIMAL(5,2) DEFAULT 0,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (partnerId) REFERENCES Clients(id)
    )
  `);

  // Contract Attachments table
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractAttachments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      fileName TEXT NOT NULL,
      fileType TEXT NOT NULL,
      fileSize INTEGER,
      filePath TEXT NOT NULL,
      uploadedBy TEXT,
      description TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Contract Audit Log table
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractAuditLog (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      action TEXT NOT NULL,
      fieldName TEXT,
      oldValue TEXT,
      newValue TEXT,
      userId TEXT,
      userRole TEXT,
      ipAddress TEXT,
      userAgent TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Payments table (legacy - will be deprecated)
  db.run(`
    CREATE TABLE IF NOT EXISTS Payments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      paymentNumber INTEGER NOT NULL,
      dueDate DATE NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      lateFee DECIMAL(10,2) DEFAULT 0,
      bouncedCheckFee DECIMAL(10,2) DEFAULT 0,
      totalAmount DECIMAL(15,2) NOT NULL,
      isPaid INTEGER DEFAULT 0,
      paidDate DATE,
      paymentMethod TEXT CHECK (paymentMethod IN ('نقدي', 'شيك', 'تحويل بنكي', 'بطاقة ائتمان')),
      checkNumber TEXT,
      bankName TEXT,
      notes TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id)
    )
  `);

  // Treasury Payments table
  db.run(`
    CREATE TABLE IF NOT EXISTS TreasuryPayments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      receiptNumber TEXT UNIQUE NOT NULL,
      paymentDate DATE NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      paymentType TEXT CHECK (paymentType IN ('مرتبطة بعقد', 'غير مرتبطة بعقد')) NOT NULL,

      -- للمدفوعات المرتبطة بعقد
      contractId INTEGER,
      receivableId INTEGER,
      contractNumber TEXT,
      contractSubject TEXT,
      clientName TEXT,
      receivableDescription TEXT,
      receivableStartDate DATE,
      receivableEndDate DATE,
      paymentStatus TEXT CHECK (paymentStatus IN ('كامل', 'جزئي')),

      -- للمدفوعات غير المرتبطة بعقد
      nonContractType TEXT CHECK (nonContractType IN ('رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب')),

      -- تفاصيل طريقة الدفع
      paymentMethod TEXT CHECK (paymentMethod IN ('نقدي', 'شيك')) NOT NULL,

      -- للشيكات
      checkNumber TEXT,
      checkDate DATE,
      bankName TEXT,
      checkStatus TEXT CHECK (checkStatus IN ('عهدة', 'تحت التحصيل', 'أمانة')),

      -- حقول إضافية
      notes TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,

      FOREIGN KEY (contractId) REFERENCES Contracts(id),
      FOREIGN KEY (receivableId) REFERENCES ContractInstallments(id)
    )
  `);

  // Bank Payments table
  db.run(`
    CREATE TABLE IF NOT EXISTS BankPayments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      bankTransactionNumber TEXT UNIQUE NOT NULL,
      paymentDate DATE NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      bankName TEXT NOT NULL,
      paymentType TEXT CHECK (paymentType IN ('مرتبطة بعقد', 'غير مرتبطة بعقد')) NOT NULL,

      -- للمدفوعات المرتبطة بعقد
      contractId INTEGER,
      receivableId INTEGER,
      contractNumber TEXT,
      contractSubject TEXT,
      clientName TEXT,
      receivableDescription TEXT,
      receivableStartDate DATE,
      receivableEndDate DATE,
      paymentStatus TEXT CHECK (paymentStatus IN ('كامل', 'جزئي')),

      -- للمدفوعات غير المرتبطة بعقد
      nonContractType TEXT CHECK (nonContractType IN ('رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب')),

      -- حقول إضافية
      notes TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,

      FOREIGN KEY (contractId) REFERENCES Contracts(id),
      FOREIGN KEY (receivableId) REFERENCES ContractInstallments(id)
    )
  `);

  // Cheques table (updated for comprehensive cheque management)
  db.run(`
    CREATE TABLE IF NOT EXISTS Cheques (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      chequeNumber TEXT UNIQUE NOT NULL,
      contractId INTEGER NOT NULL,
      clientId INTEGER NOT NULL,
      receivableId INTEGER,

      -- Basic cheque information
      bankName TEXT NOT NULL,
      chequeAmount DECIMAL(15,2) NOT NULL,
      chequeDate DATE NOT NULL,
      dueDate DATE NOT NULL,

      -- Status and workflow
      status TEXT NOT NULL DEFAULT 'خزينة' CHECK (status IN (
        'خزينة',           -- في الخزينة (مستلم)
        'عهدة',            -- أوراق قبض بالإدارة المالية
        'تحت_التحصيل',     -- مودع بالبنك للتحصيل
        'محصل',           -- تم التحصيل بنجاح
        'مسحوب',          -- مسحوب من البنك
        'مرتد',           -- مرتد من البنك
        'قانونية',        -- محول للإدارة القانونية
        'ملغي'            -- ملغي
      )),

      -- Important dates
      receivedDate DATE NOT NULL,     -- تاريخ الاستلام بالخزينة
      statusDate DATE,                -- تاريخ آخر تغيير حالة

      -- Entry numbers (mandatory for movements)
      entryNumber TEXT,               -- رقم القيد الحالي

      -- Contract status at time of receipt
      contractStatusAtReceipt TEXT,   -- حالة العقد وقت الاستلام
      shouldDepositToBank BOOLEAN DEFAULT FALSE, -- هل يجب إيداعه بالبنك

      -- Additional information for special statuses
      bounceReason TEXT,              -- سبب الارتداد (للشيكات المرتدة)
      withdrawalReason TEXT,          -- سبب السحب (للشيكات المسحوبة)

      -- Legacy fields (for backward compatibility)
      checkNumber TEXT,               -- الاسم القديم
      checkDate DATE,                 -- الاسم القديم
      amount DECIMAL(15,2),           -- الاسم القديم
      checkStatus TEXT,               -- الاسم القديم
      sourceType TEXT,                -- للتوافق مع النظام القديم
      sourcePaymentId INTEGER,        -- للتوافق مع النظام القديم

      -- General information
      notes TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      isActive BOOLEAN DEFAULT TRUE,

      FOREIGN KEY (contractId) REFERENCES Contracts(id),
      FOREIGN KEY (clientId) REFERENCES Clients(id),
      FOREIGN KEY (receivableId) REFERENCES ContractReceivables(id)
    )
  `, (err) => {
    if (err) {
      console.error('Error creating Cheques table:', err);
    } else {
      console.log('✅ Cheques table ready');

      // Add new columns if they don't exist (for existing databases)
      db.run(`ALTER TABLE Cheques ADD COLUMN chequeNumber TEXT`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN clientId INTEGER`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN chequeAmount DECIMAL(15,2)`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN dueDate DATE`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN status TEXT DEFAULT 'خزينة'`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN statusDate DATE`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN entryNumber TEXT`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN contractStatusAtReceipt TEXT`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN shouldDepositToBank BOOLEAN DEFAULT FALSE`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN bounceReason TEXT`, () => {});
      db.run(`ALTER TABLE Cheques ADD COLUMN withdrawalReason TEXT`, () => {});
    }
  });

  // Cash Receipts table (إيصالات استلام نقدية)
  db.run(`
    CREATE TABLE IF NOT EXISTS CashReceipts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      receiptNumber TEXT UNIQUE NOT NULL, -- سيريال منفصل للنقدي
      paymentDate DATE NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      paymentType TEXT CHECK (paymentType IN ('مرتبطة بعقد', 'غير مرتبطة بعقد')) NOT NULL,

      -- للمدفوعات المرتبطة بعقد
      contractId INTEGER,
      receivableId INTEGER,
      contractNumber TEXT,
      contractSubject TEXT,
      clientName TEXT,
      receivableDescription TEXT,
      receivableStartDate DATE,
      receivableEndDate DATE,
      paymentStatus TEXT CHECK (paymentStatus IN ('كامل', 'جزئي')),

      -- للمدفوعات غير المرتبطة بعقد
      nonContractType TEXT CHECK (nonContractType IN ('رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب')),

      -- حقول إضافية
      notes TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,

      FOREIGN KEY (contractId) REFERENCES Contracts(id),
      FOREIGN KEY (receivableId) REFERENCES ContractInstallments(id)
    )
  `);

  // Cheque Receipts table (إيصالات استلام شيكات)
  db.run(`
    CREATE TABLE IF NOT EXISTS ChequeReceipts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      receiptNumber TEXT UNIQUE NOT NULL, -- سيريال منفصل للشيكات
      paymentDate DATE NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      paymentType TEXT CHECK (paymentType IN ('مرتبطة بعقد', 'غير مرتبطة بعقد')) NOT NULL,

      -- للمدفوعات المرتبطة بعقد
      contractId INTEGER,
      receivableId INTEGER,
      contractNumber TEXT,
      contractSubject TEXT,
      clientName TEXT,
      receivableDescription TEXT,
      receivableStartDate DATE,
      receivableEndDate DATE,
      paymentStatus TEXT CHECK (paymentStatus IN ('كامل', 'جزئي')),

      -- للمدفوعات غير المرتبطة بعقد
      nonContractType TEXT CHECK (nonContractType IN ('رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب')),

      -- بيانات الشيك
      checkNumber TEXT NOT NULL,
      checkDate DATE NOT NULL,
      bankName TEXT NOT NULL,
      checkStatus TEXT CHECK (checkStatus IN ('عهدة', 'تحت التحصيل', 'أمانة')) DEFAULT 'عهدة',

      -- حقول إضافية
      notes TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,

      FOREIGN KEY (contractId) REFERENCES Contracts(id),
      FOREIGN KEY (receivableId) REFERENCES ContractInstallments(id)
    )
  `);

  // Add missing columns to Contracts table
  db.run("ALTER TABLE Contracts ADD COLUMN contractSigningDate DATE", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN financialActivationDate DATE", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN ownershipPercentage DECIMAL(5,2) DEFAULT 100", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN responsibleDepartment TEXT", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN irregularPaymentMonths INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN finalInsuranceRate DECIMAL(5,2) DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN finalInsuranceAmount DECIMAL(15,2) DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN advancePaymentMonths INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN advancePaymentAmount DECIMAL(15,2) DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN checkStatus TEXT DEFAULT 'لم يتقدم بالشيكات'", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN zone TEXT", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN region TEXT", () => {});

  // Add missing columns to ContractProducts table
  db.run("ALTER TABLE ContractProducts ADD COLUMN irregularBillingMonths INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN financialAccountingStartDate DATE", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN financialAccountingEndDate DATE", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN accountingDuration INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN hasAnnualIncrease INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN increaseStartYear INTEGER DEFAULT 2", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN increaseType TEXT DEFAULT 'نسبة مئوية'", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN increaseValue DECIMAL(10,2) DEFAULT 0", () => {});

  // Note: ContractFinancialDetails table removed as its fields are already in Contracts table

  // Contract Installments table (enhanced for multi-product support)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractInstallments (
      installmentId INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      productId INTEGER,
      productLabel TEXT,
      installmentNumber INTEGER NOT NULL,
      installmentDate DATE NOT NULL,
      installmentAmount DECIMAL(15,2) NOT NULL,
      baseAmount DECIMAL(15,2) NOT NULL,
      taxAmount DECIMAL(15,2) DEFAULT 0,
      yearOfContract INTEGER NOT NULL,
      paymentDueDate DATE NOT NULL,
      remainingAmount DECIMAL(15,2) NOT NULL,
      isPaid INTEGER DEFAULT 0,
      paidDate DATE,
      paidAmount DECIMAL(15,2) DEFAULT 0,
      penaltyAmount DECIMAL(10,2) DEFAULT 0,
      lateDays INTEGER DEFAULT 0,
      paymentMethod TEXT,
      checkNumber TEXT,
      bankName TEXT,
      notes TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (productId) REFERENCES ContractProducts(id) ON DELETE SET NULL
    )
  `);

  // Contract Alerts table (for smart notifications)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractAlerts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      clientId INTEGER,
      alertType TEXT NOT NULL,
      alertTitle TEXT NOT NULL,
      alertMessage TEXT NOT NULL,
      alertPriority TEXT DEFAULT 'متوسط' CHECK (alertPriority IN ('منخفض', 'متوسط', 'عالي', 'حرج')),
      alertDate DATE NOT NULL,
      actionRequired INTEGER DEFAULT 0,
      actionUrl TEXT,
      isRead INTEGER DEFAULT 0,
      isArchived INTEGER DEFAULT 0,
      isResolved INTEGER DEFAULT 0,
      resolvedDate DATE,
      resolvedBy TEXT,
      autoGenerated INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (clientId) REFERENCES Clients(id) ON DELETE SET NULL
    )
  `);

  // Add missing columns to existing ContractAlerts table if they don't exist
  db.run("ALTER TABLE ContractAlerts ADD COLUMN clientId INTEGER", () => {});
  db.run("ALTER TABLE ContractAlerts ADD COLUMN alertTitle TEXT", () => {});
  db.run("ALTER TABLE ContractAlerts ADD COLUMN alertPriority TEXT DEFAULT 'متوسط'", () => {});
  db.run("ALTER TABLE ContractAlerts ADD COLUMN actionRequired INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE ContractAlerts ADD COLUMN actionUrl TEXT", () => {});
  db.run("ALTER TABLE ContractAlerts ADD COLUMN isArchived INTEGER DEFAULT 0", () => {});

  // Contract Timeline table (for visual timeline)
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractTimeline (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      eventType TEXT NOT NULL CHECK (eventType IN ('توقيع', 'تسليم', 'تعديل', 'إنهاء', 'تجديد', 'دفعة')),
      eventDate DATE NOT NULL,
      eventDescription TEXT NOT NULL,
      eventDetails TEXT,
      userId TEXT,
      isActive INTEGER DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE
    )
  `);

  // Revenue Recognition table (for accounting)
  db.run(`
    CREATE TABLE IF NOT EXISTS RevenueRecognition (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      installmentId INTEGER NOT NULL,
      fiscalYear INTEGER NOT NULL,
      fiscalMonth INTEGER NOT NULL,
      amount DECIMAL(15,2) NOT NULL,
      recognizedDate DATE NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (contractId) REFERENCES Contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (installmentId) REFERENCES ContractInstallments(installmentId) ON DELETE CASCADE
    )
  `);

  // Add new columns to existing Contracts table if they don't exist
  db.run("ALTER TABLE Contracts ADD COLUMN irregularPaymentMonths INTEGER", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN finalInsuranceRate REAL DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN checkStatus TEXT DEFAULT 'لم يتقدم بالشيكات'", () => {});

  // Add invoiceCode column to ContractReceivables if it doesn't exist
  db.run("ALTER TABLE ContractReceivables ADD COLUMN invoiceCode TEXT", () => {});

  // Add missing columns for contract management
  db.run("ALTER TABLE Contracts ADD COLUMN ownershipPercentage REAL DEFAULT 100", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN advancePaymentMonths INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN advancePaymentAmount DECIMAL(15,2) DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN financialActivationDate DATE", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN responsibleDepartment TEXT", () => {});

  // Add missing columns for ContractProducts table
  db.run("ALTER TABLE ContractProducts ADD COLUMN irregularBillingMonths INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN financialAccountingStartDate DATE", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN financialAccountingEndDate DATE", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN accountingDuration INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN hasAnnualIncrease INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN increaseStartYear INTEGER DEFAULT 2", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN increaseType TEXT DEFAULT 'نسبة مئوية'", () => {});
  db.run("ALTER TABLE ContractProducts ADD COLUMN increaseValue DECIMAL(10,2) DEFAULT 0", () => {});

  // Add missing contract signing date
  db.run("ALTER TABLE Contracts ADD COLUMN contractSigningDate DATE", () => {});

  // Remove unused early termination columns
  db.run("ALTER TABLE Contracts DROP COLUMN earlyTerminationReason", () => {});
  db.run("ALTER TABLE Contracts DROP COLUMN earlyTerminationDate", () => {});

  // Add versioning columns
  db.run("ALTER TABLE Contracts ADD COLUMN originalContractNumber TEXT", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN versionNumber INTEGER DEFAULT 1", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN isCurrentVersion INTEGER DEFAULT 1", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN parentVersionId INTEGER", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN editCount INTEGER DEFAULT 0", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN editHistory TEXT", () => {});
  db.run("ALTER TABLE Contracts ADD COLUMN editReason TEXT", () => {});

  // Add missing uploadedAt column to ContractAttachments
  db.run("ALTER TABLE ContractAttachments ADD COLUMN uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP", () => {});

  // Add missing paidAmount column to ContractReceivables
  db.run("ALTER TABLE ContractReceivables ADD COLUMN paidAmount DECIMAL(15,2) DEFAULT 0", () => {});

  // Create Contract Receivables table
  db.run(`
    CREATE TABLE IF NOT EXISTS ContractReceivables (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contractId INTEGER NOT NULL,
      clientId INTEGER NOT NULL,
      receivableNumber TEXT NOT NULL,
      invoiceCode TEXT UNIQUE NOT NULL, -- كود فريد للفاتورة
      dueDate DATE NOT NULL,
      amount REAL NOT NULL,
      description TEXT,
      status TEXT DEFAULT 'مستحق' CHECK (status IN ('مستحق', 'مدفوع', 'متأخر', 'لم يحن موعده')),
      paymentFrequency TEXT NOT NULL,
      installmentNumber INTEGER NOT NULL,
      totalInstallments INTEGER NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      isActive INTEGER DEFAULT 1,
      FOREIGN KEY (contractId) REFERENCES Contracts(id),
      FOREIGN KEY (clientId) REFERENCES Clients(id)
    )
  `, (err) => {
    if (err) console.error('Error creating ContractReceivables table:', err);
    else console.log('✅ ContractReceivables table ready');
  });

  // Update paymentFrequency CHECK constraint to include 'غير منتظم'
  // Note: SQLite doesn't support modifying CHECK constraints, so we'll handle this in application logic

  // Add irregularBillingMonths column to ContractProducts table
  db.run("ALTER TABLE ContractProducts ADD COLUMN irregularBillingMonths INTEGER", () => {});

  // Note: SQLite doesn't support modifying CHECK constraints
  // We'll handle billingType validation in application logic to support 'غير منتظم'

  // Insert sample data if not exists
  db.get("SELECT COUNT(*) as count FROM Settings", (err, row) => {
    if (!err && row.count === 0) {
      db.run(`
        INSERT INTO Settings (
          companyName, programName, companyRegNo, taxId, about,
          currency, country, dateFormat, decimalPlaces, numberingFormat,
          notificationEmail, regions, owners, governorates, workDays
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'Test Company', 'Contract Management', '', '', '',
        'EGP', 'Egypt', 'dd/MM/yyyy', '2', 'serial-year-unit',
        '', '[]', '[]', '[]', '[]'
      ]);
      console.log('Sample data created successfully');
    }
  });

        console.log('✅ Database and tables created successfully');
        resolve();
      });
    });
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

// Settings GET endpoint moved to unified route above



// ===== REFERENCE DATA ENDPOINTS =====

// Get all reference lists
app.get('/api/reference-lists', (req, res) => {
  console.log('GET /api/reference-lists request');

  db.all(`
    SELECT DISTINCT module, listName, COUNT(*) as itemCount
    FROM ReferenceData
    WHERE isActive = 1 AND isDeleted = 0
    GROUP BY module, listName
    ORDER BY module, listName
  `, [], (err, rows) => {
    if (err) {
      console.error('Get reference lists error:', err);
      res.status(500).json({ error: err.message });
    } else {
      console.log('Reference lists retrieved:', rows?.length || 0, 'lists');
      res.json(rows || []);
    }
  });
});

// Get reference data by module and list
app.get('/api/reference-data/:module/:listName', (req, res) => {
  console.log('GET /api/reference-data/:module/:listName request');
  const { module, listName } = req.params;

  db.all(`
    SELECT * FROM ReferenceData
    WHERE module = ? AND listName = ? AND isActive = 1 AND isDeleted = 0
    ORDER BY sortOrder, itemLabel
  `, [module, listName], (err, rows) => {
    if (err) {
      console.error('Get reference data error:', err);
      res.status(500).json({ error: err.message });
    } else {
      console.log('Reference data retrieved:', rows?.length || 0, 'items');
      res.json(rows || []);
    }
  });
});

// Create new reference list
app.post('/api/reference-lists', (req, res) => {
  console.log('POST /api/reference-lists request');
  console.log('Received data:', JSON.stringify(req.body, null, 2));

  const { module, listName, items } = req.body;

  if (!module || !listName || !items || !Array.isArray(items) || items.length === 0) {
    console.log('Validation failed: missing required fields');
    return res.status(400).json({ error: 'Module, listName, and items are required' });
  }

  // Check if list already exists
  db.get(`
    SELECT COUNT(*) as count FROM ReferenceData
    WHERE module = ? AND listName = ? AND isDeleted = 0
  `, [module, listName], (err, row) => {
    if (err) {
      console.error('Check existing list error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (row.count > 0) {
      return res.status(400).json({ error: 'List already exists for this module' });
    }

    // Insert all items
    const stmt = db.prepare(`
      INSERT INTO ReferenceData (module, listName, itemValue, itemLabel, sortOrder, isActive, isDeleted, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);

    let insertedCount = 0;
    const errors = [];

    items.forEach((item, index) => {
      if (item && item.trim()) {
        stmt.run([module, listName, item.trim(), item.trim(), index], function(err) {
          if (err) {
            errors.push(`Error inserting item "${item}": ${err.message}`);
          } else {
            insertedCount++;
          }

          // Check if all items processed
          if (insertedCount + errors.length === items.filter(item => item && item.trim()).length) {
            stmt.finalize();

            if (errors.length > 0) {
              console.error('Some items failed to insert:', errors);
              res.status(500).json({ error: 'Some items failed to insert', details: errors });
            } else {
              console.log(`Reference list created successfully: ${insertedCount} items`);
              res.json({ success: true, itemsCreated: insertedCount });
            }
          }
        });
      }
    });
  });
});

// Update reference list item
app.put('/api/reference-data/:id', (req, res) => {
  console.log('PUT /api/reference-data/:id request');
  const { id } = req.params;
  const { itemValue, itemLabel, sortOrder, isActive } = req.body;

  if (!itemValue || !itemLabel) {
    return res.status(400).json({ error: 'Item value and label are required' });
  }

  db.run(`
    UPDATE ReferenceData SET
      itemValue = ?, itemLabel = ?, sortOrder = ?, isActive = ?, updatedAt = CURRENT_TIMESTAMP
    WHERE id = ? AND isDeleted = 0
  `, [
    itemValue,
    itemLabel,
    sortOrder || 0,
    isActive !== false ? 1 : 0,
    id
  ], function(err) {
    if (err) {
      console.error('Update reference data error:', err);
      res.status(500).json({ error: err.message });
    } else {
      console.log('Reference data updated successfully');
      res.json({ success: true });
    }
  });
});

// Delete reference list (soft delete)
app.delete('/api/reference-lists/:module/:listName', (req, res) => {
  console.log('DELETE /api/reference-lists/:module/:listName request');
  const { module, listName } = req.params;

  db.run(`
    UPDATE ReferenceData SET isDeleted = 1, updatedAt = CURRENT_TIMESTAMP
    WHERE module = ? AND listName = ?
  `, [module, listName], function(err) {
    if (err) {
      console.error('Delete reference list error:', err);
      res.status(500).json({ error: err.message });
    } else {
      console.log('Reference list deleted successfully');
      res.json({ success: true });
    }
  });
});

// Delete reference data item (soft delete)
app.delete('/api/reference-data/:id', (req, res) => {
  console.log('DELETE /api/reference-data/:id request');
  const { id } = req.params;

  db.run(`
    UPDATE ReferenceData SET isDeleted = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?
  `, [id], function(err) {
    if (err) {
      console.error('Delete reference data error:', err);
      res.status(500).json({ error: err.message });
    } else {
      console.log('Reference data deleted successfully');
      res.json({ success: true });
    }
  });
});

// Unified settings endpoint - handles both GET and POST
app.route('/api/settings')
  .get((req, res) => {
    console.log('📖 GET /api/settings request');

    db.get("SELECT * FROM Settings ORDER BY id DESC LIMIT 1", (err, row) => {
      if (err) {
        console.error('❌ Error getting settings:', err);
        res.status(500).json({ error: err.message });
        return;
      }

      if (row) {
        // Convert JSON strings to arrays for frontend
        try {
          row.regions = row.regions ? JSON.parse(row.regions) : [];
          row.owners = row.owners ? JSON.parse(row.owners) : [];
          row.governorates = row.governorates ? JSON.parse(row.governorates) : [];
          row.workDays = row.workDays ? JSON.parse(row.workDays) : [];
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
        }

        console.log('✅ Settings retrieved successfully');
        res.json(row);
      } else {
        console.log('⚠️ No settings found, returning defaults');
        res.json({
          companyName: 'شركة إدارة العقود المصرية',
          programName: 'نظام إدارة العقود المتقدم',
          language: 'ar',
          currency: 'جنيه مصري',
          currencySymbol: 'ج.م',
          country: 'مصر'
        });
      }
    });
  })
  .post((req, res) => {
    console.log('🔥🔥🔥 POST /api/settings request received!');
    console.log('📦 Received data keys:', Object.keys(req.body));
    console.log('📊 Data size:', JSON.stringify(req.body).length, 'characters');

    // Validate settings data
    console.log('🔍 Starting validation...');
    const validation = validateSettingsData(req.body);
    console.log('🔍 Validation result:', validation);

    if (!validation.isValid) {
      console.log('❌ Settings validation failed:', validation.errors);
      return res.status(400).json({
        error: 'بيانات الإعدادات غير صحيحة',
        details: validation.errors
      });
    }

    console.log('✅ Settings validation passed');
    const settingsData = req.body;

    console.log('🔧 Starting settings processing...');
    console.log('📋 Sample data:', {
      companyName: settingsData.companyName,
      language: settingsData.language,
      currency: settingsData.currency
    });

    try {
      console.log('🔄 Entering try block...');

  // Extract core fields that we know work + UI settings + localization
  const coreFields = {
    // Company info
    companyName: settingsData.companyName || 'شركة إدارة العقود',
    programName: settingsData.programName || 'نظام إدارة العقود المتقدم',
    companyRegNo: settingsData.companyRegNo || '',
    taxId: settingsData.taxId || '',
    companyAddress: settingsData.companyAddress || '',
    companyPhone: settingsData.companyPhone || '',
    companyEmail: settingsData.companyEmail || '',
    companyWebsite: settingsData.companyWebsite || '',

    // Localization (المنطقة)
    language: settingsData.language || 'ar',
    country: settingsData.country || 'السعودية',
    currency: settingsData.currency || 'ريال سعودي',
    currencySymbol: settingsData.currencySymbol || 'ر.س',
    timeFormat: settingsData.timeFormat || '24',
    timeZone: settingsData.timeZone || 'Asia/Riyadh',
    fiscalYearStart: settingsData.fiscalYearStart || '01/01',

    // UI settings
    theme: settingsData.theme || 'system',
    fontSize: settingsData.fontSize || 'medium',
    primaryColor: settingsData.primaryColor || '#3b82f6',
    fontFamily: settingsData.fontFamily || 'Cairo',

    // Work days
    workDays: settingsData.workDays || ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس"],

    // Additional company fields
    about: settingsData.about || '',
    dateFormat: settingsData.dateFormat || 'yyyy/mm/dd',

    // Notifications
    notificationEmail: settingsData.notificationEmail || '',
    notificationFrequency: settingsData.notificationFrequency || 'daily',
    enableEmailNotifications: settingsData.enableEmailNotifications || false,
    enableSMSNotifications: settingsData.enableSMSNotifications || false,

    // Business settings
    defaultContractDuration: settingsData.defaultContractDuration || '12',
    defaultPaymentTerms: settingsData.defaultPaymentTerms || 'شهري',
    defaultTaxRate: settingsData.defaultTaxRate || '15',
    backupFrequency: settingsData.backupFrequency || 'daily',
    enableMultiCurrency: settingsData.enableMultiCurrency || false,

    // Security settings
    sessionTimeout: settingsData.sessionTimeout || '30',
    maxLoginAttempts: settingsData.maxLoginAttempts || '5',
    passwordPolicy: settingsData.passwordPolicy || 'medium',
    enableTwoFactor: settingsData.enableTwoFactor || false,
    enableAuditLog: settingsData.enableAuditLog || false,

    // System settings
    cacheTimeout: settingsData.cacheTimeout || '60',
    maxFileSize: settingsData.maxFileSize || '10',
    enableCaching: settingsData.enableCaching || true,
    enableCompression: settingsData.enableCompression || false,
    enableDebugMode: settingsData.enableDebugMode || false
  };

  console.log('💾 Core fields to save:', coreFields);

  // Check if record exists
  db.get("SELECT COUNT(*) as count FROM Settings", (err, row) => {
    if (err) {
      console.error('❌ Error checking table:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (row.count === 0) {
      // Insert new record with core fields only
      console.log('✨ Inserting new record with core fields');
      db.run(`
        INSERT INTO Settings (
          companyName, programName, about, companyRegNo, taxId,
          language, country, currency, currencySymbol, dateFormat,
          notificationEmail, workDays,
          createdAt, updatedAt
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [
        coreFields.companyName,
        coreFields.programName,
        coreFields.about,
        coreFields.companyRegNo,
        coreFields.taxId,
        coreFields.language,
        coreFields.country,
        coreFields.currency,
        coreFields.currencySymbol,
        coreFields.dateFormat,
        coreFields.notificationEmail,
        JSON.stringify(coreFields.workDays)
      ], function(err) {
        if (err) {
          console.error('❌ Insert error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('✅ Settings inserted successfully with ID:', this.lastID);
          res.json({
            success: true,
            message: 'Settings saved successfully',
            id: this.lastID,
            savedData: coreFields
          });
        }
      });
    } else {
      // Update existing record with core fields
      console.log('🔄 Updating existing record with core fields');
      db.run(`
        UPDATE Settings SET
          companyName=?, programName=?, about=?, companyRegNo=?, taxId=?,
          language=?, country=?, currency=?, currencySymbol=?, dateFormat=?,
          notificationEmail=?, workDays=?,
          updatedAt=CURRENT_TIMESTAMP
        WHERE id = (SELECT id FROM Settings ORDER BY id DESC LIMIT 1)
      `, [
        coreFields.companyName,
        coreFields.programName,
        coreFields.about,
        coreFields.companyRegNo,
        coreFields.taxId,
        coreFields.language,
        coreFields.country,
        coreFields.currency,
        coreFields.currencySymbol,
        coreFields.dateFormat,
        coreFields.notificationEmail,
        JSON.stringify(coreFields.workDays)
      ], function(err) {
        if (err) {
          console.error('❌ Update error:', err);
          res.status(500).json({ error: err.message });
        } else {
          console.log('✅ Settings updated successfully');
          res.json({
            success: true,
            message: 'Settings updated successfully',
            savedData: coreFields
          });
        }
      });
    }
  });

    } catch (error) {
      console.error('🔥 CRITICAL ERROR in settings POST:', error);
      console.error('🔥 Error stack:', error.stack);
      res.status(500).json({
        error: 'خطأ في الخادم أثناء حفظ الإعدادات',
        details: error.message
      });
    }
});

// Settings endpoints are now unified above - no duplicate code needed

// Note: ReferenceItems endpoints removed as they were duplicates of ReferenceData endpoints



// Clients API endpoints


// Get all clients
app.get('/api/clients', (req, res) => {
  console.log('GET /api/clients request');

  db.all("SELECT * FROM Clients WHERE isActive = 1 ORDER BY clientName", (err, rows) => {
    if (err) {
      console.error('Error getting clients:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(client => ({
      ...client,
      isActive: Boolean(client.isActive)
    }));

    console.log('Clients retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get client by clientId (رقم العميل اليدوي)
app.get('/api/clients/by-client-id/:clientId', (req, res) => {
  console.log('GET /api/clients/by-client-id/:clientId request');
  console.log('Client ID:', req.params.clientId);

  const { clientId } = req.params;

  db.get("SELECT * FROM Clients WHERE clientId = ? AND isActive = 1", [clientId], (err, row) => {
    if (err) {
      console.error('Error getting client by clientId:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive)
    };

    console.log('Client found by clientId:', data);
    res.json(data);
  });
});

// Search clients by clientId or name
app.get('/api/clients/search/:query', (req, res) => {
  console.log('GET /api/clients/search/:query request');
  console.log('Search query:', req.params.query);

  const { query } = req.params;

  const searchSQL = `
    SELECT * FROM Clients
    WHERE isActive = 1
    AND (
      clientId LIKE ? OR
      clientName LIKE ? OR
      clientPhoneWhatsapp LIKE ?
    )
    ORDER BY clientName
  `;

  const searchPattern = `%${query}%`;

  db.all(searchSQL, [searchPattern, searchPattern, searchPattern], (err, rows) => {
    if (err) {
      console.error('Error searching clients:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(client => ({
      ...client,
      isActive: Boolean(client.isActive)
    }));

    console.log('Search results:', data.length, 'clients found');
    res.json(data);
  });
});

// Get client by ID
app.get('/api/clients/:id', (req, res) => {
  console.log('GET /api/clients/:id request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  db.get("SELECT * FROM Clients WHERE id = ?", [id], (err, row) => {
    if (err) {
      console.error('Error getting client:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive)
    };

    console.log('✅ Client retrieved successfully');
    console.log('🔍 Raw row from database:', row);
    console.log('🔍 Final client data sent to frontend:', data);
    res.json(data);
  });
});

// Create new client
app.post('/api/clients', (req, res) => {
  console.log('🔥 POST /api/clients request received!');
  console.log('🔥 Received data keys:', Object.keys(req.body));

  // Validate client data
  const validation = validateClientData(req.body);
  if (!validation.isValid) {
    console.log('❌ Client validation failed:', validation.errors);
    return res.status(400).json({
      error: 'بيانات العميل غير صحيحة',
      details: validation.errors
    });
  }

  console.log('✅ Client validation passed');

  const {
    clientId,
    clientType: rawClientType,
    clientName,
    clientAddress,
    clientPhoneWhatsapp,
    clientPhone2,
    clientPhone3,
    clientEmail,
    clientNotes,
    clientFinancialGuarantee,
    clientID_Image,
    clientFinancial_Category,
    clientLegal_Rep,
    clientPartner,
    clientReg_Number,
    clientTaxReg_Number,
    clientLegal_Status,
    clientRemarks,
    clientDocuments = '',
    isActive = true
  } = req.body;

  // Keep clientType as is - no normalization needed
  const clientType = rawClientType;

  // Validation
  if (!clientType || !clientName || !clientPhoneWhatsapp) {
    return res.status(400).json({
      error: 'clientType, clientName, and clientPhoneWhatsapp are required'
    });
  }

  // Insert new client directly without validation
  db.run(`
    INSERT INTO Clients (
      clientId, clientType, clientName, clientAddress, clientPhoneWhatsapp,
      clientPhone2, clientPhone3, clientEmail, clientNotes, clientFinancialGuarantee,
      clientID_Image, clientFinancial_Category, clientLegal_Rep, clientPartner,
      clientReg_Number, clientTaxReg_Number, clientLegal_Status, clientRemarks,
      clientDocuments, isActive
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [
    clientId, clientType, clientName, clientAddress, clientPhoneWhatsapp,
    clientPhone2, clientPhone3, clientEmail, clientNotes, clientFinancialGuarantee,
    clientID_Image, clientFinancial_Category, clientLegal_Rep, clientPartner,
    clientReg_Number, clientTaxReg_Number, clientLegal_Status, clientRemarks,
    clientDocuments, isActive ? 1 : 0
  ], function(err) {
    if (err) {
      console.error('Insert client error:', err);

      // معالجة خطأ تكرار رقم العميل
      if (err.message.includes('UNIQUE constraint failed: Clients.clientId')) {
        // البحث عن العميل الموجود لإرسال معلوماته
        db.get("SELECT clientId, clientName, clientType, clientPhoneWhatsapp, clientEmail, clientAddress, createdAt FROM Clients WHERE clientId = ? AND isActive = 1", [clientId], (selectErr, existingClient) => {
          const errorResponse = {
            error: 'العميل مسجل من قبل',
            errorCode: 'CLIENT_ID_EXISTS',
            message: 'العميل مسجل من قبل',
            guidance: 'رقم العميل هذا موجود في النظام مسبقاً. يمكنك البحث عن العميل الموجود أو استخدام رقم عميل مختلف',
            details: `رقم العميل "${clientId}" مسجل بالفعل في النظام`,
            field: 'clientId',
            duplicateClientId: clientId,
            severity: 'error',
            action: 'search_client'
          };

          // إضافة معلومات العميل الموجود إذا تم العثور عليه
          if (!selectErr && existingClient) {
            errorResponse.existingClient = existingClient;
          }

          return res.status(400).json(errorResponse);
        });
        return;
      }

      // معالجة أخطاء أخرى
      return res.status(500).json({
        error: 'حدث خطأ أثناء حفظ بيانات العميل',
        details: 'يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني'
      });
    } else {
      console.log('Client inserted successfully, ID:', this.lastID);
      res.json({ success: true, id: this.lastID });
    }
  });
});

// Update client by clientId
app.put('/api/clients/by-client-id/:clientId', (req, res) => {
  console.log('PUT /api/clients/by-client-id/:clientId request');
  console.log('Client ID:', req.params.clientId);
  console.log('Received data keys:', Object.keys(req.body));

  const { clientId } = req.params;

  // First, get the database ID from clientId
  db.get("SELECT id FROM Clients WHERE clientId = ? AND isActive = 1", [clientId], (err, client) => {
    if (err) {
      console.error('Error finding client:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Now update using the database ID
    req.params.id = client.id;
    return updateClientById(req, res);
  });
});

// Update client by database ID (internal function)
function updateClientById(req, res) {
  const { id } = req.params;

  // Validate client data
  const validation = validateClientData(req.body);
  if (!validation.isValid) {
    console.log('❌ Client update validation failed:', validation.errors);
    return res.status(400).json({
      error: 'بيانات العميل غير صحيحة',
      details: validation.errors
    });
  }

  console.log('✅ Client update validation passed');

  const {
    clientId,
    clientType: rawClientType,
    clientName,
    clientAddress,
    clientPhoneWhatsapp,
    clientPhone2,
    clientPhone3,
    clientEmail,
    clientNotes,
    clientFinancialGuarantee,
    clientID_Image,
    clientFinancial_Category,
    clientLegal_Rep,
    clientPartner,
    clientReg_Number,
    clientTaxReg_Number,
    clientLegal_Status,
    clientRemarks,
    clientDocuments = '',
    isActive = true
  } = req.body;

  // Keep clientType as is - no normalization needed
  const clientType = rawClientType;

  // Validation
  if (!clientId || !clientType || !clientName || !clientPhoneWhatsapp) {
    return res.status(400).json({
      error: 'clientId, clientType, clientName, and clientPhoneWhatsapp are required'
    });
  }

  // Update client directly without validation
    const updateSQL = `
      UPDATE Clients SET
        clientId = ?, clientType = ?, clientName = ?, clientAddress = ?, clientPhoneWhatsapp = ?,
        clientPhone2 = ?, clientPhone3 = ?, clientEmail = ?, clientNotes = ?,
        clientFinancialGuarantee = ?, clientID_Image = ?, clientFinancial_Category = ?,
        clientLegal_Rep = ?, clientPartner = ?, clientReg_Number = ?, clientTaxReg_Number = ?,
        clientLegal_Status = ?, clientRemarks = ?, clientDocuments = ?, isActive = ?,
        updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    db.run(updateSQL, [
      clientId, clientType, clientName, clientAddress, clientPhoneWhatsapp,
      clientPhone2, clientPhone3, clientEmail, clientNotes,
      clientFinancialGuarantee, clientID_Image, clientFinancial_Category,
      clientLegal_Rep, clientPartner, clientReg_Number, clientTaxReg_Number,
      clientLegal_Status, clientRemarks, clientDocuments, isActive ? 1 : 0,
      id
    ], function(err) {
      if (err) {
        console.error('Client update error:', err);
        return res.status(500).json({ error: err.message });
      }

      if (this.changes === 0) {
        return res.status(404).json({ error: 'Client not found' });
      }

      console.log('✅ Client updated successfully');
      res.json({
        success: true,
        message: 'Client updated successfully',
        clientId: id,
        changes: this.changes
      });
    });
}

// Update client
app.put('/api/clients/:id', (req, res) => {
  console.log('PUT /api/clients/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data keys:', Object.keys(req.body));

  // Validate client data
  const validation = validateClientData(req.body);
  if (!validation.isValid) {
    console.log('❌ Client update validation failed:', validation.errors);
    return res.status(400).json({
      error: 'بيانات العميل غير صحيحة',
      details: validation.errors
    });
  }

  console.log('✅ Client update validation passed');

  const { id } = req.params;
  const {
    clientId,
    clientType: rawClientType,
    clientName,
    clientAddress,
    clientPhoneWhatsapp,
    clientPhone2,
    clientPhone3,
    clientEmail,
    clientNotes,
    clientFinancialGuarantee,
    clientID_Image,
    clientFinancial_Category,
    clientLegal_Rep,
    clientPartner,
    clientReg_Number,
    clientTaxReg_Number,
    clientLegal_Status,
    clientRemarks,
    clientDocuments = '',
    isActive = true
  } = req.body;

  // Keep clientType as is - no normalization needed
  const clientType = rawClientType;

  // Validation
  if (!clientId || !clientType || !clientName || !clientPhoneWhatsapp) {
    return res.status(400).json({
      error: 'clientId, clientType, clientName, and clientPhoneWhatsapp are required'
    });
  }

  // Update client directly without validation
    db.run(`
      UPDATE Clients SET
        clientId=?, clientType=?, clientName=?, clientAddress=?, clientPhoneWhatsapp=?,
        clientPhone2=?, clientPhone3=?, clientEmail=?, clientNotes=?,
        clientFinancial_Category=?, clientLegal_Rep=?, clientReg_Number=?, clientTaxReg_Number=?,
        clientLegal_Status=?, clientRemarks=?, clientID_Image=?, clientDocuments=?,
        isActive=?, updatedAt=CURRENT_TIMESTAMP
      WHERE id=?
    `, [
      clientId, clientType, clientName, clientAddress, clientPhoneWhatsapp,
      clientPhone2, clientPhone3, clientEmail, clientNotes,
      clientFinancial_Category, clientLegal_Rep, clientReg_Number, clientTaxReg_Number,
      clientLegal_Status, clientRemarks, clientID_Image, clientDocuments,
      isActive ? 1 : 0, id
    ], function(err) {
      if (err) {
        console.error('Update client error:', err);
        res.status(500).json({ error: err.message });
      } else {
        console.log('Client updated successfully, changes:', this.changes);
        res.json({ success: true, changes: this.changes });
      }
    });
});

// Delete client by clientId
app.delete('/api/clients/by-client-id/:clientId', (req, res) => {
  console.log('DELETE /api/clients/by-client-id/:clientId request');
  console.log('Client ID:', req.params.clientId);

  const { clientId } = req.params;

  // First, get the database ID from clientId
  db.get("SELECT id FROM Clients WHERE clientId = ? AND isActive = 1", [clientId], (err, client) => {
    if (err) {
      console.error('Error finding client:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Now delete using the database ID
    req.params.id = client.id;
    return deleteClientById(req, res);
  });
});

// Delete client (soft delete with protection)
app.delete('/api/clients/:id', (req, res) => {
  return deleteClientById(req, res);
});

// Delete client by database ID (internal function)
function deleteClientById(req, res) {
  console.log('DELETE client by ID request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  // First, check if client has any active contracts
  const checkActiveContractsQuery = `
    SELECT COUNT(*) as activeContractsCount,
           GROUP_CONCAT(contractNumber) as contractNumbers
    FROM Contracts
    WHERE clientId = ? AND isActive = 1 AND isCurrentVersion = 1
  `;

  db.get(checkActiveContractsQuery, [id], (err, result) => {
    if (err) {
      console.error('Error checking active contracts:', err);
      return res.status(500).json({ error: err.message });
    }

    const activeContractsCount = result.activeContractsCount || 0;
    const contractNumbers = result.contractNumbers;

    if (activeContractsCount > 0) {
      console.log(`Cannot delete client ${id}: has ${activeContractsCount} active contracts`);
      return res.status(400).json({
        error: 'لا يمكن حذف العميل لأنه مرتبط بعقود نشطة',
        errorEn: 'Cannot delete client with active contracts',
        activeContractsCount: activeContractsCount,
        contractNumbers: contractNumbers ? contractNumbers.split(',') : [],
        details: {
          ar: `العميل مرتبط بـ ${activeContractsCount} عقد نشط. يجب إنهاء أو إلغاء العقود أولاً.`,
          en: `Client has ${activeContractsCount} active contract(s). Please terminate or cancel contracts first.`,
          contracts: contractNumbers ? contractNumbers.split(',') : []
        }
      });
    }

    // If no active contracts, proceed with soft delete
    db.run(
      "UPDATE Clients SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?",
      [id],
      function(deleteErr) {
        if (deleteErr) {
          console.error('Delete client error:', deleteErr);
          res.status(500).json({ error: deleteErr.message });
        } else {
          console.log('Client deleted successfully, changes:', this.changes);
          res.json({
            success: true,
            changes: this.changes,
            message: 'تم حذف العميل بنجاح',
            messageEn: 'Client deleted successfully'
          });
        }
      }
    );
  });
}

// Check if client can be deleted by clientId
app.get('/api/clients/by-client-id/:clientId/can-delete', (req, res) => {
  console.log('GET /api/clients/by-client-id/:clientId/can-delete request');
  const { clientId } = req.params;

  // First, get the database ID from clientId
  db.get("SELECT id FROM Clients WHERE clientId = ? AND isActive = 1", [clientId], (err, client) => {
    if (err) {
      console.error('Error finding client:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Now check using the database ID
    req.params.id = client.id;
    return checkClientCanDelete(req, res);
  });
});

// Check if client can be deleted
app.get('/api/clients/:id/can-delete', (req, res) => {
  return checkClientCanDelete(req, res);
});

// Check if client can be deleted (internal function)
function checkClientCanDelete(req, res) {
  console.log('Check client can delete request');
  const { id } = req.params;

  const checkQuery = `
    SELECT
      COUNT(*) as activeContractsCount,
      GROUP_CONCAT(contractNumber) as contractNumbers,
      GROUP_CONCAT(contractStatus) as contractStatuses
    FROM Contracts
    WHERE clientId = ? AND isActive = 1 AND isCurrentVersion = 1
  `;

  db.get(checkQuery, [id], (err, result) => {
    if (err) {
      console.error('Error checking client deletion status:', err);
      return res.status(500).json({ error: err.message });
    }

    const activeContractsCount = result.activeContractsCount || 0;
    const canDelete = activeContractsCount === 0;
    const contractNumbers = result.contractNumbers ? result.contractNumbers.split(',') : [];
    const contractStatuses = result.contractStatuses ? result.contractStatuses.split(',') : [];

    res.json({
      canDelete,
      activeContractsCount,
      contractNumbers,
      contractStatuses,
      message: canDelete
        ? 'يمكن حذف العميل'
        : `لا يمكن حذف العميل - مرتبط بـ ${activeContractsCount} عقد نشط`,
      messageEn: canDelete
        ? 'Client can be deleted'
        : `Cannot delete client - has ${activeContractsCount} active contract(s)`,
      details: canDelete ? null : {
        ar: `العميل مرتبط بالعقود التالية: ${contractNumbers.join(', ')}`,
        en: `Client is linked to contracts: ${contractNumbers.join(', ')}`,
        contracts: contractNumbers.map((num, index) => ({
          contractNumber: num,
          status: contractStatuses[index] || 'نشط'
        }))
      }
    });
  });
}

// Contract Receivables API endpoints

// Get all receivables with filtering and sorting
app.get('/api/receivables', (req, res) => {
  console.log('GET /api/receivables request');

  const {
    sortBy = 'dueDate',
    sortOrder = 'ASC',
    status,
    contractId,
    clientId,
    search,
    page = 1,
    limit = 50
  } = req.query;

  // Update receivable statuses first
  updateReceivableStatuses();

  let whereConditions = ['r.isActive = 1'];
  let queryParams = [];

  // Add filters
  if (status) {
    whereConditions.push('r.status = ?');
    queryParams.push(status);
  }

  if (contractId) {
    whereConditions.push('r.contractId = ?');
    queryParams.push(contractId);
  }

  if (clientId) {
    whereConditions.push('r.clientId = ?');
    queryParams.push(clientId);
  }

  if (search) {
    whereConditions.push(`(
      r.receivableNumber LIKE ? OR
      c.contractNumber LIKE ? OR
      cl.clientName LIKE ? OR
      r.description LIKE ?
    )`);
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }

  // Validate sort column
  const validSortColumns = ['dueDate', 'amount', 'contractNumber', 'clientName', 'status', 'installmentNumber'];
  const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'dueDate';
  const order = sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';

  // Build column mapping for sorting
  const columnMapping = {
    'dueDate': 'r.dueDate',
    'amount': 'r.amount',
    'contractNumber': 'c.contractNumber',
    'clientName': 'cl.clientName',
    'status': 'r.status',
    'installmentNumber': 'r.installmentNumber'
  };

  const orderByColumn = columnMapping[sortColumn] || 'r.dueDate';

  const query = `
    SELECT
      r.*,
      c.contractNumber,
      c.contractSubject as contractDescription,
      cl.clientId as clientNumber,
      cl.clientName,
      cl.clientType,
      cl.clientPhoneWhatsapp,
      cl.clientEmail,
      -- حساب حالة الدفع من المدفوعات
      COALESCE(
        (SELECT SUM(amount) FROM CashReceipts WHERE receivableId = r.id AND isActive = 1), 0
      ) + COALESCE(
        (SELECT SUM(amount) FROM ChequeReceipts WHERE receivableId = r.id AND isActive = 1), 0
      ) + COALESCE(
        (SELECT SUM(amount) FROM BankPayments WHERE receivableId = r.id AND isActive = 1), 0
      ) as paidAmount,
      CASE
        WHEN COALESCE(
          (SELECT SUM(amount) FROM CashReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM ChequeReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM BankPayments WHERE receivableId = r.id AND isActive = 1), 0
        ) >= r.amount THEN 'مدفوع'
        WHEN r.dueDate < date('now') THEN 'متأخر'
        WHEN r.dueDate <= date('now') THEN 'مستحق'
        ELSE 'لم يحن موعده'
      END as calculatedStatus
    FROM ContractReceivables r
    LEFT JOIN Contracts c ON r.contractId = c.id
    LEFT JOIN Clients cl ON r.clientId = cl.id
    WHERE ${whereConditions.join(' AND ')}
    ORDER BY ${orderByColumn} ${order}
    LIMIT ? OFFSET ?
  `;

  const offset = (page - 1) * limit;
  queryParams.push(limit, offset);

  db.all(query, queryParams, (err, rows) => {
    if (err) {
      console.error('Error getting receivables:', err);
      return res.status(500).json({ error: err.message });
    }

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM ContractReceivables r
      LEFT JOIN Contracts c ON r.contractId = c.id
      LEFT JOIN Clients cl ON r.clientId = cl.id
      WHERE ${whereConditions.join(' AND ')}
    `;

    db.get(countQuery, queryParams.slice(0, -2), (countErr, countResult) => {
      if (countErr) {
        console.error('Error getting receivables count:', countErr);
        return res.status(500).json({ error: countErr.message });
      }

      const total = countResult.total;
      const totalPages = Math.ceil(total / limit);

      // Update the status field with calculated status and format the response
      const formattedRows = rows.map(row => ({
        ...row,
        status: row.calculatedStatus, // Use the calculated status
        isPaid: Boolean(row.installmentPaid),
        paidAmount: row.paidAmount || 0,
        remainingAmount: row.installmentRemaining || row.amount,
        paidDate: row.installmentPaidDate
      }));

      res.json({
        receivables: formattedRows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });
    });
  });
});

// Get receivables statistics
app.get('/api/receivables/stats', (req, res) => {
  console.log('GET /api/receivables/stats request');

  // Update receivable statuses first
  updateReceivableStatuses();

  const statsQuery = `
    SELECT
      COUNT(*) as totalReceivables,
      SUM(r.amount) as totalAmount,
      SUM(CASE
        WHEN COALESCE(
          (SELECT SUM(amount) FROM CashReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM ChequeReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM BankPayments WHERE receivableId = r.id AND isActive = 1), 0
        ) >= r.amount THEN 0
        WHEN r.dueDate <= date('now') THEN r.amount
        ELSE 0
      END) as dueAmount,
      SUM(CASE
        WHEN COALESCE(
          (SELECT SUM(amount) FROM CashReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM ChequeReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM BankPayments WHERE receivableId = r.id AND isActive = 1), 0
        ) >= r.amount THEN 0
        WHEN r.dueDate < date('now') THEN r.amount
        ELSE 0
      END) as overdueAmount,
      SUM(CASE
        WHEN COALESCE(
          (SELECT SUM(amount) FROM CashReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM ChequeReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM BankPayments WHERE receivableId = r.id AND isActive = 1), 0
        ) >= r.amount THEN r.amount
        ELSE 0
      END) as paidAmount,
      SUM(CASE
        WHEN COALESCE(
          (SELECT SUM(amount) FROM CashReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM ChequeReceipts WHERE receivableId = r.id AND isActive = 1), 0
        ) + COALESCE(
          (SELECT SUM(amount) FROM BankPayments WHERE receivableId = r.id AND isActive = 1), 0
        ) >= r.amount THEN 0
        WHEN r.dueDate > date('now') THEN r.amount
        ELSE 0
      END) as futureAmount
    FROM ContractReceivables r
    WHERE r.isActive = 1
  `;

  db.get(statsQuery, [], (err, stats) => {
    if (err) {
      console.error('Error getting receivables stats:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(stats);
  });
});

// Generate receivables for existing contract
app.post('/api/contracts/generate-receivables', (req, res) => {
  console.log('POST /api/contracts/generate-receivables request');
  const { contractId } = req.body;

  if (!contractId) {
    return res.status(400).json({ error: 'contractId is required' });
  }

  // Get contract details
  db.get("SELECT * FROM Contracts WHERE id = ? AND isActive = 1", [contractId], (err, contract) => {
    if (err) {
      console.error('Error fetching contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Check if receivables already exist
    db.get("SELECT COUNT(*) as count FROM ContractReceivables WHERE contractId = ?", [contractId], (countErr, countResult) => {
      if (countErr) {
        console.error('Error checking existing receivables:', countErr);
        return res.status(500).json({ error: countErr.message });
      }

      if (countResult.count > 0) {
        return res.status(400).json({
          error: 'Receivables already exist for this contract',
          existingCount: countResult.count
        });
      }

      // Generate receivables
      generateContractReceivables(contract, (genErr, receivablesCount) => {
        if (genErr) {
          console.error('Error generating receivables:', genErr);
          return res.status(500).json({ error: 'Failed to generate receivables: ' + genErr.message });
        }

        res.json({
          success: true,
          contractId,
          contractNumber: contract.contractNumber,
          receivablesCount
        });
      });
    });
  });
});

// Regenerate receivables for existing contract (delete old ones and create new)
app.post('/api/contracts/regenerate-receivables', (req, res) => {
  console.log('POST /api/contracts/regenerate-receivables request');
  const { contractId } = req.body;

  if (!contractId) {
    return res.status(400).json({ error: 'contractId is required' });
  }

  // Get contract details
  db.get("SELECT * FROM Contracts WHERE id = ? AND isActive = 1", [contractId], (err, contract) => {
    if (err) {
      console.error('Error fetching contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Delete existing receivables
    db.run("DELETE FROM ContractReceivables WHERE contractId = ?", [contractId], (deleteErr) => {
      if (deleteErr) {
        console.error('Error deleting existing receivables:', deleteErr);
        return res.status(500).json({ error: deleteErr.message });
      }

      console.log('✅ Deleted existing receivables for contract:', contract.contractNumber);

      // Generate new receivables
      generateContractReceivables(contract, (genErr, receivablesCount) => {
        if (genErr) {
          console.error('Error generating receivables:', genErr);
          return res.status(500).json({ error: 'Failed to generate receivables: ' + genErr.message });
        }

        res.json({
          success: true,
          contractId,
          contractNumber: contract.contractNumber,
          receivablesCount,
          message: 'تم إعادة إنشاء الاستحقاقات بنجاح'
        });
      });
    });
  });
});

// Contracts API endpoints

// Get all contracts
app.get('/api/contracts', (req, res) => {
  try {
    const query = `
      SELECT c.*, cl.clientId as clientNumber, cl.clientName, cl.clientType, cl.clientPhoneWhatsapp, cl.clientAddress,
             cl.clientEmail, cl.clientFinancial_Category as clientClassification,
             fg.clientName as financialGuarantorName
      FROM Contracts c
      LEFT JOIN Clients cl ON c.clientId = cl.id
      LEFT JOIN Clients fg ON c.financialGuarantorId = fg.id
      WHERE c.isActive = 1
      ORDER BY c.createdAt DESC
    `;

    db.all(query, (err, rows) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }

      const data = rows.map(contract => ({
        ...contract,
        isActive: Boolean(contract.isActive),
        totalContractValue: parseFloat(contract.totalContractValue || 0),
        monthlyAmount: parseFloat(contract.monthlyAmount || 0),
        lateFeeValue: parseFloat(contract.lateFeeValue || 0),
        bouncedCheckFeeValue: parseFloat(contract.bouncedCheckFeeValue || 0),
        additionalFees: contract.additionalFees || '[]'
      }));

      res.json(data);
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ===== NEW CONTRACTS API ENDPOINTS (using NewContracts table) =====

// Get all contracts from new table
app.get('/api/new-contracts', (req, res) => {
  console.log('GET /api/new-contracts request');

  const query = `
    SELECT c.*, cl.clientName, cl.clientType, cl.clientPhoneWhatsapp, cl.clientAddress,
           cl.clientEmail, cl.clientFinancial_Category as clientClassification,
           fg.clientName as financialGuarantorName
    FROM NewContracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    LEFT JOIN Clients fg ON c.financialGuarantorId = fg.id
    WHERE c.isActive = 1
    ORDER BY c.createdAt DESC
  `;

  db.all(query, (err, rows) => {
    if (err) {
      console.error('Error getting new contracts:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    // Process the data
    const processedRows = rows.map(row => ({
      ...row,
      isActive: Boolean(row.isActive),
      totalContractValue: parseFloat(row.totalContractValue),
      monthlyAmount: parseFloat(row.monthlyAmount),
      finalInsuranceRate: parseFloat(row.finalInsuranceRate),
      finalInsuranceAmount: parseFloat(row.finalInsuranceAmount),
      advancePaymentAmount: parseFloat(row.advancePaymentAmount),
      lateFeeValue: parseFloat(row.lateFeeValue),
      bouncedCheckFeeValue: parseFloat(row.bouncedCheckFeeValue),
      additionalFees: row.additionalFees ? JSON.parse(row.additionalFees) : []
    }));

    console.log('New contracts retrieved successfully:', processedRows.length, 'items');
    res.json(processedRows);
  });
});

// Get new contract by ID
app.get('/api/new-contracts/:id', (req, res) => {
  console.log('GET /api/new-contracts/:id request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  const query = `
    SELECT c.*, cl.clientName, cl.clientType, cl.clientPhoneWhatsapp, cl.clientAddress,
           cl.clientEmail, cl.clientFinancial_Category as clientClassification,
           fg.clientName as financialGuarantorName
    FROM NewContracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    LEFT JOIN Clients fg ON c.financialGuarantorId = fg.id
    WHERE c.id = ?
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error getting new contract:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      res.status(404).json({ error: 'Contract not found' });
      return;
    }

    // Process the data
    const processedRow = {
      ...row,
      isActive: Boolean(row.isActive),
      totalContractValue: parseFloat(row.totalContractValue),
      monthlyAmount: parseFloat(row.monthlyAmount),
      finalInsuranceRate: parseFloat(row.finalInsuranceRate),
      finalInsuranceAmount: parseFloat(row.finalInsuranceAmount),
      advancePaymentAmount: parseFloat(row.advancePaymentAmount),
      lateFeeValue: parseFloat(row.lateFeeValue),
      bouncedCheckFeeValue: parseFloat(row.bouncedCheckFeeValue),
      additionalFees: row.additionalFees ? JSON.parse(row.additionalFees) : []
    };

    console.log('New contract retrieved successfully:', processedRow.contractNumber);
    res.json(processedRow);
  });
});

// Create new contract using new table
app.post('/api/new-contracts', (req, res) => {
  console.log('POST /api/new-contracts request');
  req.body.useNewTable = true; // Force use of new table
  return handleNewContract(req, res);
});

// ===== END NEW CONTRACTS API ENDPOINTS =====

// Get contract by ID (Updated to use new simplified table)
app.get('/api/contracts/:id', (req, res) => {
  console.log('GET /api/contracts/:id request - using simplified table');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  const query = `
    SELECT c.*, cl.clientId as clientNumber, cl.clientName, cl.clientType, cl.clientPhoneWhatsapp, cl.clientAddress,
           cl.clientEmail, cl.clientFinancial_Category as clientClassification,
           fg.clientName as financialGuarantorName
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    LEFT JOIN Clients fg ON c.financialGuarantorId = fg.id
    WHERE c.id = ? AND c.isActive = 1
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error getting contract:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Parse additional fees if it's a JSON string
    let additionalFees = [];
    try {
      if (row.additionalFees) {
        additionalFees = typeof row.additionalFees === 'string' ?
          JSON.parse(row.additionalFees) : row.additionalFees;
      }
    } catch (e) {
      console.warn('Error parsing additional fees:', e);
      additionalFees = [];
    }

    // Get contract products
    db.all("SELECT * FROM ContractProducts WHERE contractId = ? AND isActive = 1", [id], (productsErr, products) => {
      if (productsErr) {
        console.error('Error getting contract products:', productsErr);
        return res.status(500).json({ error: productsErr.message });
      }

      // Get contract partners
      db.all("SELECT * FROM ContractPartners WHERE contractId = ? AND isActive = 1", [id], (partnersErr, partners) => {
        if (partnersErr) {
          console.error('Error getting contract partners:', partnersErr);
          return res.status(500).json({ error: partnersErr.message });
        }

        // Get contract attachments
        db.all("SELECT * FROM ContractAttachments WHERE contractId = ? AND isActive = 1 ORDER BY createdAt DESC", [id], (attachmentsErr, attachments) => {
          if (attachmentsErr) {
            console.error('Error getting contract attachments:', attachmentsErr);
            return res.status(500).json({ error: attachmentsErr.message });
          }

          // Get contract installments
          db.all("SELECT * FROM ContractInstallments WHERE contractId = ? ORDER BY installmentDate", [id], (installmentsErr, installments) => {
            if (installmentsErr) {
              console.error('Error getting contract installments:', installmentsErr);
              return res.status(500).json({ error: installmentsErr.message });
            }

            // Get contract receivables
            db.all(`
              SELECT cr.*,
                     CASE
                       WHEN cr.dueDate < date('now') AND cr.paidAmount < cr.amount THEN 'متأخر'
                       WHEN cr.paidAmount >= cr.amount THEN 'مسدد'
                       WHEN cr.dueDate >= date('now') THEN 'لم يحن موعد استحقاقه'
                       ELSE 'مستحق'
                     END as status
              FROM ContractReceivables cr
              WHERE cr.contractId = ?
              ORDER BY cr.dueDate
            `, [id], (receivablesErr, receivables) => {
              if (receivablesErr) {
                console.error('Error getting contract receivables:', receivablesErr);
                return res.status(500).json({ error: receivablesErr.message });
              }

              // Get contract timeline/audit log
              db.all("SELECT * FROM ContractTimeline WHERE contractId = ? AND isActive = 1 ORDER BY eventDate DESC", [id], (timelineErr, timeline) => {
                if (timelineErr) {
                  console.error('Error getting contract timeline:', timelineErr);
                  return res.status(500).json({ error: timelineErr.message });
                }

                const data = {
                  ...row,
                  isActive: Boolean(row.isActive),
                  totalContractValue: parseFloat(row.totalContractValue) || 0,
                  monthlyAmount: parseFloat(row.monthlyAmount) || 0,
                  finalInsuranceRate: parseFloat(row.finalInsuranceRate) || 0,
                  finalInsuranceAmount: parseFloat(row.finalInsuranceAmount) || 0,
                  advancePaymentAmount: parseFloat(row.advancePaymentAmount) || 0,
                  lateFeeValue: parseFloat(row.lateFeeValue) || 0,
                  bouncedCheckFeeValue: parseFloat(row.bouncedCheckFeeValue) || 0,
                  ownershipPercentage: parseFloat(row.ownershipPercentage) || 100,
                  additionalFees: additionalFees,
                  hasUnifiedActivationDate: Boolean(row.hasUnifiedActivationDate),
                  products: (products || []).map(product => ({
                    ...product,
                    area: parseFloat(product.area) || 0,
                    meterPrice: parseFloat(product.meterPrice) || 0,
                    taxRate: parseFloat(product.taxRate) || 0,
                    accountingDuration: parseFloat(product.accountingDuration) || 0,
                    increaseValue: parseFloat(product.increaseValue) || 0,
                    taxInfo: Boolean(product.taxInfo),
                    hasAnnualIncrease: Boolean(product.hasAnnualIncrease),
                    customIntervals: product.customIntervals ?
                      (typeof product.customIntervals === 'string' ?
                        JSON.parse(product.customIntervals) : product.customIntervals) : []
                  })),
                  partners: partners || [],
                  attachments: attachments || [],
                  installments: installments || [],
                  receivables: receivables || [],
                  timeline: timeline || []
                };

                console.log('Contract retrieved successfully with all related data');
                res.json(data);
              });
            });
          });
        });
      });
    });
  });
});

// Get payments for a contract
app.get('/api/contracts/:id/payments', (req, res) => {
  console.log('GET /api/contracts/:id/payments request');
  console.log('Contract ID:', req.params.id);

  const { id } = req.params;

  db.all(
    "SELECT * FROM Payments WHERE contractId = ? AND isActive = 1 ORDER BY paymentNumber",
    [id],
    (err, rows) => {
      if (err) {
        console.error('Error getting payments:', err);
        res.status(500).json({ error: err.message });
        return;
      }

      const data = rows.map(payment => ({
        ...payment,
        isPaid: Boolean(payment.isPaid),
        isActive: Boolean(payment.isActive),
        amount: parseFloat(payment.amount),
        lateFee: parseFloat(payment.lateFee),
        bouncedCheckFee: parseFloat(payment.bouncedCheckFee),
        totalAmount: parseFloat(payment.totalAmount)
      }));

      console.log('Payments retrieved successfully:', data.length, 'items');
      res.json(data);
    }
  );
});

// ===== UNIFIED CONTRACT CREATION ENDPOINT =====
// This endpoint intelligently handles all contract creation scenarios
app.post('/api/contracts', (req, res) => {
  console.log('🔥 POST /api/contracts (UNIFIED) request');
  console.log('🔥 Received data:', req.body);

  // Smart detection of contract type based on data structure
  const isEnhancedContract = req.body.products && req.body.products.length > 0;
  const isSimpleContract = req.body.contractSubject || req.body.contractDescription;

  console.log('🔍 Contract type detection:', {
    isEnhanced: isEnhancedContract,
    isSimple: isSimpleContract,
    hasProducts: !!req.body.products,
    hasPartners: !!(req.body.partners && req.body.partners.length > 0)
  });

  // Simple contract creation using existing table schema
  try {
    const { contractNumber, clientId, totalContractValue, monthlyAmount } = req.body;

    if (!contractNumber || !clientId || !totalContractValue || !monthlyAmount) {
      return res.status(400).json({
        error: 'Required fields: contractNumber, clientId, totalContractValue, monthlyAmount'
      });
    }

    // Insert into existing Contracts table with proper schema
    const contractSQL = `
      INSERT INTO Contracts (
        contractNumber, contractSubject, contractDescription, clientId, contractType, contractStatus,
        startDate, endDate, contractDurationYears, totalContractValue, monthlyAmount, paymentDay,
        paymentFrequency, firstInstallmentDate, paymentMethod, lateFeeType, lateFeeValue,
        gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue, additionalFees,
        numberOfProducts, hasUnifiedActivationDate, notes, importantNotes,
        assetOwner, responsibleDepartment, region, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(contractSQL, [
      contractNumber,
      req.body.contractSubject || '',
      req.body.contractDescription || '',
      clientId,
      req.body.contractType || 'عقد إيجار',
      req.body.contractStatus || 'نشط',
      req.body.startDate || new Date().toISOString().split('T')[0],
      req.body.endDate || new Date().toISOString().split('T')[0],
      req.body.contractDurationYears || 1,
      totalContractValue,
      monthlyAmount,
      req.body.paymentDay || 1,
      req.body.paymentFrequency || 'شهري',
      req.body.firstInstallmentDate || new Date().toISOString().split('T')[0],
      req.body.paymentMethod || 'تحويل بنكي',
      req.body.lateFeeType || 'نسبة مئوية',
      req.body.lateFeeValue || 0,
      req.body.gracePeriodDays || 0,
      req.body.bouncedCheckFeeType || 'مبلغ ثابت',
      req.body.bouncedCheckFeeValue || 0,
      JSON.stringify(req.body.additionalFees || []),
      req.body.numberOfProducts || 1,
      req.body.hasUnifiedActivationDate ? 1 : 0,
      req.body.notes || '',
      req.body.importantNotes || '',
      req.body.assetOwner || '',
      req.body.responsibleDepartment || '',
      req.body.region || '',
      1
    ], function(err) {
      if (err) {
        return res.status(500).json({ error: err.message });
      }

      res.json({
        success: true,
        contractId: this.lastID,
        message: 'Contract created successfully'
      });
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ===== CONTRACT HANDLERS =====

// New Clean Contract Handler (using NewContracts table)
function handleNewContract(req, res) {
  console.log('🔥 New Clean Contract Handler');

  const {
    contractNumber,
    contractSubject,
    contractDescription,
    clientId,
    contractType,
    contractStatus = 'نشط',
    contractSigningDate,
    startDate,
    endDate,
    contractDurationYears,
    totalContractValue,
    monthlyAmount,
    paymentDay = 1,
    paymentFrequency = 'شهري',
    firstInstallmentDate,
    paymentMethod = 'تحويل بنكي',
    finalInsuranceRate = 0,
    finalInsuranceAmount = 0,
    advancePaymentMonths = 0,
    advancePaymentAmount = 0,
    responsibleDepartment,
    region,
    zone,
    assetOwner,
    financialGuarantorId,
    checkStatus = 'لم يتقدم بالشيكات',
    lateFeeType = 'نسبة مئوية',
    lateFeeValue = 0,
    gracePeriodDays = 5,
    bouncedCheckFeeType = 'مبلغ ثابت',
    bouncedCheckFeeValue = 0,
    numberOfProducts = 1,
    hasUnifiedActivationDate = 1,
    notes,
    contractNotes,
    importantNotes,
    additionalFees,
    products = [],
    partners = [],
    isActive = true
  } = req.body;

  // Validation
  if (!contractNumber || !clientId || !totalContractValue || !monthlyAmount || !startDate || !contractDurationYears) {
    return res.status(400).json({
      error: 'Required fields: contractNumber, clientId, totalContractValue, monthlyAmount, startDate, contractDurationYears'
    });
  }

  // Check if contract number already exists
  db.get("SELECT id FROM NewContracts WHERE contractNumber = ?", [contractNumber], (err, existingContract) => {
    if (err) {
      console.error('Error checking existing contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingContract) {
      return res.status(400).json({ error: 'Contract number already exists' });
    }

    // Calculate end date if not provided
    const finalEndDate = endDate || (() => {
      const start = new Date(startDate);
      start.setFullYear(start.getFullYear() + contractDurationYears);
      // طرح يوم واحد للحصول على آخر يوم في فترة العقد
      start.setDate(start.getDate() - 1);
      return start.toISOString().split('T')[0];
    })();

    // Insert new contract
    const contractSQL = `
      INSERT INTO NewContracts (
        contractNumber, contractSubject, contractDescription, clientId, contractType, contractStatus,
        contractSigningDate, startDate, endDate, contractDurationYears,
        totalContractValue, monthlyAmount, paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod,
        finalInsuranceRate, finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount,
        responsibleDepartment, region, zone, assetOwner, financialGuarantorId,
        checkStatus, lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue,
        numberOfProducts, hasUnifiedActivationDate, notes, contractNotes, importantNotes, additionalFees, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(contractSQL, [
      contractNumber, contractSubject, contractDescription, clientId, contractType, contractStatus,
      contractSigningDate, startDate, finalEndDate, contractDurationYears,
      totalContractValue, monthlyAmount, paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod,
      finalInsuranceRate, finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount,
      responsibleDepartment, region, zone, assetOwner, financialGuarantorId,
      checkStatus, lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue,
      numberOfProducts, hasUnifiedActivationDate, notes || contractNotes, contractNotes, importantNotes,
      JSON.stringify(additionalFees || []), isActive ? 1 : 0
    ], function(err) {
      if (err) {
        console.error('New contract insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      const contractId = this.lastID;
      console.log('✅ New contract inserted with ID:', contractId);

      // Insert products if any
      if (products && products.length > 0) {
        insertContractProducts(contractId, products, (productErr) => {
          if (productErr) {
            console.error('Error inserting contract products:', productErr);
            return res.status(500).json({ error: productErr.message });
          }

          console.log('✅ Contract products inserted successfully');
          res.status(201).json({
            message: 'Contract created successfully',
            contractId: contractId,
            contractNumber: contractNumber
          });
        });
      } else {
        res.status(201).json({
          message: 'Contract created successfully',
          contractId: contractId,
          contractNumber: contractNumber
        });
      }
    });
  });
}

// Enhanced Contract Handler (with products, partners, etc.)
function handleEnhancedContract(req, res) {
  console.log('🔥 Enhanced Contract Handler');

  // Validate contract data
  const validation = validateContractData(req.body);
  if (!validation.isValid) {
    console.log('❌ Contract validation failed:', validation.errors);
    return res.status(400).json({
      error: 'بيانات العقد غير صحيحة',
      details: validation.errors
    });
  }

  console.log('✅ Contract validation passed');

  const {
    contractNumber,
    contractDescription,
    contractSubject,
    clientId,
    contractType,
    contractStatus = 'نشط',
    contractDate,
    contractSigningDate,
    startDate,
    actualStartDate,
    endDate,
    actualEndDate,
    contractDurationYears,
    assetOwner,
    ownershipPercentage = 100,
    responsibleDepartment,
    region,
    financialGuarantorId,
    parentContractId,
    numberOfProducts = 1,
    hasUnifiedActivationDate = true,
    totalContractValue,
    monthlyAmount,
    paymentDay = 1,
    paymentFrequency = 'شهري',
    irregularPaymentMonths,
    finalInsuranceRate = 0,
    advancePaymentMonths = 0,
    advancePaymentAmount = 0,
    financialActivationDate,
    checkStatus = 'لم يتقدم بالشيكات',
    lateFeeType = 'نسبة مئوية',
    lateFeeValue = 0,
    gracePeriodDays = 5,
    bouncedCheckFeeType = 'مبلغ ثابت',
    bouncedCheckFeeValue = 0,
    additionalFees,
    importantNotes,
    notes,
    products = [],
    partners = [],
    isActive = true
  } = req.body;

  // Use notes directly
  const finalNotes = notes || null;

  // Enhanced validation
  if (!contractNumber || !clientId || !totalContractValue || !monthlyAmount) {
    return res.status(400).json({
      error: 'Required fields: contractNumber, clientId, totalContractValue, monthlyAmount'
    });
  }

  // Check if contract number already exists
  db.get("SELECT id FROM Contracts WHERE contractNumber = ?", [contractNumber], (err, existingContract) => {
    if (err) {
      console.error('Error checking existing contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingContract) {
      return res.status(400).json({ error: 'Contract number already exists' });
    }

    // Calculate contract dates from products if not provided
    const getContractDates = () => {
      if (!products || products.length === 0) {
        return {
          startDate: startDate || new Date().toISOString().split('T')[0],
          endDate: endDate || new Date().toISOString().split('T')[0]
        };
      }

      const dates = products
        .filter(p => p.activationDate && p.endDate)
        .map(p => ({
          start: new Date(p.activationDate),
          end: new Date(p.endDate)
        }));

      if (dates.length === 0) {
        return {
          startDate: startDate || new Date().toISOString().split('T')[0],
          endDate: endDate || new Date().toISOString().split('T')[0]
        };
      }

      const earliestStart = new Date(Math.min(...dates.map(d => d.start.getTime())));
      const latestEnd = new Date(Math.max(...dates.map(d => d.end.getTime())));

      return {
        startDate: earliestStart.toISOString().split('T')[0],
        endDate: latestEnd.toISOString().split('T')[0]
      };
    };

    const { startDate: finalStartDate, endDate: finalEndDate } = getContractDates();
    const finalContractDate = contractSigningDate || contractDate || finalStartDate;

    // Insert enhanced contract
    const contractSQL = `
      INSERT INTO Contracts (
        contractNumber, contractInternalId, contractDescription, contractSubject,
        clientId, contractType, contractStatus, contractDate, contractSigningDate,
        financialActivationDate, startDate, actualStartDate, endDate, actualEndDate,
        contractDurationYears, assetOwner, financialGuarantorId, parentContractId,
        numberOfProducts, hasUnifiedActivationDate, totalContractValue, monthlyAmount,
        paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod, irregularPaymentMonths,
        annualIncreaseType, annualIncreaseValue, annualIncreaseStartYear,
        lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue,
        finalInsuranceRate, finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount,
        checkStatus, ownershipPercentage, responsibleDepartment, region, zone,
        additionalFees, importantNotes, systemFlags, notes, isActive,
        originalContractNumber, versionNumber, isCurrentVersion, parentVersionId,
        editCount, editHistory, editReason
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(contractSQL, [
      contractNumber, req.body.contractInternalId || null, contractDescription, contractSubject,
      clientId, contractType, contractStatus, contractDate || finalContractDate, contractSigningDate,
      financialActivationDate, finalStartDate, actualStartDate, finalEndDate, actualEndDate,
      contractDurationYears, assetOwner, financialGuarantorId, parentContractId,
      numberOfProducts, hasUnifiedActivationDate ? 1 : 0, totalContractValue, monthlyAmount,
      paymentDay, paymentFrequency, req.body.firstInstallmentDate, req.body.paymentMethod || 'تحويل بنكي', irregularPaymentMonths || 0,
      req.body.annualIncreaseType || 'لا يوجد', req.body.annualIncreaseValue || 0, req.body.annualIncreaseStartYear || 2,
      lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue,
      finalInsuranceRate, finalInsuranceRate * totalContractValue / 100, advancePaymentMonths, advancePaymentAmount,
      checkStatus, ownershipPercentage, responsibleDepartment, region, req.body.zone,
      additionalFees, importantNotes, req.body.systemFlags, finalNotes, isActive ? 1 : 0,
      req.body.originalContractNumber, req.body.versionNumber || 1, req.body.isCurrentVersion !== false ? 1 : 0, req.body.parentVersionId,
      req.body.editCount || 0, req.body.editHistory, req.body.editReason
    ], function(err) {
      if (err) {
        console.error('Enhanced contract insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      const contractId = this.lastID;
      console.log('✅ Enhanced contract inserted with ID:', contractId);

      // Insert products if any
      if (products && products.length > 0) {
        insertContractProducts(contractId, products, (productErr) => {
          if (productErr) {
            console.error('Error inserting products:', productErr);
            return res.status(500).json({ error: productErr.message });
          }

          // Insert partners if any
          if (partners && partners.length > 0) {
            insertContractPartners(contractId, partners, (partnerErr) => {
              if (partnerErr) {
                console.error('Error inserting partners:', partnerErr);
                return res.status(500).json({ error: partnerErr.message });
              }

              addTimelineEntry(contractId, 'توقيع', finalContractDate, 'تم إنشاء العقد');

              // Generate receivables automatically for the new contract
              console.log('🔄 Auto-generating receivables for new contract...');
              const contractForReceivables = {
                id: contractId,
                contractNumber,
                clientId,
                startDate: finalStartDate,
                totalContractValue,
                paymentFrequency,
                contractDurationYears
              };

              generateContractReceivables(contractForReceivables, (genErr, receivablesCount) => {
                if (genErr) {
                  console.error('⚠️ Error auto-generating receivables:', genErr);
                  // Don't fail the contract creation, just log the error
                  res.json({
                    success: true,
                    id: contractId,
                    type: 'enhanced',
                    warning: 'Contract created but receivables generation failed'
                  });
                } else {
                  console.log(`✅ Auto-generated ${receivablesCount} receivables for contract ${contractNumber}`);
                  res.json({
                    success: true,
                    id: contractId,
                    type: 'enhanced',
                    receivablesCount
                  });
                }
              });
            });
          } else {
            addTimelineEntry(contractId, 'توقيع', finalContractDate, 'تم إنشاء العقد');

            // Generate receivables automatically for the new contract (no partners case)
            console.log('🔄 Auto-generating receivables for new contract (no partners)...');
            const contractForReceivables = {
              id: contractId,
              contractNumber,
              clientId,
              startDate: finalStartDate,
              totalContractValue,
              paymentFrequency,
              contractDurationYears
            };

            generateContractReceivables(contractForReceivables, (genErr, receivablesCount) => {
              if (genErr) {
                console.error('⚠️ Error auto-generating receivables:', genErr);
                res.json({
                  success: true,
                  id: contractId,
                  type: 'enhanced',
                  warning: 'Contract created but receivables generation failed'
                });
              } else {
                console.log(`✅ Auto-generated ${receivablesCount} receivables for contract ${contractNumber}`);
                res.json({
                  success: true,
                  id: contractId,
                  type: 'enhanced',
                  receivablesCount
                });
              }
            });
          }
        });
      } else {
        // Insert partners if any
        if (partners && partners.length > 0) {
          insertContractPartners(contractId, partners, (partnerErr) => {
            if (partnerErr) {
              console.error('Error inserting partners:', partnerErr);
              return res.status(500).json({ error: partnerErr.message });
            }

            addTimelineEntry(contractId, 'توقيع', finalContractDate, 'تم إنشاء العقد');

            // Generate receivables automatically (with partners, no products case)
            console.log('🔄 Auto-generating receivables for new contract (with partners, no products)...');
            const contractForReceivables = {
              id: contractId,
              contractNumber,
              clientId,
              startDate: finalStartDate,
              totalContractValue,
              paymentFrequency,
              contractDurationYears
            };

            generateContractReceivables(contractForReceivables, (genErr, receivablesCount) => {
              if (genErr) {
                console.error('⚠️ Error auto-generating receivables:', genErr);
                res.json({
                  success: true,
                  id: contractId,
                  type: 'enhanced',
                  warning: 'Contract created but receivables generation failed'
                });
              } else {
                console.log(`✅ Auto-generated ${receivablesCount} receivables for contract ${contractNumber}`);
                res.json({
                  success: true,
                  id: contractId,
                  type: 'enhanced',
                  receivablesCount
                });
              }
            });
          });
        } else {
          addTimelineEntry(contractId, 'توقيع', finalContractDate, 'تم إنشاء العقد');

          // Generate receivables automatically (no partners, no products case)
          console.log('🔄 Auto-generating receivables for new contract (no partners, no products)...');
          const contractForReceivables = {
            id: contractId,
            contractNumber,
            clientId,
            startDate: finalStartDate,
            totalContractValue,
            paymentFrequency,
            contractDurationYears
          };

          generateContractReceivables(contractForReceivables, (genErr, receivablesCount) => {
            if (genErr) {
              console.error('⚠️ Error auto-generating receivables:', genErr);
              res.json({
                success: true,
                id: contractId,
                type: 'enhanced',
                warning: 'Contract created but receivables generation failed'
              });
            } else {
              console.log(`✅ Auto-generated ${receivablesCount} receivables for contract ${contractNumber}`);
              res.json({
                success: true,
                id: contractId,
                type: 'enhanced',
                receivablesCount
              });
            }
          });
        }
      }
    });
  });
}

// Simple Contract Handler (with basic fields)
function handleSimpleContract(req, res) {
  console.log('🔥 Simple Contract Handler');
  console.log('🔥 Request body:', JSON.stringify(req.body, null, 2));

  const {
    contractNumber,
    contractSubject,
    contractDescription,
    clientId,
    contractType,
    contractStatus = 'نشط',
    contractSigningDate,
    startDate,
    endDate,
    contractDurationYears,
    totalContractValue,
    monthlyAmount,
    paymentDay = 1,
    paymentFrequency = 'مختلط',
    firstInstallmentDate,
    paymentMethod = 'تحويل بنكي',
    finalInsuranceRate = 0,
    finalInsuranceAmount = 0,
    advancePaymentMonths = 0,
    advancePaymentAmount = 0,
    responsibleDepartment,
    region,
    zone,
    assetOwner,
    checkStatus,
    lateFeeType = 'نسبة مئوية',
    lateFeeValue = 0,
    gracePeriodDays = 5,
    bouncedCheckFeeType = 'مبلغ ثابت',
    bouncedCheckFeeValue = 0,
    additionalFees,
    importantNotes,
    notes,
    products = [],
    partners = [],
    isActive = true
  } = req.body;

  console.log('🔍 Validation check:', {
    contractNumber: !!contractNumber,
    clientId: !!clientId,
    totalContractValue: !!totalContractValue,
    monthlyAmount: !!monthlyAmount
  });

  // Use notes directly
  const finalNotes = notes || null;

  // Simple validation
  if (!contractNumber || !clientId || !totalContractValue || !monthlyAmount) {
    console.error('❌ Validation failed:', {
      contractNumber,
      clientId,
      totalContractValue,
      monthlyAmount
    });
    return res.status(400).json({
      error: 'Required fields: contractNumber, clientId, totalContractValue, monthlyAmount'
    });
  }

  // Check if contract number already exists
  db.get("SELECT id FROM Contracts WHERE contractNumber = ?", [contractNumber], (err, existingContract) => {
    if (err) {
      console.error('Error checking existing contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingContract) {
      return res.status(400).json({ error: 'Contract number already exists' });
    }

    // Insert simple contract
    const contractSQL = `
      INSERT INTO Contracts (
        contractNumber, contractSubject, contractDescription, clientId, contractType, contractStatus,
        contractSigningDate, startDate, endDate, contractDurationYears, totalContractValue, monthlyAmount,
        paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod, finalInsuranceRate,
        finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount, responsibleDepartment,
        region, zone, assetOwner, checkStatus, lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType,
        bouncedCheckFeeValue, additionalFees, importantNotes, notes, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(contractSQL, [
      contractNumber, contractSubject, contractDescription, clientId, contractType || 'عقد إيجار', contractStatus,
      contractSigningDate, startDate, endDate, contractDurationYears, totalContractValue, monthlyAmount,
      paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod, finalInsuranceRate,
      finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount, responsibleDepartment,
      region, zone, assetOwner, checkStatus, lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType,
      bouncedCheckFeeValue, additionalFees, importantNotes, finalNotes, isActive ? 1 : 0
    ], function(err) {
      if (err) {
        console.error('Simple contract insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      const contractId = this.lastID;
      console.log('✅ Simple contract inserted with ID:', contractId);

      // Insert products if any
      if (products && products.length > 0) {
        insertContractProducts(contractId, products, (productErr) => {
          if (productErr) {
            console.error('Error inserting products:', productErr);
            return res.status(500).json({ error: productErr.message });
          }

          // Insert partners if any
          if (partners && partners.length > 0) {
            insertContractPartners(contractId, partners, (partnerErr) => {
              if (partnerErr) {
                console.error('Error inserting partners:', partnerErr);
                return res.status(500).json({ error: partnerErr.message });
              }

              addTimelineEntry(contractId, 'توقيع', contractSigningDate || startDate, 'تم إنشاء العقد');

              // Generate receivables for the contract
              generateContractReceivables({
                id: contractId,
                clientId,
                contractNumber,
                startDate,
                totalContractValue,
                paymentFrequency,
                contractDurationYears,
                contractStatus
              }, (receivablesErr, receivablesCount) => {
                if (receivablesErr) {
                  console.error('Error generating receivables:', receivablesErr);
                  // Don't fail the contract creation, just log the error
                } else {
                  console.log(`✅ Generated ${receivablesCount || 0} receivables for contract ${contractId}`);
                }

                // Send response after receivables generation (success or failure)
                res.json({ success: true, id: contractId, type: 'simple' });
              });
            });
          } else {
            addTimelineEntry(contractId, 'توقيع', contractSigningDate || startDate, 'تم إنشاء العقد');

            // Generate receivables for the contract
            generateContractReceivables({
              id: contractId,
              clientId,
              contractNumber,
              startDate,
              totalContractValue,
              paymentFrequency,
              contractDurationYears,
              contractStatus
            }, (receivablesErr, receivablesCount) => {
              if (receivablesErr) {
                console.error('Error generating receivables:', receivablesErr);
                // Don't fail the contract creation, just log the error
              } else {
                console.log(`✅ Generated ${receivablesCount || 0} receivables for contract ${contractId}`);
              }

              // Send response after receivables generation (success or failure)
              res.json({ success: true, id: contractId, type: 'simple' });
            });
          }
        });
      } else {
        // Insert partners if any
        if (partners && partners.length > 0) {
          insertContractPartners(contractId, partners, (partnerErr) => {
            if (partnerErr) {
              console.error('Error inserting partners:', partnerErr);
              return res.status(500).json({ error: partnerErr.message });
            }

            addTimelineEntry(contractId, 'توقيع', contractSigningDate || startDate, 'تم إنشاء العقد');

            // Generate receivables for the contract
            generateContractReceivables({
              id: contractId,
              clientId,
              contractNumber,
              startDate,
              totalContractValue,
              paymentFrequency,
              contractDurationYears,
              contractStatus
            }, (receivablesErr, receivablesCount) => {
              if (receivablesErr) {
                console.error('Error generating receivables:', receivablesErr);
                // Don't fail the contract creation, just log the error
              } else {
                console.log(`✅ Generated ${receivablesCount || 0} receivables for contract ${contractId}`);
              }

              // Send response after receivables generation (success or failure)
              res.json({ success: true, id: contractId, type: 'simple' });
            });
          });
        } else {
          addTimelineEntry(contractId, 'توقيع', contractSigningDate || startDate, 'تم إنشاء العقد');

          // Generate receivables for the contract
          generateContractReceivables({
            id: contractId,
            clientId,
            contractNumber,
            startDate,
            totalContractValue,
            paymentFrequency,
            contractDurationYears,
            contractStatus
          }, (receivablesErr, receivablesCount) => {
            if (receivablesErr) {
              console.error('Error generating receivables:', receivablesErr);
              // Don't fail the contract creation, just log the error
            } else {
              console.log(`✅ Generated ${receivablesCount || 0} receivables for contract ${contractId}`);
            }

            // Send response after receivables generation (success or failure)
            res.json({ success: true, id: contractId, type: 'simple' });
          });
        }
      }
    });
  });
}

// Basic Contract Handler (legacy support)
function handleBasicContract(req, res) {
  console.log('🔥 Basic Contract Handler (Legacy)');

  const {
    contractNumber,
    clientId,
    contractType,
    startDate,
    contractDurationYears,
    totalContractValue,
    monthlyAmount,
    paymentDay,
    lateFeePercentage = 0,
    bouncedCheckFee = 0,
    contractStatus = 'نشط',
    notes,
    isActive = true
  } = req.body;

  // Use notes directly
  const finalNotes = notes || null;

  // Basic validation
  if (!contractNumber || !clientId || !contractType || !startDate || !contractDurationYears || !totalContractValue || !monthlyAmount || !paymentDay) {
    return res.status(400).json({
      error: 'All required fields must be provided'
    });
  }

  // Check if contract number already exists
  db.get("SELECT id FROM Contracts WHERE contractNumber = ?", [contractNumber], (err, existingContract) => {
    if (err) {
      console.error('Error checking existing contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existingContract) {
      return res.status(400).json({ error: 'Contract number already exists' });
    }

    // Insert basic contract
    db.run(`
      INSERT INTO Contracts (
        contractNumber, clientId, contractType, startDate, contractDurationYears,
        totalContractValue, monthlyAmount, paymentDay, lateFeePercentage,
        bouncedCheckFee, contractStatus, notes, isActive
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      contractNumber, clientId, contractType, startDate, contractDurationYears,
      totalContractValue, monthlyAmount, paymentDay, lateFeePercentage,
      bouncedCheckFee, contractStatus, finalNotes, isActive ? 1 : 0
    ], function(err) {
      if (err) {
        console.error('Basic contract insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      const contractId = this.lastID;
      console.log('✅ Basic contract inserted with ID:', contractId);
      res.json({ success: true, id: contractId, type: 'basic' });
    });
  });
}

// Helper functions
function insertContractProducts(contractId, products, callback) {
  if (!products || products.length === 0) {
    return callback(null);
  }

  const productSQL = `
    INSERT INTO ContractProducts (
      contractId, productLabel, area, meterPrice, activationDate, endDate,
      billingType, irregularBillingMonths, taxInfo, taxRate,
      financialAccountingStartDate, financialAccountingEndDate, accountingDuration,
      hasAnnualIncrease, increaseStartYear, increaseType, increaseValue,
      customPaymentType, totalInstallments, customIntervals, monthsBetweenPayments
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  let insertedProducts = 0;
  products.forEach(product => {
    db.run(productSQL, [
      contractId,
      product.productLabel,
      product.area,
      product.meterPrice,
      product.activationDate,
      product.endDate,
      product.billingType,
      product.irregularBillingMonths || 0,
      product.taxInfo ? 1 : 0,
      product.taxRate || 0,
      product.financialAccountingStartDate,
      product.financialAccountingEndDate,
      product.accountingDuration || 0,
      product.hasAnnualIncrease ? 1 : 0,
      product.increaseStartYear || 2,
      product.increaseType || 'نسبة مئوية',
      product.increaseValue || 0,
      product.customPaymentType || '',
      product.totalInstallments || 0,
      product.customIntervals ? JSON.stringify(product.customIntervals) : '',
      product.monthsBetweenPayments || 0
    ], (err) => {
      if (err) {
        return callback(err);
      }

      insertedProducts++;
      if (insertedProducts === products.length) {
        callback(null);
      }
    });
  });
}

function insertContractPartners(contractId, partners, callback) {
  if (!partners || partners.length === 0) {
    return callback(null);
  }

  const partnerSQL = `
    INSERT INTO ContractPartners (contractId, partnerId, partnerType, partnershipPercentage)
    VALUES (?, ?, ?, ?)
  `;

  let insertedPartners = 0;
  partners.forEach(partner => {
    db.run(partnerSQL, [
      contractId,
      partner.partnerId,
      partner.partnerType || 'شريك',
      partner.partnershipPercentage || 0
    ], (err) => {
      if (err) {
        return callback(err);
      }

      insertedPartners++;
      if (insertedPartners === partners.length) {
        callback(null);
      }
    });
  });
}

function addTimelineEntry(contractId, eventType, eventDate, eventDescription) {
  db.run(`
    INSERT INTO ContractTimeline (contractId, eventType, eventDate, eventDescription)
    VALUES (?, ?, ?, ?)
  `, [contractId, eventType, eventDate || new Date().toISOString().split('T')[0], eventDescription], (err) => {
    if (err) console.error('Error inserting timeline:', err);
  });
}

// Payments API endpoints

// Update payment (record payment)
app.put('/api/payments/:id', (req, res) => {
  console.log('PUT /api/payments/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const {
    isPaid,
    paidDate,
    paymentMethod,
    checkNumber,
    bankName,
    lateFee = 0,
    bouncedCheckFee = 0,
    totalAmount,
    notes
  } = req.body;

  db.run(`
    UPDATE Payments SET
      isPaid=?, paidDate=?, paymentMethod=?, checkNumber=?, bankName=?,
      lateFee=?, bouncedCheckFee=?, totalAmount=?, notes=?, updatedAt=CURRENT_TIMESTAMP
    WHERE id=?
  `, [
    isPaid ? 1 : 0, paidDate, paymentMethod, checkNumber, bankName,
    lateFee, bouncedCheckFee, totalAmount, notes, id
  ], function(err) {
    if (err) {
      console.error('Update payment error:', err);
      res.status(500).json({ error: err.message });
    } else {
      console.log('Payment updated successfully, changes:', this.changes);
      res.json({ success: true, changes: this.changes });
    }
  });
});

// Update existing contract (Updated to use new simplified table)
app.put('/api/contracts/:id', (req, res) => {
  console.log('📝 PUT /api/contracts/:id request - using simplified table');
  console.log('📝 Contract ID:', req.params.id);

  const { id } = req.params;
  const {
    contractNumber,
    contractInternalId,
    contractDescription,
    contractSubject,
    clientId,
    contractType,
    contractStatus = 'نشط',
    contractDate,
    contractSigningDate,
    financialActivationDate,
    startDate,
    actualStartDate,
    endDate,
    actualEndDate,
    contractDurationYears,
    assetOwner,
    region,
    responsibleDepartment,
    ownershipPercentage = 100,
    financialGuarantorId,
    numberOfProducts = 1,
    hasUnifiedActivationDate = true,
    totalContractValue,
    monthlyAmount,
    paymentDay = 1,
    paymentFrequency = 'شهري',
    firstInstallmentDate,
    paymentMethod = 'تحويل بنكي',
    irregularPaymentMonths = 0,
    finalInsuranceRate = 0,
    finalInsuranceAmount = 0,
    advancePaymentMonths = 0,
    advancePaymentAmount = 0,
    lateFeeType = 'نسبة مئوية',
    lateFeeValue = 0,
    gracePeriodDays = 5,
    bouncedCheckFeeType = 'مبلغ ثابت',
    bouncedCheckFeeValue = 0,
    checkStatus = 'لم يتقدم بالشيكات',
    additionalFees,
    notes,
    importantNotes,
    parentVersionId,
    editCount = 0,
    editHistory,
    editReason,
    products = [],
    partners = [],
    isActive = true
  } = req.body;

  // Update contract in database using simplified schema
  const updateSQL = `
    UPDATE Contracts SET
      contractNumber = ?,
      contractInternalId = ?,
      contractSubject = ?,
      contractDescription = ?,
      contractType = ?,
      contractStatus = ?,
      clientId = ?,
      financialGuarantorId = ?,
      contractDate = ?,
      contractSigningDate = ?,
      financialActivationDate = ?,
      startDate = ?,
      actualStartDate = ?,
      endDate = ?,
      actualEndDate = ?,
      contractDurationYears = ?,
      assetOwner = ?,
      ownershipPercentage = ?,
      responsibleDepartment = ?,
      region = ?,
      totalContractValue = ?,
      monthlyAmount = ?,
      paymentDay = ?,
      paymentFrequency = ?,
      firstInstallmentDate = ?,
      paymentMethod = ?,
      irregularPaymentMonths = ?,
      finalInsuranceRate = ?,
      finalInsuranceAmount = ?,
      advancePaymentMonths = ?,
      advancePaymentAmount = ?,
      lateFeeType = ?,
      lateFeeValue = ?,
      gracePeriodDays = ?,
      bouncedCheckFeeType = ?,
      bouncedCheckFeeValue = ?,
      additionalFees = ?,
      numberOfProducts = ?,
      hasUnifiedActivationDate = ?,
      checkStatus = ?,
      notes = ?,
      importantNotes = ?,
      updatedAt = CURRENT_TIMESTAMP
    WHERE id = ?
  `;

  db.run(updateSQL, [
    contractNumber,
    contractInternalId || null,
    contractSubject,
    contractDescription,
    contractType,
    contractStatus,
    clientId,
    financialGuarantorId,
    contractDate,
    contractSigningDate,
    financialActivationDate,
    startDate,
    actualStartDate,
    endDate,
    actualEndDate,
    contractDurationYears,
    assetOwner,
    ownershipPercentage,
    responsibleDepartment,
    region,
    totalContractValue,
    monthlyAmount,
    paymentDay,
    paymentFrequency,
    firstInstallmentDate,
    paymentMethod,
    irregularPaymentMonths,
    finalInsuranceRate,
    finalInsuranceAmount,
    advancePaymentMonths,
    advancePaymentAmount,
    lateFeeType,
    lateFeeValue,
    gracePeriodDays,
    bouncedCheckFeeType,
    bouncedCheckFeeValue,
    JSON.stringify(additionalFees || []),
    numberOfProducts,
    hasUnifiedActivationDate ? 1 : 0,
    checkStatus,
    notes,
    importantNotes,
    id
  ], function(err) {
    if (err) {
      console.error('Contract update error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    console.log('✅ Contract updated successfully');

    // Now handle products and partners update
    const { products = [], partners = [] } = req.body;

    // Delete existing products and partners first
    db.run("DELETE FROM ContractProducts WHERE contractId = ?", [id], (deleteProductsErr) => {
      if (deleteProductsErr) {
        console.error('Error deleting existing products:', deleteProductsErr);
        return res.status(500).json({ error: deleteProductsErr.message });
      }

      db.run("DELETE FROM ContractPartners WHERE contractId = ?", [id], (deletePartnersErr) => {
        if (deletePartnersErr) {
          console.error('Error deleting existing partners:', deletePartnersErr);
          return res.status(500).json({ error: deletePartnersErr.message });
        }

        // Insert updated products if any
        if (products && products.length > 0) {
          insertContractProducts(id, products, (productErr) => {
            if (productErr) {
              console.error('Error inserting updated products:', productErr);
              return res.status(500).json({ error: productErr.message });
            }

            // Insert updated partners if any
            if (partners && partners.length > 0) {
              insertContractPartners(id, partners, (partnerErr) => {
                if (partnerErr) {
                  console.error('Error inserting updated partners:', partnerErr);
                  return res.status(500).json({ error: partnerErr.message });
                }

                console.log('✅ Contract, products, and partners updated successfully');
                res.json({
                  success: true,
                  message: 'Contract updated successfully',
                  contractId: id,
                  changes: this.changes
                });
              });
            } else {
              console.log('✅ Contract and products updated successfully');
              res.json({
                success: true,
                message: 'Contract updated successfully',
                contractId: id,
                changes: this.changes
              });
            }
          });
        } else {
          // Insert updated partners if any (no products case)
          if (partners && partners.length > 0) {
            insertContractPartners(id, partners, (partnerErr) => {
              if (partnerErr) {
                console.error('Error inserting updated partners:', partnerErr);
                return res.status(500).json({ error: partnerErr.message });
              }

              console.log('✅ Contract and partners updated successfully');
              res.json({
                success: true,
                message: 'Contract updated successfully',
                contractId: id,
                changes: this.changes
              });
            });
          } else {
            // No products or partners case
            console.log('✅ Contract updated successfully (no products/partners)');
            res.json({
              success: true,
              message: 'Contract updated successfully',
              contractId: id,
              changes: this.changes
            });
          }
        }
      });
    });
  });
});

// Create payment schedule for a contract
app.post('/api/contracts/:id/generate-payments', (req, res) => {
  console.log('POST /api/contracts/:id/generate-payments request');
  console.log('Contract ID:', req.params.id);

  const { id: contractId } = req.params;

  // First, get contract details
  db.get("SELECT * FROM Contracts WHERE id = ?", [contractId], (err, contract) => {
    if (err) {
      console.error('Error getting contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Check if payments already exist
    db.get("SELECT COUNT(*) as count FROM Payments WHERE contractId = ?", [contractId], (err, result) => {
      if (err) {
        console.error('Error checking existing payments:', err);
        return res.status(500).json({ error: err.message });
      }

      if (result.count > 0) {
        return res.status(400).json({ error: 'Payment schedule already exists for this contract' });
      }

      // Generate payment schedule
      const startDate = new Date(contract.startDate);
      const totalMonths = contract.contractDurationYears * 12;
      const payments = [];

      for (let i = 0; i < totalMonths; i++) {
        const dueDate = new Date(startDate);
        dueDate.setMonth(dueDate.getMonth() + i);
        dueDate.setDate(contract.paymentDay);

        payments.push([
          contractId,
          i + 1, // paymentNumber
          dueDate.toISOString().split('T')[0], // dueDate
          contract.monthlyAmount, // amount
          0, // lateFee
          0, // bouncedCheckFee
          contract.monthlyAmount // totalAmount
        ]);
      }

      // Insert all payments
      const insertSQL = `
        INSERT INTO Payments (contractId, paymentNumber, dueDate, amount, lateFee, bouncedCheckFee, totalAmount)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      let completed = 0;
      let hasError = false;

      payments.forEach((payment) => {
        db.run(insertSQL, payment, function(err) {
          if (err && !hasError) {
            hasError = true;
            console.error('Error inserting payment:', err);
            return res.status(500).json({ error: err.message });
          }

          completed++;
          if (completed === payments.length && !hasError) {
            console.log('Payment schedule generated successfully:', payments.length, 'payments');
            res.json({ success: true, paymentsCreated: payments.length });
          }
        });
      });
    });
  });
});

// Note: ContractFinancialDetails API endpoints removed as the table was duplicate

// ===== TREASURY PAYMENTS API ENDPOINTS =====

// Get all treasury payments
app.get('/api/treasury-payments', (req, res) => {
  console.log('GET /api/treasury-payments request');

  const query = `
    SELECT tp.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM TreasuryPayments tp
    LEFT JOIN Contracts c ON tp.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE tp.isActive = 1
    ORDER BY tp.createdAt DESC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting treasury payments:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(payment => ({
      ...payment,
      isActive: Boolean(payment.isActive),
      amount: parseFloat(payment.amount)
    }));

    console.log('Treasury payments retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get treasury payment by ID
app.get('/api/treasury-payments/:id', (req, res) => {
  console.log('GET /api/treasury-payments/:id request');
  const { id } = req.params;

  const query = `
    SELECT tp.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM TreasuryPayments tp
    LEFT JOIN Contracts c ON tp.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE tp.id = ? AND tp.isActive = 1
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error getting treasury payment:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Treasury payment not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive),
      amount: parseFloat(row.amount)
    };

    console.log('Treasury payment retrieved successfully');
    res.json(data);
  });
});

// Create treasury payment
app.post('/api/treasury-payments', (req, res) => {
  console.log('POST /api/treasury-payments request');
  console.log('Received data:', req.body);

  const {
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
    paymentMethod, checkNumber, checkDate, bankName, checkStatus, notes
  } = req.body;

  // Validate required fields
  if (!receiptNumber || !paymentDate || !amount || !paymentType || !paymentMethod) {
    return res.status(400).json({
      error: 'Missing required fields: receiptNumber, paymentDate, amount, paymentType, paymentMethod'
    });
  }

  // Check for duplicate receipt number
  db.get('SELECT id FROM TreasuryPayments WHERE receiptNumber = ? AND isActive = 1', [receiptNumber], (err, existing) => {
    if (err) {
      console.error('Error checking receipt number:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existing) {
      return res.status(400).json({ error: 'رقم الإيصال موجود مسبقاً' });
    }

    const insertSQL = `
      INSERT INTO TreasuryPayments (
        receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
        contractNumber, contractSubject, clientName, receivableDescription,
        receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
        paymentMethod, checkNumber, checkDate, bankName, checkStatus, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(insertSQL, [
      receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
      contractNumber, contractSubject, clientName, receivableDescription,
      receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
      paymentMethod, checkNumber, checkDate, bankName, checkStatus, notes
    ], function(err) {
      if (err) {
        console.error('Treasury payment insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log('Treasury payment created successfully, ID:', this.lastID);

      // If payment method is check, create a cheque record
      if (paymentMethod === 'شيك' && checkNumber) {
        const chequeSQL = `
          INSERT INTO Cheques (
            checkNumber, checkDate, amount, bankName, checkStatus,
            sourceType, sourcePaymentId, contractId, contractNumber,
            contractSubject, clientName, receivableId, receivableDescription,
            receivedDate, notes
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        db.run(chequeSQL, [
          checkNumber, checkDate, amount, bankName, checkStatus || 'عهدة',
          'treasury', this.lastID, contractId, contractNumber,
          contractSubject, clientName, receivableId, receivableDescription,
          paymentDate, notes
        ], function(chequeErr) {
          if (chequeErr) {
            console.error('Error creating cheque record:', chequeErr);
          } else {
            console.log('Cheque record created successfully, ID:', this.lastID);
          }
        });
      }

      res.json({
        success: true,
        id: this.lastID,
        message: 'تم تسجيل الدفعة بنجاح'
      });
    });
  });
});

// Update treasury payment
app.put('/api/treasury-payments/:id', (req, res) => {
  console.log('PUT /api/treasury-payments/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const {
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
    paymentMethod, checkNumber, checkDate, bankName, checkStatus, notes
  } = req.body;

  const updateSQL = `
    UPDATE TreasuryPayments SET
      receiptNumber=?, paymentDate=?, amount=?, paymentType=?, contractId=?, receivableId=?,
      contractNumber=?, contractSubject=?, clientName=?, receivableDescription=?,
      receivableStartDate=?, receivableEndDate=?, paymentStatus=?, nonContractType=?,
      paymentMethod=?, checkNumber=?, checkDate=?, bankName=?, checkStatus=?, notes=?,
      updatedAt=CURRENT_TIMESTAMP
    WHERE id=? AND isActive=1
  `;

  db.run(updateSQL, [
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
    paymentMethod, checkNumber, checkDate, bankName, checkStatus, notes, id
  ], function(err) {
    if (err) {
      console.error('Treasury payment update error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Treasury payment not found' });
    }

    console.log('Treasury payment updated successfully, changes:', this.changes);
    res.json({
      success: true,
      changes: this.changes,
      message: 'تم تحديث الدفعة بنجاح'
    });
  });
});

// Delete treasury payment (soft delete)
app.delete('/api/treasury-payments/:id', (req, res) => {
  console.log('DELETE /api/treasury-payments/:id request');
  const { id } = req.params;

  db.run('UPDATE TreasuryPayments SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?', [id], function(err) {
    if (err) {
      console.error('Treasury payment delete error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Treasury payment not found' });
    }

    console.log('Treasury payment deleted successfully');
    res.json({
      success: true,
      message: 'تم حذف الدفعة بنجاح'
    });
  });
});

// ===== BANK PAYMENTS API ENDPOINTS =====

// Get all bank payments
app.get('/api/bank-payments', (req, res) => {
  console.log('GET /api/bank-payments request');

  const query = `
    SELECT bp.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM BankPayments bp
    LEFT JOIN Contracts c ON bp.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE bp.isActive = 1
    ORDER BY bp.createdAt DESC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting bank payments:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(payment => ({
      ...payment,
      isActive: Boolean(payment.isActive),
      amount: parseFloat(payment.amount)
    }));

    console.log('Bank payments retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get bank payment by ID
app.get('/api/bank-payments/:id', (req, res) => {
  console.log('GET /api/bank-payments/:id request');
  const { id } = req.params;

  const query = `
    SELECT bp.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM BankPayments bp
    LEFT JOIN Contracts c ON bp.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE bp.id = ? AND bp.isActive = 1
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error getting bank payment:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Bank payment not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive),
      amount: parseFloat(row.amount)
    };

    console.log('Bank payment retrieved successfully');
    res.json(data);
  });
});

// Create bank payment
app.post('/api/bank-payments', (req, res) => {
  console.log('POST /api/bank-payments request');
  console.log('Received data:', req.body);

  const {
    bankTransactionNumber, paymentDate, amount, bankName, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
  } = req.body;

  // Validate required fields
  if (!bankTransactionNumber || !paymentDate || !amount || !bankName || !paymentType) {
    return res.status(400).json({
      error: 'Missing required fields: bankTransactionNumber, paymentDate, amount, bankName, paymentType'
    });
  }

  // Check for duplicate transaction number
  db.get('SELECT id FROM BankPayments WHERE bankTransactionNumber = ? AND isActive = 1', [bankTransactionNumber], (err, existing) => {
    if (err) {
      console.error('Error checking transaction number:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existing) {
      return res.status(400).json({ error: 'رقم الحركة البنكية موجود مسبقاً' });
    }

    const insertSQL = `
      INSERT INTO BankPayments (
        bankTransactionNumber, paymentDate, amount, bankName, paymentType, contractId, receivableId,
        contractNumber, contractSubject, clientName, receivableDescription,
        receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(insertSQL, [
      bankTransactionNumber, paymentDate, amount, bankName, paymentType, contractId, receivableId,
      contractNumber, contractSubject, clientName, receivableDescription,
      receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
    ], function(err) {
      if (err) {
        console.error('Bank payment insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log('Bank payment created successfully, ID:', this.lastID);

      // إذا كانت الدفعة مرتبطة بعقد، حدث حالة الاستحقاق
      if (paymentType === 'مرتبطة بعقد' && receivableId) {
        updateReceivableStatus(receivableId, amount, (updateErr) => {
          if (updateErr) {
            console.error('Error updating receivable status:', updateErr);
          } else {
            console.log('Receivable status updated successfully');
          }
        });
      }

      res.json({
        success: true,
        id: this.lastID,
        message: 'تم تسجيل الدفعة البنكية بنجاح'
      });
    });
  });
});

// Update bank payment
app.put('/api/bank-payments/:id', (req, res) => {
  console.log('PUT /api/bank-payments/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const {
    bankTransactionNumber, paymentDate, amount, bankName, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
  } = req.body;

  const updateSQL = `
    UPDATE BankPayments SET
      bankTransactionNumber=?, paymentDate=?, amount=?, bankName=?, paymentType=?, contractId=?, receivableId=?,
      contractNumber=?, contractSubject=?, clientName=?, receivableDescription=?,
      receivableStartDate=?, receivableEndDate=?, paymentStatus=?, nonContractType=?, notes=?,
      updatedAt=CURRENT_TIMESTAMP
    WHERE id=? AND isActive=1
  `;

  db.run(updateSQL, [
    bankTransactionNumber, paymentDate, amount, bankName, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes, id
  ], function(err) {
    if (err) {
      console.error('Bank payment update error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Bank payment not found' });
    }

    console.log('Bank payment updated successfully, changes:', this.changes);

    // إذا كانت الدفعة مرتبطة بعقد، حدث حالة الاستحقاق
    if (paymentType === 'مرتبطة بعقد' && receivableId) {
      updateReceivableStatus(receivableId, amount, (updateErr) => {
        if (updateErr) {
          console.error('Error updating receivable status:', updateErr);
        } else {
          console.log('Receivable status updated successfully');
        }
      });
    }

    res.json({
      success: true,
      changes: this.changes,
      message: 'تم تحديث الدفعة البنكية بنجاح'
    });
  });
});

// Delete bank payment (soft delete)
app.delete('/api/bank-payments/:id', (req, res) => {
  console.log('DELETE /api/bank-payments/:id request');
  const { id } = req.params;

  db.run('UPDATE BankPayments SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?', [id], function(err) {
    if (err) {
      console.error('Bank payment delete error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Bank payment not found' });
    }

    console.log('Bank payment deleted successfully');
    res.json({
      success: true,
      message: 'تم حذف الدفعة البنكية بنجاح'
    });
  });
});

// ===== CASH RECEIPTS API ENDPOINTS =====

// Get all cash receipts
app.get('/api/cash-receipts', (req, res) => {
  console.log('GET /api/cash-receipts request');

  const query = `
    SELECT cr.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM CashReceipts cr
    LEFT JOIN Contracts c ON cr.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE cr.isActive = 1
    ORDER BY cr.createdAt DESC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting cash receipts:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(receipt => ({
      ...receipt,
      isActive: Boolean(receipt.isActive),
      amount: parseFloat(receipt.amount)
    }));

    console.log('Cash receipts retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get cash receipt by ID
app.get('/api/cash-receipts/:id', (req, res) => {
  console.log('GET /api/cash-receipts/:id request');
  const { id } = req.params;

  const query = `
    SELECT cr.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM CashReceipts cr
    LEFT JOIN Contracts c ON cr.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE cr.id = ? AND cr.isActive = 1
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error getting cash receipt:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Cash receipt not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive),
      amount: parseFloat(row.amount)
    };

    console.log('Cash receipt retrieved successfully');
    res.json(data);
  });
});

// Create cash receipt
app.post('/api/cash-receipts', (req, res) => {
  console.log('POST /api/cash-receipts request');
  console.log('Received data:', req.body);

  const {
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
  } = req.body;

  // Validate required fields
  if (!receiptNumber || !paymentDate || !amount || !paymentType) {
    return res.status(400).json({
      error: 'Missing required fields: receiptNumber, paymentDate, amount, paymentType'
    });
  }

  // Check for duplicate receipt number
  db.get('SELECT id FROM CashReceipts WHERE receiptNumber = ? AND isActive = 1', [receiptNumber], (err, existing) => {
    if (err) {
      console.error('Error checking receipt number:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existing) {
      return res.status(400).json({ error: 'رقم الإيصال موجود مسبقاً' });
    }

    const insertSQL = `
      INSERT INTO CashReceipts (
        receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
        contractNumber, contractSubject, clientName, receivableDescription,
        receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(insertSQL, [
      receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
      contractNumber, contractSubject, clientName, receivableDescription,
      receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
    ], function(err) {
      if (err) {
        console.error('Cash receipt insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log('Cash receipt created successfully, ID:', this.lastID);

      // إذا كانت الدفعة مرتبطة بعقد، حدث حالة الاستحقاق
      if (paymentType === 'مرتبطة بعقد' && receivableId) {
        updateReceivableStatus(receivableId, amount, (updateErr) => {
          if (updateErr) {
            console.error('Error updating receivable status:', updateErr);
          } else {
            console.log('Receivable status updated successfully');
          }
        });
      }

      res.json({
        success: true,
        id: this.lastID,
        message: 'تم تسجيل الإيصال النقدي بنجاح'
      });
    });
  });
});

// Update cash receipt
app.put('/api/cash-receipts/:id', (req, res) => {
  console.log('PUT /api/cash-receipts/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const {
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes
  } = req.body;

  const updateSQL = `
    UPDATE CashReceipts SET
      receiptNumber=?, paymentDate=?, amount=?, paymentType=?, contractId=?, receivableId=?,
      contractNumber=?, contractSubject=?, clientName=?, receivableDescription=?,
      receivableStartDate=?, receivableEndDate=?, paymentStatus=?, nonContractType=?, notes=?,
      updatedAt=CURRENT_TIMESTAMP
    WHERE id=? AND isActive=1
  `;

  db.run(updateSQL, [
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType, notes, id
  ], function(err) {
    if (err) {
      console.error('Cash receipt update error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Cash receipt not found' });
    }

    console.log('Cash receipt updated successfully, changes:', this.changes);

    // إذا كانت الدفعة مرتبطة بعقد، حدث حالة الاستحقاق
    if (paymentType === 'مرتبطة بعقد' && receivableId) {
      updateReceivableStatus(receivableId, amount, (updateErr) => {
        if (updateErr) {
          console.error('Error updating receivable status:', updateErr);
        } else {
          console.log('Receivable status updated successfully');
        }
      });
    }

    res.json({
      success: true,
      changes: this.changes,
      message: 'تم تحديث الإيصال النقدي بنجاح'
    });
  });
});

// Delete cash receipt (soft delete)
app.delete('/api/cash-receipts/:id', (req, res) => {
  console.log('DELETE /api/cash-receipts/:id request');
  const { id } = req.params;

  db.run('UPDATE CashReceipts SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?', [id], function(err) {
    if (err) {
      console.error('Cash receipt delete error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Cash receipt not found' });
    }

    console.log('Cash receipt deleted successfully');
    res.json({
      success: true,
      message: 'تم حذف الإيصال النقدي بنجاح'
    });
  });
});

// ===== DASHBOARD API ENDPOINTS =====

// Get monthly revenue data for charts
app.get('/api/dashboard/monthly-revenue', (req, res) => {
  console.log('GET /api/dashboard/monthly-revenue request');

  // Function to calculate revenue for a specific month from receivables
  const calculateMonthlyRevenue = (year, month) => {
    return new Promise((resolve, reject) => {
      const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
      const endDate = new Date(year, month, 0).toISOString().split('T')[0]; // Last day of month

      // Get all receivables for active contracts
      const query = `
        SELECT
          r.id,
          r.amount,
          r.dueDate,
          r.paymentFrequency,
          c.contractStatus
        FROM ContractReceivables r
        JOIN Contracts c ON r.contractId = c.id
        WHERE c.contractStatus = 'نشط'
          AND c.isActive = 1
          AND r.isActive = 1
          AND r.dueDate >= ? AND r.dueDate <= ?
      `;

      db.all(query, [startDate, endDate], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        let monthRevenue = 0;

        // For each receivable in this month, add its amount as revenue
        rows.forEach(receivable => {
          monthRevenue += receivable.amount;
        });

        resolve(monthRevenue);
      });
    });
  };

  // Get revenue for last 6 months
  const currentDate = new Date();
  const months = [];
  const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                     'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

  for (let i = 5; i >= 0; i--) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
    months.push({
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      name: monthNames[date.getMonth()]
    });
  }

  Promise.all(months.map(m => calculateMonthlyRevenue(m.year, m.month)))
    .then(revenues => {
      const result = months.map((month, index) => ({
        month: month.name,
        revenue: Math.round(revenues[index] || 0),
        year: month.year
      }));

      console.log('Monthly revenue data:', result);
      res.json(result);
    })
    .catch(err => {
      console.error('Error calculating monthly revenue:', err);
      res.status(500).json({ error: 'Failed to calculate monthly revenue' });
    });
});

// Get contract status distribution for pie chart
app.get('/api/dashboard/contract-distribution', (req, res) => {
  console.log('GET /api/dashboard/contract-distribution request');

  const queries = [
    { name: 'نشطة', query: `SELECT COUNT(*) as count FROM Contracts WHERE contractStatus = 'نشط' AND isActive = 1`, color: '#10B981' },
    { name: 'منتهية', query: `SELECT COUNT(*) as count FROM Contracts WHERE contractStatus = 'منتهي' AND isActive = 1`, color: '#F59E0B' },
    { name: 'معلقة', query: `SELECT COUNT(*) as count FROM Contracts WHERE contractStatus = 'معلق' AND isActive = 1`, color: '#EF4444' },
    { name: 'مجددة', query: `SELECT COUNT(*) as count FROM Contracts WHERE contractStatus = 'مجدد' AND isActive = 1`, color: '#3B82F6' }
  ];

  let completed = 0;
  const results = [];

  queries.forEach((item, index) => {
    db.get(item.query, [], (err, row) => {
      if (err) {
        console.error(`Error in contract distribution query ${index}:`, err);
        results[index] = { name: item.name, value: 0, color: item.color };
      } else {
        results[index] = { name: item.name, value: row.count || 0, color: item.color };
      }

      completed++;
      if (completed === queries.length) {
        console.log('Contract distribution data:', results);
        res.json(results);
      }
    });
  });
});

// Get payment trends data for bar chart
app.get('/api/dashboard/payment-trends', (req, res) => {
  console.log('GET /api/dashboard/payment-trends request');

  // Get last 6 months payment data
  const currentDate = new Date();
  const months = [];
  const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                     'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

  for (let i = 5; i >= 0; i--) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
    const startDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-01`;
    const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0).toISOString().split('T')[0];

    months.push({
      name: monthNames[date.getMonth()],
      startDate,
      endDate
    });
  }

  let completed = 0;
  const results = [];

  months.forEach((month, index) => {
    // Get collected payments (treasury + bank)
    const collectedQuery = `
      SELECT
        COALESCE(SUM(cr.totalAmount), 0) + COALESCE(SUM(chr.totalAmount), 0) + COALESCE(SUM(bp.amount), 0) as collected
      FROM (
        SELECT totalAmount, receiptDate FROM CashReceipts
        WHERE receiptDate >= ? AND receiptDate <= ? AND isActive = 1
        UNION ALL
        SELECT totalAmount, receiptDate FROM ChequeReceipts
        WHERE receiptDate >= ? AND receiptDate <= ? AND isActive = 1
      ) cr
      LEFT JOIN BankPayments bp ON bp.paymentDate >= ? AND bp.paymentDate <= ? AND bp.isActive = 1
    `;

    // Get outstanding receivables for the month
    const outstandingQuery = `
      SELECT COALESCE(SUM(r.amount - COALESCE(r.paidAmount, 0)), 0) as outstanding
      FROM ContractReceivables r
      JOIN Contracts c ON r.contractId = c.id
      WHERE c.contractStatus = 'نشط'
        AND c.isActive = 1
        AND r.isActive = 1
        AND r.dueDate >= ? AND r.dueDate <= ?
        AND r.status IN ('مستحق', 'متأخر')
    `;

    db.get(collectedQuery, [month.startDate, month.endDate, month.startDate, month.endDate, month.startDate, month.endDate], (err, collectedRow) => {
      if (err) {
        console.error('Error in collected payments query:', err);
        results[index] = { month: month.name, collected: 0, outstanding: 0 };
        completed++;
        if (completed === months.length) {
          res.json(results);
        }
        return;
      }

      db.get(outstandingQuery, [month.startDate, month.endDate], (err, outstandingRow) => {
        if (err) {
          console.error('Error in outstanding payments query:', err);
          results[index] = { month: month.name, collected: collectedRow.collected || 0, outstanding: 0 };
        } else {
          results[index] = {
            month: month.name,
            collected: Math.round(collectedRow.collected || 0),
            outstanding: Math.round(outstandingRow.outstanding || 0)
          };
        }

        completed++;
        if (completed === months.length) {
          console.log('Payment trends data:', results);
          res.json(results);
        }
      });
    });
  });
});

// Get dashboard statistics
app.get('/api/dashboard/stats', (req, res) => {
  console.log('GET /api/dashboard/stats request');

  // Get all stats in parallel
  const queries = {
    totalClients: `SELECT COUNT(*) as count FROM Clients WHERE isActive = 1`,
    activeContracts: `SELECT COUNT(*) as count FROM Contracts WHERE contractStatus = 'نشط' AND isActive = 1`,
    totalContracts: `SELECT COUNT(*) as count FROM Contracts WHERE isActive = 1`,
    inactiveContracts: `SELECT COUNT(*) as count FROM Contracts WHERE contractStatus = 'غير نشط' AND isActive = 1`,
    expiredContracts: `SELECT COUNT(*) as count FROM Contracts WHERE contractStatus = 'منتهي' AND isActive = 1`,
    totalRevenue: `SELECT COALESCE(SUM(totalContractValue), 0) as total FROM Contracts WHERE isActive = 1`,
    outstandingAmount: `SELECT COALESCE(SUM(amount), 0) as total FROM ContractReceivables WHERE status IN ('مستحق', 'متاخر') AND isActive = 1`,
    overduePayments: `SELECT COUNT(*) as count FROM ContractReceivables WHERE status = 'متاخر' AND isActive = 1`,
    collectedAmount: `SELECT COALESCE(SUM(amount), 0) as total FROM ContractReceivables WHERE status = 'مسدد' AND isActive = 1`,
    pendingAmount: `SELECT COALESCE(SUM(amount), 0) as total FROM ContractReceivables WHERE status = 'لم يحن موعد استحقاقه' AND isActive = 1`
  };

  const stats = {};
  let completed = 0;
  const total = Object.keys(queries).length;

  Object.entries(queries).forEach(([key, query]) => {
    db.get(query, [], (err, row) => {
      if (err) {
        console.error(`Error in ${key} query:`, err);
        stats[key] = 0;
      } else {
        stats[key] = row.count || row.total || 0;
      }

      completed++;
      if (completed === total) {
        console.log('Dashboard stats retrieved successfully:', stats);
        res.json(stats);
      }
    });
  });
});

// Get recent activities
app.get('/api/dashboard/recent-activities', (req, res) => {
  console.log('GET /api/dashboard/recent-activities request');

  const query = `
    SELECT
      'contract' as type,
      c.id,
      c.contractNumber as reference,
      c.contractSubject as title,
      cl.clientName as clientName,
      c.createdAt as date,
      c.contractStatus as status
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE c.isActive = 1
    ORDER BY c.createdAt DESC
    LIMIT 10
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting recent activities:', err);
      return res.status(500).json({ error: err.message });
    }

    console.log('Recent activities retrieved successfully:', rows.length, 'items');
    res.json(rows);
  });
});

// Get overdue payments
app.get('/api/dashboard/overdue-payments', (req, res) => {
  console.log('GET /api/dashboard/overdue-payments request');

  const query = `
    SELECT
      cr.*,
      c.contractNumber,
      c.contractSubject,
      cl.clientName
    FROM ContractReceivables cr
    LEFT JOIN Contracts c ON cr.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE cr.status = 'متاخر' AND cr.isActive = 1
    ORDER BY cr.dueDate ASC
    LIMIT 10
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting overdue payments:', err);
      return res.status(500).json({ error: err.message });
    }

    const data = rows.map(payment => ({
      ...payment,
      amount: parseFloat(payment.amount)
    }));

    console.log('Overdue payments retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get contracts expiring soon
app.get('/api/dashboard/expiring-contracts', (req, res) => {
  console.log('GET /api/dashboard/expiring-contracts request');

  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
  const dateString = thirtyDaysFromNow.toISOString().split('T')[0];

  const query = `
    SELECT
      c.*,
      cl.clientName
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE c.endDate <= ?
      AND c.contractStatus = 'نشط'
      AND c.isActive = 1
    ORDER BY c.endDate ASC
    LIMIT 10
  `;

  db.all(query, [dateString], (err, rows) => {
    if (err) {
      console.error('Error getting expiring contracts:', err);
      return res.status(500).json({ error: err.message });
    }

    console.log('Expiring contracts retrieved successfully:', rows.length, 'items');
    res.json(rows);
  });
});

// ===== OLD CHEQUES API ENDPOINTS (REMOVED) =====

// ===== CHEQUE RECEIPTS API ENDPOINTS =====

// Get all cheque receipts
app.get('/api/cheque-receipts', (req, res) => {
  console.log('GET /api/cheque-receipts request');

  const query = `
    SELECT chr.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM ChequeReceipts chr
    LEFT JOIN Contracts c ON chr.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE chr.isActive = 1
    ORDER BY chr.createdAt DESC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting cheque receipts:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    const data = rows.map(receipt => ({
      ...receipt,
      isActive: Boolean(receipt.isActive),
      amount: parseFloat(receipt.amount)
    }));

    console.log('Cheque receipts retrieved successfully:', data.length, 'items');
    res.json(data);
  });
});

// Get cheque receipt by ID
app.get('/api/cheque-receipts/:id', (req, res) => {
  console.log('GET /api/cheque-receipts/:id request');
  const { id } = req.params;

  const query = `
    SELECT chr.*, c.contractNumber as linkedContractNumber, c.contractSubject as linkedContractSubject,
           cl.clientName as linkedClientName
    FROM ChequeReceipts chr
    LEFT JOIN Contracts c ON chr.contractId = c.id
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE chr.id = ? AND chr.isActive = 1
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error getting cheque receipt:', err);
      res.status(500).json({ error: err.message });
      return;
    }

    if (!row) {
      return res.status(404).json({ error: 'Cheque receipt not found' });
    }

    const data = {
      ...row,
      isActive: Boolean(row.isActive),
      amount: parseFloat(row.amount)
    };

    console.log('Cheque receipt retrieved successfully');
    res.json(data);
  });
});

// Create cheque receipt
app.post('/api/cheque-receipts', (req, res) => {
  console.log('POST /api/cheque-receipts request');
  console.log('Received data:', req.body);

  const {
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
    checkNumber, checkDate, bankName, checkStatus, notes
  } = req.body;

  // Validate required fields
  if (!receiptNumber || !paymentDate || !amount || !paymentType || !checkNumber || !checkDate || !bankName) {
    return res.status(400).json({
      error: 'Missing required fields: receiptNumber, paymentDate, amount, paymentType, checkNumber, checkDate, bankName'
    });
  }

  // Check for duplicate receipt number
  db.get('SELECT id FROM ChequeReceipts WHERE receiptNumber = ? AND isActive = 1', [receiptNumber], (err, existing) => {
    if (err) {
      console.error('Error checking receipt number:', err);
      return res.status(500).json({ error: err.message });
    }

    if (existing) {
      return res.status(400).json({ error: 'رقم الإيصال موجود مسبقاً' });
    }

    const insertSQL = `
      INSERT INTO ChequeReceipts (
        receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
        contractNumber, contractSubject, clientName, receivableDescription,
        receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
        checkNumber, checkDate, bankName, checkStatus, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    db.run(insertSQL, [
      receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
      contractNumber, contractSubject, clientName, receivableDescription,
      receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
      checkNumber, checkDate, bankName, checkStatus || 'عهدة', notes
    ], function(err) {
      if (err) {
        console.error('Cheque receipt insert error:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log('Cheque receipt created successfully, ID:', this.lastID);

      // Create a cheque record in the Cheques table
      const chequeSQL = `
        INSERT INTO Cheques (
          checkNumber, checkDate, amount, bankName, checkStatus,
          sourceType, sourcePaymentId, contractId, contractNumber,
          contractSubject, clientName, receivableId, receivableDescription,
          receivedDate, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      db.run(chequeSQL, [
        checkNumber, checkDate, amount, bankName, checkStatus || 'عهدة',
        'cheque_receipt', this.lastID, contractId, contractNumber,
        contractSubject, clientName, receivableId, receivableDescription,
        paymentDate, notes
      ], function(chequeErr) {
        if (chequeErr) {
          console.error('Error creating cheque record:', chequeErr);
        } else {
          console.log('Cheque record created successfully, ID:', this.lastID);
        }
      });

      // إذا كانت الدفعة مرتبطة بعقد، حدث حالة الاستحقاق
      if (paymentType === 'مرتبطة بعقد' && receivableId) {
        updateReceivableStatus(receivableId, amount, (updateErr) => {
          if (updateErr) {
            console.error('Error updating receivable status:', updateErr);
          } else {
            console.log('Receivable status updated successfully');
          }
        });
      }

      res.json({
        success: true,
        id: this.lastID,
        message: 'تم تسجيل إيصال الشيك بنجاح'
      });
    });
  });
});

// Update cheque receipt
app.put('/api/cheque-receipts/:id', (req, res) => {
  console.log('PUT /api/cheque-receipts/:id request');
  console.log('ID:', req.params.id);
  console.log('Received data:', req.body);

  const { id } = req.params;
  const {
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
    checkNumber, checkDate, bankName, checkStatus, notes
  } = req.body;

  const updateSQL = `
    UPDATE ChequeReceipts SET
      receiptNumber=?, paymentDate=?, amount=?, paymentType=?, contractId=?, receivableId=?,
      contractNumber=?, contractSubject=?, clientName=?, receivableDescription=?,
      receivableStartDate=?, receivableEndDate=?, paymentStatus=?, nonContractType=?,
      checkNumber=?, checkDate=?, bankName=?, checkStatus=?, notes=?,
      updatedAt=CURRENT_TIMESTAMP
    WHERE id=? AND isActive=1
  `;

  db.run(updateSQL, [
    receiptNumber, paymentDate, amount, paymentType, contractId, receivableId,
    contractNumber, contractSubject, clientName, receivableDescription,
    receivableStartDate, receivableEndDate, paymentStatus, nonContractType,
    checkNumber, checkDate, bankName, checkStatus, notes, id
  ], function(err) {
    if (err) {
      console.error('Cheque receipt update error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Cheque receipt not found' });
    }

    console.log('Cheque receipt updated successfully, changes:', this.changes);
    res.json({
      success: true,
      changes: this.changes,
      message: 'تم تحديث إيصال الشيك بنجاح'
    });
  });
});

// Delete cheque receipt (soft delete)
app.delete('/api/cheque-receipts/:id', (req, res) => {
  console.log('DELETE /api/cheque-receipts/:id request');
  const { id } = req.params;

  db.run('UPDATE ChequeReceipts SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?', [id], function(err) {
    if (err) {
      console.error('Cheque receipt delete error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Cheque receipt not found' });
    }

    console.log('Cheque receipt deleted successfully');
    res.json({
      success: true,
      message: 'تم حذف إيصال الشيك بنجاح'
    });
  });
});

// Enhanced Contracts API endpoints

// Test endpoint
app.post('/api/test', (req, res) => {
  console.log('🧪 TEST endpoint called!');
  console.log('🧪 Received data:', req.body);
  res.json({ success: true, message: 'Test successful!' });
});

// ===== DEPRECATED ENDPOINTS (Redirected to unified endpoint) =====

// Legacy simple endpoint - redirects to unified
app.post('/api/contracts/simple', (req, res) => {
  console.log('🔄 Redirecting /api/contracts/simple to unified endpoint');
  req.url = '/api/contracts';
  return handleSimpleContract(req, res);
});

// Legacy enhanced endpoint - redirects to unified
app.post('/api/contracts/enhanced', (req, res) => {
  console.log('🔄 Redirecting /api/contracts/enhanced to unified endpoint');
  req.url = '/api/contracts';
  return handleEnhancedContract(req, res);
});





// Get contract versions/history
app.get('/api/contracts/:id/versions', (req, res) => {
  console.log('GET /api/contracts/:id/versions request');
  const { id } = req.params;

  // Get the original contract number first
  db.get("SELECT originalContractNumber, contractNumber FROM Contracts WHERE id = ?", [id], (err, mainContract) => {
    if (err) {
      console.error('Error fetching main contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!mainContract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    const originalNumber = mainContract.originalContractNumber || mainContract.contractNumber;

    // Get all versions of this contract
    const query = `
      SELECT
        id, contractNumber, versionNumber, isCurrentVersion,
        editCount, editHistory, editReason, createdAt, updatedAt,
        contractDescription, contractSubject, contractStatus
      FROM Contracts
      WHERE originalContractNumber = ? OR contractNumber = ?
      ORDER BY versionNumber DESC, createdAt DESC
    `;

    db.all(query, [originalNumber, originalNumber], (err, versions) => {
      if (err) {
        console.error('Error fetching contract versions:', err);
        return res.status(500).json({ error: err.message });
      }

      // Parse edit history for each version
      const versionsWithHistory = versions.map(version => ({
        ...version,
        editHistory: version.editHistory ? JSON.parse(version.editHistory) : [],
        isCurrentVersion: Boolean(version.isCurrentVersion)
      }));

      res.json(versionsWithHistory);
    });
  });
});

// Get contract products
app.get('/api/contracts/:id/products', (req, res) => {
  console.log('GET /api/contracts/:id/products request');
  const { id } = req.params;

  db.all("SELECT * FROM ContractProducts WHERE contractId = ? AND isActive = 1", [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract products:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get contract partners
app.get('/api/contracts/:id/partners', (req, res) => {
  console.log('GET /api/contracts/:id/partners request');
  const { id } = req.params;

  const query = `
    SELECT cp.*, c.clientName, c.clientType
    FROM ContractPartners cp
    LEFT JOIN Clients c ON cp.partnerId = c.id
    WHERE cp.contractId = ? AND cp.isActive = 1
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract partners:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get contract attachments
app.get('/api/contracts/:id/attachments', (req, res) => {
  console.log('GET /api/contracts/:id/attachments request');
  const { id } = req.params;

  db.all("SELECT * FROM ContractAttachments WHERE contractId = ? AND isActive = 1 ORDER BY createdAt DESC", [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract attachments:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Upload contract attachment
app.post('/api/contracts/:id/attachments', (req, res) => {
  console.log('POST /api/contracts/:id/attachments request');
  const { id } = req.params;
  const { fileName, fileType, fileSize, filePath, uploadedBy } = req.body;

  if (!fileName || !fileType || !filePath) {
    return res.status(400).json({ error: 'File name, type, and path are required' });
  }

  db.run(`
    INSERT INTO ContractAttachments (contractId, fileName, fileType, fileSize, filePath, uploadedBy)
    VALUES (?, ?, ?, ?, ?, ?)
  `, [id, fileName, fileType, fileSize || 0, filePath, uploadedBy || 'System'], function(err) {
    if (err) {
      console.error('Error uploading attachment:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json({
      success: true,
      id: this.lastID,
      message: 'Attachment uploaded successfully'
    });
  });
});

// Delete contract attachment
app.delete('/api/contracts/:contractId/attachments/:attachmentId', (req, res) => {
  console.log('DELETE /api/contracts/:contractId/attachments/:attachmentId request');
  const { attachmentId } = req.params;

  db.run("UPDATE ContractAttachments SET isActive = 0 WHERE id = ?", [attachmentId], function(err) {
    if (err) {
      console.error('Error deleting attachment:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  });
});

// Get contract revenue recognition
app.get('/api/contracts/:id/revenue-recognition', (req, res) => {
  console.log('GET /api/contracts/:id/revenue-recognition request');
  const { id } = req.params;

  const query = `
    SELECT rr.*, ci.installmentNumber, ci.installmentDate, ci.installmentAmount
    FROM RevenueRecognition rr
    LEFT JOIN ContractInstallments ci ON rr.installmentId = ci.installmentId
    WHERE rr.contractId = ? AND rr.isActive = 1
    ORDER BY rr.fiscalYear, rr.fiscalMonth
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      console.error('Error getting revenue recognition:', err);
      return res.status(500).json({ error: err.message });
    }

    // Group by fiscal year for better presentation
    const groupedByYear = rows.reduce((acc, row) => {
      const year = row.fiscalYear;
      if (!acc[year]) {
        acc[year] = {
          fiscalYear: year,
          totalRecognized: 0,
          months: []
        };
      }

      acc[year].totalRecognized += parseFloat(row.recognizedAmount || 0);
      acc[year].months.push({
        ...row,
        recognizedAmount: parseFloat(row.recognizedAmount || 0),
        cumulativeAmount: parseFloat(row.cumulativeAmount || 0),
        installmentAmount: parseFloat(row.installmentAmount || 0)
      });

      return acc;
    }, {});

    res.json({
      byYear: Object.values(groupedByYear),
      total: rows.reduce((sum, row) => sum + parseFloat(row.recognizedAmount || 0), 0)
    });
  });
});

// Generate revenue recognition for contract
app.post('/api/contracts/:id/generate-revenue-recognition', (req, res) => {
  console.log('POST /api/contracts/:id/generate-revenue-recognition request');
  const { id } = req.params;

  // First, get contract installments
  db.all("SELECT * FROM ContractInstallments WHERE contractId = ? AND isActive = 1 ORDER BY installmentDate", [id], (err, installments) => {
    if (err) {
      console.error('Error getting installments:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!installments || installments.length === 0) {
      return res.status(400).json({ error: 'No installments found for this contract' });
    }

    // Clear existing revenue recognition
    db.run("UPDATE RevenueRecognition SET isActive = 0 WHERE contractId = ?", [id], (clearErr) => {
      if (clearErr) {
        console.error('Error clearing existing revenue recognition:', clearErr);
        return res.status(500).json({ error: clearErr.message });
      }

      // Generate new revenue recognition entries
      let insertedCount = 0;
      let cumulativeAmount = 0;

      installments.forEach(installment => {
        const installmentDate = new Date(installment.installmentDate);
        const fiscalYear = installmentDate.getFullYear();
        const fiscalMonth = installmentDate.getMonth() + 1;
        const recognizedAmount = parseFloat(installment.installmentAmount || 0);
        cumulativeAmount += recognizedAmount;

        db.run(`
          INSERT INTO RevenueRecognition (
            contractId, installmentId, fiscalYear, fiscalMonth,
            recognizedAmount, cumulativeAmount, recognitionDate
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          id, installment.installmentId, fiscalYear, fiscalMonth,
          recognizedAmount, cumulativeAmount, installment.installmentDate
        ], function(insertErr) {
          if (insertErr) {
            console.error('Error inserting revenue recognition:', insertErr);
          } else {
            insertedCount++;
            if (insertedCount === installments.length) {
              res.json({
                success: true,
                message: 'Revenue recognition generated successfully',
                entriesCreated: insertedCount
              });
            }
          }
        });
      });
    });
  });
});

// Get contract timeline
app.get('/api/contracts/:id/timeline', (req, res) => {
  console.log('GET /api/contracts/:id/timeline request');
  const { id } = req.params;

  db.all("SELECT * FROM ContractTimeline WHERE contractId = ? AND isActive = 1 ORDER BY eventDate", [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract timeline:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get contract alerts
app.get('/api/contracts/:id/alerts', (req, res) => {
  console.log('GET /api/contracts/:id/alerts request');
  const { id } = req.params;

  db.all("SELECT * FROM ContractAlerts WHERE contractId = ? ORDER BY alertDate DESC", [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract alerts:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Contract Calculations API

// Generate installment schedule for a contract
app.post('/api/contracts/:id/generate-installments', (req, res) => {
  console.log('POST /api/contracts/:id/generate-installments request');
  const { id } = req.params;

  // Get contract details with products
  const contractQuery = `
    SELECT c.*, cl.clientName
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    WHERE c.id = ?
  `;

  db.get(contractQuery, [id], (err, contract) => {
    if (err) {
      console.error('Error getting contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Get contract products
    db.all("SELECT * FROM ContractProducts WHERE contractId = ? AND isActive = 1", [id], (err, products) => {
      if (err) {
        console.error('Error getting contract products:', err);
        return res.status(500).json({ error: err.message });
      }

      // Calculate installments using the calculation engine
      // For now, let's create a simple calculation
      const installments = [];
      const startDate = new Date(contract.startDate);
      const endDate = new Date(contract.endDate);
      const monthlyAmount = contract.monthlyAmount;
      const paymentDay = contract.paymentDay;

      let currentDate = new Date(startDate);
      let installmentNumber = 1;

      while (currentDate <= endDate) {
        const dueDate = new Date(currentDate);
        dueDate.setDate(paymentDay);
        if (dueDate < currentDate) {
          dueDate.setMonth(dueDate.getMonth() + 1);
        }

        const yearOfContract = Math.floor((installmentNumber - 1) / 12) + 1;

        // Apply annual increase
        let adjustedAmount = monthlyAmount;
        if (contract.annualIncreaseType === 'مبلغ ثابت' && yearOfContract >= contract.annualIncreaseStartYear) {
          adjustedAmount += contract.annualIncreaseValue * (yearOfContract - contract.annualIncreaseStartYear + 1);
        } else if (contract.annualIncreaseType === 'نسبة مئوية' && yearOfContract >= contract.annualIncreaseStartYear) {
          adjustedAmount += monthlyAmount * (contract.annualIncreaseValue / 100) * (yearOfContract - contract.annualIncreaseStartYear + 1);
        } else if (contract.annualIncreaseType === 'نسبة مركبة' && yearOfContract >= contract.annualIncreaseStartYear) {
          adjustedAmount = monthlyAmount * Math.pow(1 + (contract.annualIncreaseValue / 100), yearOfContract - contract.annualIncreaseStartYear + 1);
        }

        installments.push({
          contractId: id,
          installmentNumber,
          installmentDate: currentDate.toISOString().split('T')[0],
          paymentDueDate: dueDate.toISOString().split('T')[0],
          baseAmount: adjustedAmount,
          taxAmount: 0,
          installmentAmount: adjustedAmount,
          yearOfContract,
          remainingAmount: adjustedAmount,
          isPaid: 0,
          paidAmount: 0,
          penaltyAmount: 0,
          lateDays: 0,
        });

        currentDate.setMonth(currentDate.getMonth() + 1);
        installmentNumber++;
      }

      // Save installments to database
      const insertSQL = `
        INSERT INTO ContractInstallments (
          contractId, installmentNumber, installmentDate, installmentAmount,
          baseAmount, taxAmount, yearOfContract, paymentDueDate, remainingAmount,
          isPaid, paidAmount, penaltyAmount, lateDays
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      // Clear existing installments first
      db.run("DELETE FROM ContractInstallments WHERE contractId = ?", [id], (err) => {
        if (err) {
          console.error('Error clearing existing installments:', err);
          return res.status(500).json({ error: err.message });
        }

        // Insert new installments
        let insertedCount = 0;
        const totalInstallments = installments.length;

        if (totalInstallments === 0) {
          return res.json({ success: true, installments: [], message: 'No installments to generate' });
        }

        installments.forEach((installment) => {
          db.run(insertSQL, [
            installment.contractId,
            installment.installmentNumber,
            installment.installmentDate,
            installment.installmentAmount,
            installment.baseAmount,
            installment.taxAmount,
            installment.yearOfContract,
            installment.paymentDueDate,
            installment.remainingAmount,
            installment.isPaid,
            installment.paidAmount,
            installment.penaltyAmount,
            installment.lateDays
          ], function(err) {
            if (err) {
              console.error('Error inserting installment:', err);
            } else {
              insertedCount++;
              if (insertedCount === totalInstallments) {
                // Add timeline entry
                db.run(`
                  INSERT INTO ContractTimeline (contractId, eventType, eventDate, eventDescription)
                  VALUES (?, 'تعديل', ?, 'تم إنشاء جدول الأقساط')
                `, [id, new Date().toISOString().split('T')[0]], (err) => {
                  if (err) console.error('Error inserting timeline:', err);
                });

                res.json({
                  success: true,
                  installments,
                  message: `تم إنشاء ${totalInstallments} قسط بنجاح`
                });
              }
            }
          });
        });
      });
    });
  });
});

// Get contract installments
app.get('/api/contracts/:id/installments', (req, res) => {
  console.log('GET /api/contracts/:id/installments request');
  const { id } = req.params;

  const query = `
    SELECT * FROM ContractInstallments
    WHERE contractId = ?
    ORDER BY installmentNumber
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      console.error('Error getting installments:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get contract receivables (استحقاقات العقد)
app.get('/api/contracts/:id/receivables', (req, res) => {
  console.log('GET /api/contracts/:id/receivables request');
  const { id } = req.params;

  // Update receivable statuses first
  updateReceivableStatuses();

  const query = `
    SELECT
      r.*,
      c.contractNumber,
      cl.clientName,
      ci.isPaid as installmentPaid,
      ci.paidAmount,
      ci.remainingAmount as installmentRemaining,
      ci.paidDate as installmentPaidDate,
      CASE
        WHEN ci.isPaid = 1 THEN 'مدفوع'
        WHEN r.dueDate < date('now') AND (ci.isPaid IS NULL OR ci.isPaid = 0) THEN 'متأخر'
        WHEN r.dueDate <= date('now') AND (ci.isPaid IS NULL OR ci.isPaid = 0) THEN 'مستحق'
        ELSE 'لم يحن موعده'
      END as calculatedStatus,
      (julianday('now') - julianday(r.dueDate)) as daysFromDue
    FROM ContractReceivables r
    LEFT JOIN Contracts c ON r.contractId = c.id
    LEFT JOIN Clients cl ON r.clientId = cl.id
    LEFT JOIN ContractInstallments ci ON r.contractId = ci.contractId
      AND r.installmentNumber = ci.installmentNumber
    WHERE r.contractId = ? AND r.isActive = 1
    ORDER BY r.installmentNumber
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      console.error('Error getting contract receivables:', err);
      return res.status(500).json({ error: err.message });
    }

    // Process the data to add calculated fields
    const processedRows = rows.map(row => ({
      ...row,
      isPaid: row.installmentPaid === 1,
      paidAmount: row.paidAmount || 0,
      remainingAmount: row.installmentRemaining || row.amount,
      paidDate: row.installmentPaidDate,
      status: row.calculatedStatus,
      daysOverdue: row.calculatedStatus === 'متأخر' ? Math.max(0, Math.floor(row.daysFromDue)) : 0
    }));

    res.json(processedRows);
  });
});

// Update installment payment
app.put('/api/installments/:id/payment', (req, res) => {
  console.log('PUT /api/installments/:id/payment request');
  const { id } = req.params;
  const { paidAmount, paymentDate, paymentMethod, checkNumber, bankName, notes } = req.body;

  // Get installment details first
  db.get("SELECT * FROM ContractInstallments WHERE installmentId = ?", [id], (err, installment) => {
    if (err) {
      console.error('Error getting installment:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!installment) {
      return res.status(404).json({ error: 'Installment not found' });
    }

    // Calculate late days and penalty
    const dueDate = new Date(installment.paymentDueDate);
    const payDate = new Date(paymentDate);
    const lateDays = Math.max(0, Math.ceil((payDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)));

    // Get contract details for penalty calculation
    db.get("SELECT * FROM Contracts WHERE id = ?", [installment.contractId], (err, contract) => {
      if (err) {
        console.error('Error getting contract:', err);
        return res.status(500).json({ error: err.message });
      }

      let penaltyAmount = 0;
      if (lateDays > contract.gracePeriodDays) {
        const effectiveLateDays = lateDays - contract.gracePeriodDays;
        if (contract.lateFeeType === 'مبلغ ثابت') {
          penaltyAmount = contract.lateFeeValue * effectiveLateDays;
        } else if (contract.lateFeeType === 'نسبة مئوية') {
          penaltyAmount = installment.installmentAmount * (contract.lateFeeValue / 100) * effectiveLateDays;
        }
      }

      const remainingAmount = Math.max(0, installment.installmentAmount - paidAmount);
      const isPaid = paidAmount >= installment.installmentAmount ? 1 : 0;

      // Update installment
      db.run(`
        UPDATE ContractInstallments
        SET paidAmount = ?, remainingAmount = ?, isPaid = ?, paidDate = ?,
            lateDays = ?, penaltyAmount = ?, paymentMethod = ?, checkNumber = ?,
            bankName = ?, notes = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE installmentId = ?
      `, [
        paidAmount, remainingAmount, isPaid, paymentDate,
        lateDays, penaltyAmount, paymentMethod, checkNumber,
        bankName, notes, id
      ], function(err) {
        if (err) {
          console.error('Error updating installment:', err);
          return res.status(500).json({ error: err.message });
        }

        // Add timeline entry
        db.run(`
          INSERT INTO ContractTimeline (contractId, eventType, eventDate, eventDescription)
          VALUES (?, 'دفعة', ?, ?)
        `, [
          installment.contractId,
          paymentDate,
          `تم دفع ${paidAmount} للقسط رقم ${installment.installmentNumber}`
        ], (err) => {
          if (err) console.error('Error inserting timeline:', err);
        });

        res.json({ success: true, message: 'تم تحديث الدفعة بنجاح' });
      });
    });
  });
});

// Calculate contract financial summary
app.get('/api/contracts/:id/financial-summary', (req, res) => {
  console.log('GET /api/contracts/:id/financial-summary request');
  const { id } = req.params;

  const query = `
    SELECT
      COUNT(*) as totalInstallments,
      SUM(installmentAmount) as totalContractValue,
      SUM(baseAmount) as totalBaseValue,
      SUM(taxAmount) as totalTaxValue,
      SUM(paidAmount) as totalPaid,
      SUM(remainingAmount) as totalOutstanding,
      SUM(penaltyAmount) as totalPenalties,
      SUM(CASE WHEN isPaid = 1 THEN 1 ELSE 0 END) as paidInstallments,
      SUM(CASE WHEN isPaid = 0 AND date(paymentDueDate) < date('now') THEN 1 ELSE 0 END) as overdueInstallments
    FROM ContractInstallments
    WHERE contractId = ?
  `;

  db.get(query, [id], (err, summary) => {
    if (err) {
      console.error('Error getting financial summary:', err);
      return res.status(500).json({ error: err.message });
    }

    const paymentProgress = summary.totalContractValue > 0
      ? (summary.totalPaid / summary.totalContractValue * 100)
      : 0;

    res.json({
      ...summary,
      paymentProgress: Math.round(paymentProgress * 100) / 100
    });
  });
});

// Financial Reports API

// Get financial summary
app.get('/api/reports/financial-summary', (req, res) => {
  console.log('GET /api/reports/financial-summary request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE c.startDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      COUNT(c.id) as totalContracts,
      COUNT(CASE WHEN c.contractStatus = 'نشط' THEN 1 END) as activeContracts,
      COALESCE(SUM(c.totalContractValue), 0) as totalContractValue,
      COALESCE(SUM(ci.paidAmount), 0) as totalCollected,
      COALESCE(SUM(ci.remainingAmount), 0) as totalOutstanding,
      COALESCE(SUM(CASE WHEN ci.isPaid = 0 AND date(ci.paymentDueDate) < date('now') THEN ci.remainingAmount ELSE 0 END), 0) as totalOverdue,
      COALESCE(AVG(c.totalContractValue), 0) as averageContractValue
    FROM Contracts c
    LEFT JOIN ContractInstallments ci ON c.id = ci.contractId
    ${dateFilter}
  `;

  db.get(query, params, (err, summary) => {
    if (err) {
      console.error('Error getting financial summary:', err);
      return res.status(500).json({ error: err.message });
    }

    const collectionRate = summary.totalContractValue > 0
      ? (summary.totalCollected / summary.totalContractValue * 100)
      : 0;

    res.json({
      ...summary,
      collectionRate: Math.round(collectionRate * 100) / 100
    });
  });
});

// Get monthly revenue data
app.get('/api/reports/monthly-revenue', (req, res) => {
  console.log('GET /api/reports/monthly-revenue request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE ci.installmentDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      strftime('%Y-%m', ci.installmentDate) as month,
      SUM(ci.installmentAmount) as revenue,
      SUM(ci.paidAmount) as collections,
      SUM(ci.remainingAmount) as outstanding
    FROM ContractInstallments ci
    ${dateFilter}
    GROUP BY strftime('%Y-%m', ci.installmentDate)
    ORDER BY month DESC
    LIMIT 12
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting monthly revenue:', err);
      return res.status(500).json({ error: err.message });
    }

    // Format month names
    const formattedRows = rows.map(row => ({
      ...row,
      month: new Date(row.month + '-01').toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long'
      })
    }));

    res.json(formattedRows);
  });
});

// Get contract type analysis
app.get('/api/reports/contract-types', (req, res) => {
  console.log('GET /api/reports/contract-types request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE c.startDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      'عقد إيجار' as contractType,
      COUNT(c.id) as count,
      SUM(c.totalContractValue) as totalValue,
      AVG(c.totalContractValue) as averageValue,
      CASE
        WHEN SUM(c.totalContractValue) > 0
        THEN (SUM(ci.paidAmount) / SUM(c.totalContractValue) * 100)
        ELSE 0
      END as collectionRate
    FROM Contracts c
    LEFT JOIN ContractInstallments ci ON c.id = ci.contractId
    ${dateFilter}
    ORDER BY totalValue DESC
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting contract type analysis:', err);
      return res.status(500).json({ error: err.message });
    }

    const formattedRows = rows.map(row => ({
      ...row,
      collectionRate: Math.round(row.collectionRate * 100) / 100
    }));

    res.json(formattedRows);
  });
});

// Get client analysis
app.get('/api/reports/client-analysis', (req, res) => {
  console.log('GET /api/reports/client-analysis request');
  const { from, to } = req.query;

  let dateFilter = '';
  let params = [];

  if (from && to) {
    dateFilter = 'WHERE c.startDate BETWEEN ? AND ?';
    params = [from, to];
  }

  const query = `
    SELECT
      cl.id as clientId,
      cl.clientName,
      COUNT(c.id) as contractsCount,
      SUM(c.totalContractValue) as totalValue,
      SUM(ci.paidAmount) as paidAmount,
      SUM(ci.remainingAmount) as outstandingAmount,
      CASE
        WHEN SUM(c.totalContractValue) > 0
        THEN (SUM(ci.paidAmount) / SUM(c.totalContractValue) * 100)
        ELSE 0
      END as paymentScore
    FROM Clients cl
    LEFT JOIN Contracts c ON cl.id = c.clientId
    LEFT JOIN ContractInstallments ci ON c.id = ci.contractId
    ${dateFilter}
    GROUP BY cl.id, cl.clientName
    HAVING contractsCount > 0
    ORDER BY totalValue DESC
    LIMIT 50
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting client analysis:', err);
      return res.status(500).json({ error: err.message });
    }

    const formattedRows = rows.map(row => ({
      ...row,
      paymentScore: Math.round(row.paymentScore * 100) / 100
    }));

    res.json(formattedRows);
  });
});

// Get overdue installments report
app.get('/api/reports/overdue-installments', (req, res) => {
  console.log('GET /api/reports/overdue-installments request');

  const query = `
    SELECT
      ci.*,
      c.contractNumber,
      cl.clientName,
      'عقد إيجار' as contractType,
      (julianday('now') - julianday(ci.paymentDueDate)) as daysOverdue
    FROM ContractInstallments ci
    JOIN Contracts c ON ci.contractId = c.id
    JOIN Clients cl ON c.clientId = cl.id
    WHERE ci.isPaid = 0
      AND date(ci.paymentDueDate) < date('now')
    ORDER BY daysOverdue DESC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting overdue installments:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get collection efficiency report
app.get('/api/reports/collection-efficiency', (req, res) => {
  console.log('GET /api/reports/collection-efficiency request');

  const query = `
    SELECT
      strftime('%Y-%m', ci.paidDate) as month,
      COUNT(ci.installmentId) as totalPayments,
      SUM(ci.paidAmount) as totalCollected,
      AVG(julianday(ci.paidDate) - julianday(ci.paymentDueDate)) as avgDaysLate,
      COUNT(CASE WHEN ci.paidDate <= ci.paymentDueDate THEN 1 END) as onTimePayments
    FROM ContractInstallments ci
    WHERE ci.isPaid = 1 AND ci.paidDate IS NOT NULL
    GROUP BY strftime('%Y-%m', ci.paidDate)
    ORDER BY month DESC
    LIMIT 12
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting collection efficiency:', err);
      return res.status(500).json({ error: err.message });
    }

    const formattedRows = rows.map(row => ({
      ...row,
      month: new Date(row.month + '-01').toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long'
      }),
      onTimeRate: row.totalPayments > 0 ? (row.onTimePayments / row.totalPayments * 100) : 0,
      avgDaysLate: Math.round(row.avgDaysLate * 10) / 10
    }));

    res.json(formattedRows);
  });
});

// Alerts System API

// Get alerts with filtering
app.get('/api/alerts', (req, res) => {
  console.log('GET /api/alerts request');
  const { tab, search, priority, type } = req.query;

  let whereConditions = ['1=1'];
  let params = [];

  // Tab filtering
  if (tab === 'unread') {
    whereConditions.push('isRead = 0');
  } else if (tab === 'action') {
    whereConditions.push('actionRequired = 1');
  } else if (tab === 'archived') {
    whereConditions.push('isArchived = 1');
  } else {
    whereConditions.push('isArchived = 0'); // Default: show non-archived
  }

  // Search filtering
  if (search && search.trim() !== '') {
    whereConditions.push('(alertTitle LIKE ? OR alertMessage LIKE ? OR contractNumber LIKE ? OR clientName LIKE ?)');
    const searchTerm = `%${search}%`;
    params.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }

  // Priority filtering
  if (priority && priority !== 'all') {
    whereConditions.push('alertPriority = ?');
    params.push(priority);
  }

  // Type filtering
  if (type && type !== 'all') {
    whereConditions.push('alertType = ?');
    params.push(type);
  }

  const query = `
    SELECT ca.*, c.contractNumber, cl.clientName
    FROM ContractAlerts ca
    LEFT JOIN Contracts c ON ca.contractId = c.id
    LEFT JOIN Clients cl ON ca.clientId = cl.id
    WHERE ${whereConditions.join(' AND ')}
    ORDER BY ca.alertDate DESC, ca.alertPriority DESC
  `;

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error getting alerts:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(rows);
  });
});

// Get alert statistics
app.get('/api/alerts/stats', (req, res) => {
  console.log('GET /api/alerts/stats request');

  const query = `
    SELECT
      COUNT(*) as totalAlerts,
      SUM(CASE WHEN isRead = 0 THEN 1 ELSE 0 END) as unreadAlerts,
      SUM(CASE WHEN alertPriority IN ('عالي', 'حرج') THEN 1 ELSE 0 END) as highPriorityAlerts,
      SUM(CASE WHEN actionRequired = 1 THEN 1 ELSE 0 END) as actionRequiredAlerts,
      SUM(CASE WHEN alertType = 'payment_overdue' THEN 1 ELSE 0 END) as overduePayments,
      SUM(CASE WHEN alertType = 'contract_expiring' THEN 1 ELSE 0 END) as contractExpirations,
      SUM(CASE WHEN alertType = 'system_alert' THEN 1 ELSE 0 END) as systemAlerts
    FROM ContractAlerts
    WHERE isArchived = 0
  `;

  db.get(query, [], (err, stats) => {
    if (err) {
      console.error('Error getting alert stats:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json(stats);
  });
});

// Mark alert as read
app.put('/api/alerts/:id/read', (req, res) => {
  console.log('PUT /api/alerts/:id/read request');
  const { id } = req.params;

  db.run("UPDATE ContractAlerts SET isRead = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?", [id], function(err) {
    if (err) {
      console.error('Error marking alert as read:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    res.json({ success: true, message: 'Alert marked as read' });
  });
});

// Archive alert
app.put('/api/alerts/:id/archive', (req, res) => {
  console.log('PUT /api/alerts/:id/archive request');
  const { id } = req.params;

  db.run("UPDATE ContractAlerts SET isArchived = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?", [id], function(err) {
    if (err) {
      console.error('Error archiving alert:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    res.json({ success: true, message: 'Alert archived' });
  });
});

// Delete alert
app.delete('/api/alerts/:id', (req, res) => {
  console.log('DELETE /api/alerts/:id request');
  const { id } = req.params;

  db.run("DELETE FROM ContractAlerts WHERE id = ?", [id], function(err) {
    if (err) {
      console.error('Error deleting alert:', err);
      return res.status(500).json({ error: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ error: 'Alert not found' });
    }

    res.json({ success: true, message: 'Alert deleted' });
  });
});

// Create new alert
app.post('/api/alerts', (req, res) => {
  console.log('POST /api/alerts request');
  const {
    alertType,
    alertTitle,
    alertMessage,
    alertPriority = 'متوسط',
    contractId,
    clientId,
    alertDate,
    actionRequired = false,
    actionUrl
  } = req.body;

  if (!alertType || !alertTitle || !alertMessage) {
    return res.status(400).json({ error: 'Alert type, title, and message are required' });
  }

  db.run(`
    INSERT INTO ContractAlerts (
      alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
      alertDate, actionRequired, actionUrl, isRead, isArchived
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0)
  `, [
    alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
    alertDate || new Date().toISOString(), actionRequired ? 1 : 0, actionUrl
  ], function(err) {
    if (err) {
      console.error('Error creating alert:', err);
      return res.status(500).json({ error: err.message });
    }

    res.json({ success: true, id: this.lastID, message: 'Alert created successfully' });
  });
});

// Generate automatic alerts (to be called by a scheduler)
app.post('/api/alerts/generate', (req, res) => {
  console.log('POST /api/alerts/generate request');

  // Check for overdue payments
  const overdueQuery = `
    SELECT ci.*, c.contractNumber, cl.clientName, c.clientId
    FROM ContractInstallments ci
    JOIN Contracts c ON ci.contractId = c.id
    JOIN Clients cl ON c.clientId = cl.id
    WHERE ci.isPaid = 0
      AND date(ci.paymentDueDate) < date('now')
      AND NOT EXISTS (
        SELECT 1 FROM ContractAlerts ca
        WHERE ca.contractId = c.id
          AND ca.alertType = 'payment_overdue'
          AND date(ca.alertDate) = date('now')
      )
  `;

  db.all(overdueQuery, [], (err, overdueInstallments) => {
    if (err) {
      console.error('Error checking overdue payments:', err);
      return res.status(500).json({ error: err.message });
    }

    // Create alerts for overdue payments
    overdueInstallments.forEach(installment => {
      const daysOverdue = Math.ceil((new Date().getTime() - new Date(installment.paymentDueDate).getTime()) / (1000 * 60 * 60 * 24));

      db.run(`
        INSERT INTO ContractAlerts (
          alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
          alertDate, actionRequired, actionUrl, isRead, isArchived
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, 0, 0)
      `, [
        'payment_overdue',
        `دفعة متأخرة - ${installment.contractNumber}`,
        `القسط رقم ${installment.installmentNumber} متأخر بـ ${daysOverdue} يوم للعميل ${installment.clientName}`,
        daysOverdue > 30 ? 'حرج' : daysOverdue > 15 ? 'عالي' : 'متوسط',
        installment.contractId,
        installment.clientId,
        new Date().toISOString(),
        `/contracts/${installment.contractId}`
      ], (err) => {
        if (err) console.error('Error creating overdue alert:', err);
      });
    });

    // Check for contracts expiring soon
    const expiringQuery = `
      SELECT c.*, cl.clientName
      FROM Contracts c
      JOIN Clients cl ON c.clientId = cl.id
      WHERE c.contractStatus = 'نشط'
        AND date(c.endDate) BETWEEN date('now') AND date('now', '+30 days')
        AND NOT EXISTS (
          SELECT 1 FROM ContractAlerts ca
          WHERE ca.contractId = c.id
            AND ca.alertType = 'contract_expiring'
            AND date(ca.alertDate) = date('now')
        )
    `;

    db.all(expiringQuery, [], (err, expiringContracts) => {
      if (err) {
        console.error('Error checking expiring contracts:', err);
        return res.status(500).json({ error: err.message });
      }

      // Create alerts for expiring contracts
      expiringContracts.forEach(contract => {
        const daysUntilExpiry = Math.ceil((new Date(contract.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

        db.run(`
          INSERT INTO ContractAlerts (
            alertType, alertTitle, alertMessage, alertPriority, contractId, clientId,
            alertDate, actionRequired, actionUrl, isRead, isArchived
          ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, 0, 0)
        `, [
          'contract_expiring',
          `عقد ينتهي قريباً - ${contract.contractNumber}`,
          `العقد مع ${contract.clientName} سينتهي خلال ${daysUntilExpiry} يوم`,
          daysUntilExpiry <= 7 ? 'حرج' : daysUntilExpiry <= 15 ? 'عالي' : 'متوسط',
          contract.id,
          contract.clientId,
          new Date().toISOString(),
          `/contracts/${contract.id}`
        ], (err) => {
          if (err) console.error('Error creating expiring contract alert:', err);
        });
      });

      res.json({
        success: true,
        message: `Generated ${overdueInstallments.length} overdue alerts and ${expiringContracts.length} expiring contract alerts`
      });
    });
  });
});

// Delete contract (soft delete with protection)
app.delete('/api/contracts/:id', (req, res) => {
  console.log('DELETE /api/contracts/:id request');
  console.log('ID:', req.params.id);

  const { id } = req.params;

  // Check if this is a test contract first
  db.get("SELECT contractNumber FROM Contracts WHERE id = ?", [id], (err, contract) => {
    if (err) {
      console.error('Error checking contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'العقد غير موجود' });
    }

    // If it's a test contract (starts with BACKUP-TEST), allow deletion without restrictions
    if (contract.contractNumber && contract.contractNumber.startsWith('BACKUP-TEST')) {
      console.log(`Deleting test contract ${contract.contractNumber} without restrictions`);

      // Direct deletion for test contracts
      db.run(
        "UPDATE Contracts SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?",
        [id],
        function(deleteErr) {
          if (deleteErr) {
            console.error('Delete test contract error:', deleteErr);
            res.status(500).json({ error: deleteErr.message });
          } else {
            console.log('Test contract deleted successfully, changes:', this.changes);

            // Also soft delete related receivables
            db.run(
              "UPDATE ContractReceivables SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE contractId=?",
              [id],
              function(receivablesErr) {
                if (receivablesErr) {
                  console.error('Error updating test contract receivables:', receivablesErr);
                } else {
                  console.log('Test contract receivables updated, changes:', this.changes);
                }
              }
            );

            res.json({
              success: true,
              changes: this.changes,
              message: 'تم حذف العقد التجريبي بنجاح',
              messageEn: 'Test contract deleted successfully',
              isTestContract: true,
              hasUnpaidReceivables: false
            });
          }
        }
      );
      return;
    }

    // For regular contracts, check receivables first
  const checkActiveReceivablesQuery = `
    SELECT
      COUNT(*) as activeReceivablesCount,
      COUNT(CASE WHEN status != 'مدفوع' THEN 1 END) as unpaidReceivablesCount,
      SUM(CASE WHEN status != 'مدفوع' THEN amount ELSE 0 END) as unpaidAmount,
      GROUP_CONCAT(
        CASE WHEN status != 'مدفوع'
        THEN receivableNumber || ' (' || amount || ' ج.م - ' || status || ')'
        END, ', '
      ) as unpaidDetails
    FROM ContractReceivables
    WHERE contractId = ? AND isActive = 1
  `;

  db.get(checkActiveReceivablesQuery, [id], (err, result) => {
    if (err) {
      console.error('Error checking contract receivables:', err);
      return res.status(500).json({ error: err.message });
    }

    const activeReceivablesCount = result.activeReceivablesCount || 0;
    const unpaidReceivablesCount = result.unpaidReceivablesCount || 0;
    const unpaidAmount = result.unpaidAmount || 0;
    const unpaidDetails = result.unpaidDetails || '';

    // Prepare warning message if there are unpaid receivables (but still allow deletion)
    let warningMessage = null;
    let warningDetails = null;

    if (unpaidReceivablesCount > 0) {
      console.log(`Warning: Deleting contract ${id} with ${unpaidReceivablesCount} unpaid receivables worth ${unpaidAmount} EGP`);

      warningMessage = {
        ar: `تحذير: تم حذف العقد الذي يحتوي على ${unpaidReceivablesCount} استحقاق غير مسدد بقيمة ${unpaidAmount?.toLocaleString()} ج.م`,
        en: `Warning: Deleted contract with ${unpaidReceivablesCount} unpaid receivables worth ${unpaidAmount?.toLocaleString()} EGP`
      };

      warningDetails = {
        unpaidReceivablesCount: unpaidReceivablesCount,
        activeReceivablesCount: activeReceivablesCount,
        unpaidAmount: unpaidAmount,
        unpaidDetails: unpaidDetails
      };
    }

    // Proceed with soft delete (always allow deletion, but warn if needed)
    db.run(
      "UPDATE Contracts SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE id=?",
      [id],
      function(deleteErr) {
        if (deleteErr) {
          console.error('Delete contract error:', deleteErr);
          res.status(500).json({ error: deleteErr.message });
        } else {
          console.log('Contract deleted successfully, changes:', this.changes);

          // Also soft delete related receivables
          db.run(
            "UPDATE ContractReceivables SET isActive=0, updatedAt=CURRENT_TIMESTAMP WHERE contractId=?",
            [id],
            function(receivablesErr) {
              if (receivablesErr) {
                console.error('Error updating receivables:', receivablesErr);
              } else {
                console.log('Contract receivables updated, changes:', this.changes);
              }
            }
          );

          // Prepare response with warning if applicable
          const response = {
            success: true,
            changes: this.changes,
            message: warningMessage ?
              `تم حذف العقد بنجاح مع تحذير: ${warningMessage.ar}` :
              'تم حذف العقد بنجاح',
            messageEn: warningMessage ?
              `Contract deleted successfully with warning: ${warningMessage.en}` :
              'Contract deleted successfully'
          };

          // Add warning details if there were unpaid receivables
          if (warningMessage) {
            response.warning = warningMessage;
            response.warningDetails = warningDetails;
            response.hasUnpaidReceivables = true;
          } else {
            response.hasUnpaidReceivables = false;
          }

          res.json(response);
        }
      }
    );
  });
  }); // إغلاق دالة فحص العقد
});

// Check if contract can be deleted
app.get('/api/contracts/:id/can-delete', (req, res) => {
  console.log('GET /api/contracts/:id/can-delete request');
  const { id } = req.params;

  const checkQuery = `
    SELECT
      COUNT(*) as activeReceivablesCount,
      COUNT(CASE WHEN status != 'مدفوع' THEN 1 END) as unpaidReceivablesCount,
      SUM(CASE WHEN status != 'مدفوع' THEN amount ELSE 0 END) as unpaidAmount
    FROM ContractReceivables
    WHERE contractId = ? AND isActive = 1
  `;

  db.get(checkQuery, [id], (err, result) => {
    if (err) {
      console.error('Error checking contract deletion status:', err);
      return res.status(500).json({ error: err.message });
    }

    const activeReceivablesCount = result.activeReceivablesCount || 0;
    const unpaidReceivablesCount = result.unpaidReceivablesCount || 0;
    const unpaidAmount = result.unpaidAmount || 0;
    const canDelete = unpaidReceivablesCount === 0;

    res.json({
      canDelete,
      activeReceivablesCount,
      unpaidReceivablesCount,
      unpaidAmount,
      message: canDelete
        ? 'يمكن حذف العقد'
        : `لا يمكن حذف العقد - يحتوي على ${unpaidReceivablesCount} استحقاق غير مسدد`,
      messageEn: canDelete
        ? 'Contract can be deleted'
        : `Cannot delete contract - has ${unpaidReceivablesCount} unpaid receivable(s)`,
      details: canDelete ? null : {
        ar: `العقد يحتوي على استحقاقات غير مسددة بقيمة ${unpaidAmount}`,
        en: `Contract has unpaid receivables worth ${unpaidAmount}`,
        unpaidAmount: unpaidAmount
      }
    });
  });
});

// Update contract statuses (manual trigger)
app.post('/api/contracts/update-statuses', (req, res) => {
  console.log('POST /api/contracts/update-statuses request');

  updateContractStatuses();
  updateReceivableStatuses();

  res.json({
    success: true,
    message: 'تم تحديث حالة العقود والاستحقاقات بنجاح',
    messageEn: 'Contract and receivable statuses updated successfully'
  });
});

// Clean database and create new simplified contracts table
app.post('/api/admin/reset-contracts-table', (req, res) => {
  console.log('🗑️ Starting contracts table reset...');

  // Step 1: Drop existing contracts table
  db.run("DROP TABLE IF EXISTS Contracts", (err) => {
    if (err) {
      console.error('Error dropping Contracts table:', err);
      return res.status(500).json({ error: err.message });
    }

    console.log('✅ Old Contracts table dropped');

    // Step 2: Create new simplified contracts table
    const createTableSQL = `
      CREATE TABLE Contracts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,

        -- Basic Contract Info
        contractNumber TEXT UNIQUE NOT NULL,
        contractInternalId TEXT,
        contractSubject TEXT NOT NULL,
        contractDescription TEXT,
        contractType TEXT NOT NULL,
        contractStatus TEXT DEFAULT 'نشط' CHECK (contractStatus IN ('نشط', 'غير نشط', 'منتهي', 'ملغي', 'معلق')),

        -- Client and Guarantor
        clientId INTEGER NOT NULL,
        financialGuarantorId INTEGER,

        -- Contract Dates
        contractDate DATE NOT NULL,
        contractSigningDate DATE NOT NULL,
        financialActivationDate DATE,
        startDate DATE NOT NULL,
        actualStartDate DATE,
        endDate DATE,
        actualEndDate DATE,
        contractDurationYears INTEGER NOT NULL,

        -- Asset and Management
        assetOwner TEXT,
        ownershipPercentage DECIMAL(5,2) DEFAULT 100,
        responsibleDepartment TEXT,
        region TEXT,

        -- Financial Terms
        totalContractValue DECIMAL(15,2) NOT NULL,
        monthlyAmount DECIMAL(15,2) NOT NULL,
        paymentDay INTEGER DEFAULT 1,
        paymentFrequency TEXT DEFAULT 'شهري',
        firstInstallmentDate DATE,
        paymentMethod TEXT DEFAULT 'تحويل بنكي',
        irregularPaymentMonths INTEGER DEFAULT 0,

        -- Insurance and Advance Payment
        finalInsuranceRate DECIMAL(5,2) DEFAULT 0,
        finalInsuranceAmount DECIMAL(15,2) DEFAULT 0,
        advancePaymentMonths INTEGER DEFAULT 0,
        advancePaymentAmount DECIMAL(15,2) DEFAULT 0,

        -- Penalties and Fees
        lateFeeType TEXT DEFAULT 'نسبة مئوية',
        lateFeeValue DECIMAL(10,2) DEFAULT 0,
        gracePeriodDays INTEGER DEFAULT 5,
        bouncedCheckFeeType TEXT DEFAULT 'مبلغ ثابت',
        bouncedCheckFeeValue DECIMAL(10,2) DEFAULT 0,
        additionalFees TEXT, -- JSON string for additional fees

        -- Contract Structure
        numberOfProducts INTEGER DEFAULT 1,
        hasUnifiedActivationDate INTEGER DEFAULT 1,

        -- Check Status
        checkStatus TEXT DEFAULT 'لم يتقدم بالشيكات',

        -- Notes
        notes TEXT,
        importantNotes TEXT,

        -- System Fields
        isActive INTEGER DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,

        -- Foreign Keys
        FOREIGN KEY (clientId) REFERENCES Clients(id),
        FOREIGN KEY (financialGuarantorId) REFERENCES Clients(id)
      )
    `;

    db.run(createTableSQL, (err) => {
      if (err) {
        console.error('Error creating new Contracts table:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log('✅ New simplified Contracts table created');

      // Step 3: Also clean related tables
      const cleanupQueries = [
        "DELETE FROM ContractProducts",
        "DELETE FROM ContractPartners",
        "DELETE FROM ContractReceivables",
        "DELETE FROM ContractInstallments",
        "DELETE FROM ContractTimeline"
      ];

      let completed = 0;
      cleanupQueries.forEach((query, index) => {
        db.run(query, (err) => {
          if (err) {
            console.error(`Error cleaning table ${index}:`, err);
          } else {
            console.log(`✅ Cleaned related table ${index + 1}/${cleanupQueries.length}`);
          }

          completed++;
          if (completed === cleanupQueries.length) {
            res.json({
              success: true,
              message: 'Contracts table reset successfully',
              details: {
                oldTableDropped: true,
                newTableCreated: true,
                relatedTablesCleared: true
              }
            });
          }
        });
      });
    });
  });
});

// Simplified contract creation API
app.post('/api/contracts/simple', async (req, res) => {
  console.log('📝 Creating new contract with simplified schema...');

  const {
    contractNumber,
    contractInternalId,
    contractSubject,
    contractDescription,
    contractType,
    contractStatus = 'نشط',
    clientId,
    financialGuarantorId,
    contractDate,
    contractSigningDate,
    financialActivationDate,
    startDate,
    actualStartDate,
    endDate,
    actualEndDate,
    contractDurationYears,
    assetOwner,
    ownershipPercentage = 100,
    responsibleDepartment,
    region,
    totalContractValue,
    monthlyAmount,
    paymentDay = 1,
    paymentFrequency = 'شهري',
    firstInstallmentDate,
    paymentMethod = 'تحويل بنكي',
    irregularPaymentMonths = 0,
    finalInsuranceRate = 0,
    finalInsuranceAmount = 0,
    advancePaymentMonths = 0,
    advancePaymentAmount = 0,
    lateFeeType = 'نسبة مئوية',
    lateFeeValue = 0,
    gracePeriodDays = 5,
    bouncedCheckFeeType = 'مبلغ ثابت',
    bouncedCheckFeeValue = 0,
    additionalFees = [],
    numberOfProducts = 1,
    hasUnifiedActivationDate = true,
    checkStatus = 'لم يتقدم بالشيكات',
    notes,
    importantNotes,
    products = [],
    partners = []
  } = req.body;

  // Insert contract
  const contractSQL = `
    INSERT INTO Contracts (
      contractNumber, contractInternalId, contractSubject, contractDescription, contractType, contractStatus,
      clientId, financialGuarantorId, contractDate, contractSigningDate, financialActivationDate,
      startDate, actualStartDate, endDate, actualEndDate, contractDurationYears,
      assetOwner, ownershipPercentage, responsibleDepartment, region,
      totalContractValue, monthlyAmount, paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod, irregularPaymentMonths,
      finalInsuranceRate, finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount,
      lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue, additionalFees,
      numberOfProducts, hasUnifiedActivationDate, checkStatus, notes, importantNotes, isActive
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  db.run(contractSQL, [
    contractNumber, contractInternalId, contractSubject, contractDescription, contractType, contractStatus,
    clientId, financialGuarantorId, contractDate, contractSigningDate, financialActivationDate,
    startDate, actualStartDate, endDate, actualEndDate, contractDurationYears,
    assetOwner, ownershipPercentage, responsibleDepartment, region,
    totalContractValue, monthlyAmount, paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod, irregularPaymentMonths,
    finalInsuranceRate, finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount,
    lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue, JSON.stringify(additionalFees),
    numberOfProducts, hasUnifiedActivationDate ? 1 : 0, checkStatus, notes, importantNotes, 1
  ], function(err) {
    if (err) {
      console.error('Error creating contract:', err);
      return res.status(500).json({ error: err.message });
    }

    const contractId = this.lastID;
    console.log('✅ Contract created with ID:', contractId);

    // Insert products if any
    if (products && products.length > 0) {
      insertContractProducts(contractId, products, (productErr) => {
        if (productErr) {
          console.error('Error inserting products:', productErr);
          return res.status(500).json({ error: productErr.message });
        }

        // Insert partners if any
        if (partners && partners.length > 0) {
          insertContractPartners(contractId, partners, (partnerErr) => {
            if (partnerErr) {
              console.error('Error inserting partners:', partnerErr);
              return res.status(500).json({ error: partnerErr.message });
            }

            res.json({
              success: true,
              message: 'Contract created successfully',
              contractId: contractId
            });
          });
        } else {
          res.json({
            success: true,
            message: 'Contract created successfully',
            contractId: contractId
          });
        }
      });
    } else {
      res.json({
        success: true,
        message: 'Contract created successfully',
        contractId: contractId
      });
    }
  });
});

// Simplified contract retrieval for editing
app.get('/api/contracts/simple/:id', (req, res) => {
  const contractId = req.params.id;
  console.log('📖 Fetching contract for editing, ID:', contractId);

  const query = `
    SELECT
      c.*,
      cl.clientName,
      cl.clientType,
      cl.clientPhoneWhatsapp,
      cl.clientAddress,
      cl.clientEmail,
      cl.clientFinancial_Category as clientClassification,
      fg.clientName as financialGuarantorName
    FROM Contracts c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    LEFT JOIN Clients fg ON c.financialGuarantorId = fg.id
    WHERE c.id = ? AND c.isActive = 1
  `;

  db.get(query, [contractId], (err, contract) => {
    if (err) {
      console.error('Error fetching contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(404).json({ error: 'Contract not found' });
    }

    // Get contract products
    db.all("SELECT * FROM ContractProducts WHERE contractId = ? AND isActive = 1", [contractId], (err, products) => {
      if (err) {
        console.error('Error fetching products:', err);
        return res.status(500).json({ error: err.message });
      }

      // Get contract partners
      db.all("SELECT * FROM ContractPartners WHERE contractId = ?", [contractId], (err, partners) => {
        if (err) {
          console.error('Error fetching partners:', err);
          return res.status(500).json({ error: err.message });
        }

        // Parse additional fees
        let additionalFees = [];
        try {
          if (contract.additionalFees) {
            additionalFees = JSON.parse(contract.additionalFees);
          }
        } catch (e) {
          console.warn('Error parsing additional fees:', e);
          additionalFees = [];
        }

        // Return clean contract data
        const contractData = {
          ...contract,
          additionalFees,
          products: products || [],
          partners: partners || [],
          hasPartners: partners && partners.length > 0,
          hasUnifiedActivationDate: contract.hasUnifiedActivationDate === 1
        };

        console.log('✅ Contract data retrieved successfully');
        res.json(contractData);
      });
    });
  });
});

// Simplified contract update API
app.put('/api/contracts/simple/:id', (req, res) => {
  const contractId = req.params.id;
  console.log('📝 Updating contract with simplified schema, ID:', contractId);

  const {
    contractNumber,
    contractInternalId,
    contractSubject,
    contractDescription,
    contractType,
    contractStatus,
    clientId,
    financialGuarantorId,
    contractDate,
    contractSigningDate,
    financialActivationDate,
    startDate,
    actualStartDate,
    endDate,
    actualEndDate,
    contractDurationYears,
    assetOwner,
    ownershipPercentage,
    responsibleDepartment,
    region,
    totalContractValue,
    monthlyAmount,
    paymentDay,
    paymentFrequency,
    firstInstallmentDate,
    paymentMethod,
    irregularPaymentMonths,
    finalInsuranceRate,
    finalInsuranceAmount,
    advancePaymentMonths,
    advancePaymentAmount,
    lateFeeType,
    lateFeeValue,
    gracePeriodDays,
    bouncedCheckFeeType,
    bouncedCheckFeeValue,
    additionalFees,
    numberOfProducts,
    hasUnifiedActivationDate,
    checkStatus,
    notes,
    importantNotes,
    products,
    partners
  } = req.body;

  const updateSQL = `
    UPDATE Contracts SET
      contractNumber = ?, contractInternalId = ?, contractSubject = ?, contractDescription = ?, contractType = ?, contractStatus = ?,
      clientId = ?, financialGuarantorId = ?, contractDate = ?, contractSigningDate = ?, financialActivationDate = ?,
      startDate = ?, actualStartDate = ?, endDate = ?, actualEndDate = ?, contractDurationYears = ?,
      assetOwner = ?, ownershipPercentage = ?, responsibleDepartment = ?, region = ?,
      totalContractValue = ?, monthlyAmount = ?, paymentDay = ?, paymentFrequency = ?, firstInstallmentDate = ?, paymentMethod = ?, irregularPaymentMonths = ?,
      finalInsuranceRate = ?, finalInsuranceAmount = ?, advancePaymentMonths = ?, advancePaymentAmount = ?,
      lateFeeType = ?, lateFeeValue = ?, gracePeriodDays = ?, bouncedCheckFeeType = ?, bouncedCheckFeeValue = ?, additionalFees = ?,
      numberOfProducts = ?, hasUnifiedActivationDate = ?, checkStatus = ?, notes = ?, importantNotes = ?,
      updatedAt = CURRENT_TIMESTAMP
    WHERE id = ?
  `;

  db.run(updateSQL, [
    contractNumber, contractInternalId, contractSubject, contractDescription, contractType, contractStatus,
    clientId, financialGuarantorId, contractDate, contractSigningDate, financialActivationDate,
    startDate, actualStartDate, endDate, actualEndDate, contractDurationYears,
    assetOwner, ownershipPercentage, responsibleDepartment, region,
    totalContractValue, monthlyAmount, paymentDay, paymentFrequency, firstInstallmentDate, paymentMethod, irregularPaymentMonths,
    finalInsuranceRate, finalInsuranceAmount, advancePaymentMonths, advancePaymentAmount,
    lateFeeType, lateFeeValue, gracePeriodDays, bouncedCheckFeeType, bouncedCheckFeeValue, JSON.stringify(additionalFees || []),
    numberOfProducts, hasUnifiedActivationDate ? 1 : 0, checkStatus, notes, importantNotes,
    contractId
  ], function(err) {
    if (err) {
      console.error('Error updating contract:', err);
      return res.status(500).json({ error: err.message });
    }

    console.log('✅ Contract updated successfully');
    res.json({
      success: true,
      message: 'Contract updated successfully',
      contractId: contractId
    });
  });
});

// ===== CHEQUES MANAGEMENT API =====

// Get all cheques with filters
app.get('/api/cheques', (req, res) => {
  console.log('GET /api/cheques request');

  const { status, contractId, clientId, bankName, page = 1, limit = 100 } = req.query;

  let query = `
    SELECT c.*,
           cl.clientName,
           ct.contractNumber,
           ct.contractSubject,
           cr.receivableNumber,
           cr.description as receivableDescription
    FROM Cheques c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    LEFT JOIN Contracts ct ON c.contractId = ct.id
    LEFT JOIN ContractReceivables cr ON c.receivableId = cr.id
    WHERE c.isActive = 1
  `;

  const params = [];

  if (status) {
    query += ' AND c.status = ?';
    params.push(status);
  }

  if (contractId) {
    query += ' AND c.contractId = ?';
    params.push(contractId);
  }

  if (clientId) {
    query += ' AND c.clientId = ?';
    params.push(clientId);
  }

  if (bankName) {
    query += ' AND c.bankName = ?';
    params.push(bankName);
  }

  query += ' ORDER BY c.createdAt DESC';

  // Add pagination
  const offset = (page - 1) * limit;
  query += ' LIMIT ? OFFSET ?';
  params.push(limit, offset);

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error fetching cheques:', err);
      return res.status(500).json({ error: err.message });
    }

    console.log(`Cheques retrieved successfully: ${rows.length} items`);
    res.json(rows);
  });
});

// Get banks list for cheques (must be before :id route)
app.get('/api/cheques/banks', (req, res) => {
  console.log('GET /api/cheques/banks request');

  db.all(
    "SELECT itemValue as value, itemLabel as label FROM ReferenceData WHERE module = 'cheques' AND listName = 'banks' AND isActive = 1 AND isDeleted = 0 ORDER BY sortOrder",
    [],
    (err, rows) => {
      if (err) {
        console.error('Error fetching banks:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log(`Banks retrieved successfully: ${rows.length} items`);
      res.json(rows);
    }
  );
});

// Get cheque by ID
app.get('/api/cheques/:id', (req, res) => {
  console.log('GET /api/cheques/:id request');
  const { id } = req.params;

  const query = `
    SELECT c.*,
           cl.clientName,
           ct.contractNumber,
           ct.contractSubject,
           cr.receivableNumber,
           cr.description as receivableDescription
    FROM Cheques c
    LEFT JOIN Clients cl ON c.clientId = cl.id
    LEFT JOIN Contracts ct ON c.contractId = ct.id
    LEFT JOIN ContractReceivables cr ON c.receivableId = cr.id
    WHERE c.id = ? AND c.isActive = 1
  `;

  db.get(query, [id], (err, row) => {
    if (err) {
      console.error('Error fetching cheque:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!row) {
      return res.status(404).json({ error: 'Cheque not found' });
    }

    console.log('Cheque retrieved successfully');
    res.json(row);
  });
});

// Create new cheque
app.post('/api/cheques', (req, res) => {
  console.log('POST /api/cheques request');
  console.log('Received data:', req.body);

  const {
    chequeNumber,
    contractId,
    clientId,
    receivableId,
    bankName,
    chequeAmount,
    chequeDate,
    dueDate,
    entryNumber,
    notes
  } = req.body;

  // Validate required fields
  if (!chequeNumber || !contractId || !clientId || !bankName || !chequeAmount || !chequeDate || !dueDate) {
    return res.status(400).json({
      error: 'البيانات المطلوبة ناقصة',
      details: 'رقم الشيك، العقد، العميل، البنك، المبلغ، تاريخ الشيك، وتاريخ الاستحقاق مطلوبة'
    });
  }

  // Get contract status to determine initial workflow
  db.get("SELECT contractStatus FROM Contracts WHERE id = ?", [contractId], (err, contract) => {
    if (err) {
      console.error('Error fetching contract:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!contract) {
      return res.status(400).json({ error: 'العقد غير موجود' });
    }

    const shouldDepositToBank = contract.contractStatus === 'نشط';

    // Insert cheque
    const query = `
      INSERT INTO Cheques (
        chequeNumber, contractId, clientId, receivableId, bankName, chequeAmount,
        chequeDate, dueDate, status, receivedDate, statusDate, entryNumber,
        contractStatusAtReceipt, shouldDepositToBank, notes, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'خزينة', DATE('now'), DATE('now'), ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `;

    db.run(query, [
      chequeNumber, contractId, clientId, receivableId, bankName, chequeAmount,
      chequeDate, dueDate, entryNumber, contract.contractStatus, shouldDepositToBank ? 1 : 0, notes
    ], function(err) {
      if (err) {
        console.error('Error creating cheque:', err);
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(400).json({ error: 'رقم الشيك مستخدم من قبل' });
        }
        return res.status(500).json({ error: err.message });
      }

      console.log('Cheque created successfully, ID:', this.lastID);
      res.json({
        success: true,
        id: this.lastID,
        shouldDepositToBank: shouldDepositToBank,
        message: shouldDepositToBank ? 'تم استلام الشيك - يُنصح بإيداعه بالبنك' : 'تم استلام الشيك - سيُحتفظ به كأوراق قبض'
      });
    });
  });
});

// Update cheque status
app.put('/api/cheques/:id/status', (req, res) => {
  console.log('PUT /api/cheques/:id/status request');
  const { id } = req.params;
  const { status, entryNumber, reason, notes } = req.body;

  // Validate required fields
  if (!status) {
    return res.status(400).json({ error: 'الحالة الجديدة مطلوبة' });
  }

  if (!entryNumber && status !== 'خزينة') {
    return res.status(400).json({ error: 'رقم القيد مطلوب لتغيير الحالة' });
  }

  // Get current cheque data
  db.get("SELECT * FROM Cheques WHERE id = ? AND isActive = 1", [id], (err, cheque) => {
    if (err) {
      console.error('Error fetching cheque:', err);
      return res.status(500).json({ error: err.message });
    }

    if (!cheque) {
      return res.status(404).json({ error: 'الشيك غير موجود' });
    }

    // Prepare update fields based on new status
    let updateFields = {
      status: status,
      statusDate: new Date().toISOString().split('T')[0],
      entryNumber: entryNumber,
      updatedAt: new Date().toISOString()
    };

    // Add specific fields based on status
    if (status === 'مرتد' && reason) {
      updateFields.bounceReason = reason;
    } else if (status === 'مسحوب' && reason) {
      updateFields.withdrawalReason = reason;
    }

    if (notes) {
      updateFields.notes = notes;
    }

    // Build update query
    const setClause = Object.keys(updateFields).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updateFields);
    values.push(id);

    const query = `UPDATE Cheques SET ${setClause} WHERE id = ?`;

    db.run(query, values, function(err) {
      if (err) {
        console.error('Error updating cheque status:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log(`Cheque ${id} status updated to ${status}`);

      // If cheque is collected, update related receivable
      if (status === 'محصل' && cheque.receivableId) {
        db.run(
          "UPDATE ContractReceivables SET status = 'مسدد', updatedAt = CURRENT_TIMESTAMP WHERE id = ?",
          [cheque.receivableId],
          (err) => {
            if (err) {
              console.error('Error updating receivable status:', err);
            } else {
              console.log('Related receivable marked as paid');
            }
          }
        );
      }

      res.json({
        success: true,
        message: `تم تحديث حالة الشيك إلى: ${status}`,
        newStatus: status
      });
    });
  });
});



// Function to update automatic contract fields based on payment behavior
function updateContractAutomaticFields(contractId, callback) {
  console.log('🔄 Updating automatic fields for contract:', contractId);

  // Get contract installments to calculate missed payments
  db.all(`
    SELECT * FROM ContractInstallments
    WHERE contractId = ? AND isActive = 1
    ORDER BY installmentDate ASC
  `, [contractId], (err, installments) => {
    if (err) {
      console.error('Error fetching installments:', err);
      return callback(err);
    }

    const today = new Date();
    let consecutiveMissed = 0;
    let totalMissed = 0;
    let foundPaid = false;

    // Calculate missed payments
    installments.forEach(installment => {
      const dueDate = new Date(installment.paymentDueDate);
      const isPastDue = dueDate < today;
      const isPaid = installment.isPaid === 1;

      if (isPastDue && !isPaid) {
        totalMissed++;
        if (!foundPaid) {
          consecutiveMissed++;
        }
      } else if (isPaid) {
        foundPaid = true;
      }
    });

    // Determine if auto termination should be suggested
    const autoTerminationSuggested = consecutiveMissed >= 3 ? 1 : 0;

    // Update contract with calculated values
    db.run(`
      UPDATE Contracts
      SET
        consecutiveMissedPayments = ?,
        totalMissedPayments = ?,
        autoTerminationSuggested = ?,
        updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [consecutiveMissed, totalMissed, autoTerminationSuggested, contractId], (updateErr) => {
      if (updateErr) {
        console.error('Error updating automatic fields:', updateErr);
        return callback(updateErr);
      }

      console.log(`✅ Updated automatic fields for contract ${contractId}:`, {
        consecutiveMissed,
        totalMissed,
        autoTerminationSuggested: autoTerminationSuggested === 1
      });

      callback(null, {
        consecutiveMissedPayments: consecutiveMissed,
        totalMissedPayments: totalMissed,
        autoTerminationSuggested: autoTerminationSuggested === 1
      });
    });
  });
}

// API endpoint to manually trigger automatic fields update
app.post('/api/contracts/:id/update-automatic-fields', (req, res) => {
  const { id } = req.params;

  updateContractAutomaticFields(id, (err, result) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }

    res.json({
      success: true,
      message: 'Automatic fields updated successfully',
      data: result
    });
  });
});

// Start server with proper initialization
async function startServer() {
  try {
    // Initialize database first
    await initializeDatabase();

    // Update contract statuses on startup
    updateContractStatuses();

    // Set up periodic status updates (every hour)
    setInterval(() => {
      updateContractStatuses();
      updateReceivableStatuses();
    }, 60 * 60 * 1000); // 1 hour

// ===== REFERENCE DATA API ENDPOINTS =====

// Get reference lists configuration
app.get('/api/reference-lists-config', (req, res) => {
  console.log('GET /api/reference-lists-config request');

  const query = `
    SELECT * FROM ReferenceListsConfig
    WHERE isActive = 1
    ORDER BY displayName ASC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting reference lists config:', err);
      return res.status(500).json({ error: err.message });
    }

    // Parse JSON fields
    const configs = rows.map(row => ({
      ...row,
      linkedPages: JSON.parse(row.linkedPages || '[]'),
      fieldMapping: JSON.parse(row.fieldMapping || '{}')
    }));

    console.log(`Reference lists config retrieved successfully: ${configs.length} items`);
    res.json(configs);
  });
});

// Create new reference list configuration
app.post('/api/reference-lists-config', (req, res) => {
  console.log('POST /api/reference-lists-config request');
  console.log('Received data:', JSON.stringify(req.body, null, 2));

  const { listName, displayName, description, linkedPages, isRequired } = req.body;

  if (!listName || !displayName) {
    console.log('Validation failed: missing required fields');
    return res.status(400).json({ error: 'listName and displayName are required' });
  }

  // Check if list already exists
  db.get('SELECT COUNT(*) as count FROM ReferenceListsConfig WHERE listName = ?', [listName], (err, row) => {
    if (err) {
      console.error('Check existing list error:', err);
      return res.status(500).json({ error: err.message });
    }

    if (row.count > 0) {
      return res.status(400).json({ error: 'List with this name already exists' });
    }

    // Parse linkedPages and create fieldMapping
    const linkedPagesArray = linkedPages ? linkedPages.split(',').map(p => p.trim()).filter(p => p) : [];
    const fieldMapping = {};
    linkedPagesArray.forEach(page => {
      fieldMapping[page] = listName; // Default field mapping
    });

    // Insert new configuration
    db.run(`
      INSERT INTO ReferenceListsConfig (listName, displayName, description, linkedPages, fieldMapping, isRequired, isActive, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [
      listName,
      displayName,
      description || '',
      JSON.stringify(linkedPagesArray),
      JSON.stringify(fieldMapping),
      isRequired || false
    ], function(err) {
      if (err) {
        console.error('Insert reference list config error:', err);
        return res.status(500).json({ error: err.message });
      }

      console.log(`Reference list config created successfully: ${listName}`);
      res.json({
        success: true,
        id: this.lastID,
        listName,
        displayName
      });
    });
  });
});

// Get reference lists for a specific page
app.get('/api/reference-lists-for-page/:pageName', (req, res) => {
  const { pageName } = req.params;
  console.log(`GET /api/reference-lists-for-page/${pageName} request`);

  const query = `
    SELECT * FROM ReferenceListsConfig
    WHERE isActive = 1 AND linkedPages LIKE ?
    ORDER BY displayName ASC
  `;

  db.all(query, [`%"${pageName}"%`], (err, rows) => {
    if (err) {
      console.error('Error getting reference lists for page:', err);
      return res.status(500).json({ error: err.message });
    }

    // Parse JSON fields and get actual data
    const configs = rows.map(row => ({
      ...row,
      linkedPages: JSON.parse(row.linkedPages || '[]'),
      fieldMapping: JSON.parse(row.fieldMapping || '{}')
    }));

    console.log(`Reference lists for page ${pageName} retrieved successfully: ${configs.length} items`);
    res.json(configs);
  });
});

// Check if required reference lists are configured for a page
app.get('/api/reference-lists-status/:pageName', (req, res) => {
  const { pageName } = req.params;
  console.log(`GET /api/reference-lists-status/${pageName} request`);

  const query = `
    SELECT rlc.*,
           (SELECT COUNT(*) FROM ReferenceData rd WHERE rd.listName = rlc.listName AND rd.isDeleted = 0 AND rd.isActive = 1) as itemCount
    FROM ReferenceListsConfig rlc
    WHERE rlc.isActive = 1 AND rlc.linkedPages LIKE ?
    ORDER BY rlc.isRequired DESC, rlc.displayName ASC
  `;

  db.all(query, [`%"${pageName}"%`], (err, rows) => {
    if (err) {
      console.error('Error getting reference lists status:', err);
      return res.status(500).json({ error: err.message });
    }

    const status = rows.map(row => ({
      listName: row.listName,
      displayName: row.displayName,
      description: row.description,
      isRequired: row.isRequired,
      itemCount: row.itemCount,
      isConfigured: row.itemCount > 0,
      linkedPages: JSON.parse(row.linkedPages || '[]'),
      fieldMapping: JSON.parse(row.fieldMapping || '{}')
    }));

    const missingRequired = status.filter(s => s.isRequired && !s.isConfigured);
    const hasIssues = missingRequired.length > 0;

    console.log(`Reference lists status for page ${pageName}: ${hasIssues ? 'HAS ISSUES' : 'OK'}`);
    res.json({
      status,
      hasIssues,
      missingRequired,
      summary: {
        total: status.length,
        configured: status.filter(s => s.isConfigured).length,
        required: status.filter(s => s.isRequired).length,
        missingRequired: missingRequired.length
      }
    });
  });
});


// Add new reference data item
app.post('/api/reference-data/:module/:listName', (req, res) => {
  const { module, listName } = req.params;
  const { itemValue, itemLabel, sortOrder } = req.body;
  console.log(`POST /api/reference-data/${module}/${listName} request`, req.body);

  const query = `
    INSERT INTO ReferenceData (module, listName, itemValue, itemLabel, sortOrder, isActive, isDeleted, createdAt, updatedAt)
    VALUES (?, ?, ?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `;

  db.run(query, [module, listName, itemValue, itemLabel, sortOrder || 999], function(err) {
    if (err) {
      console.error('Error adding reference data:', err);
      return res.status(500).json({ error: err.message });
    }

    console.log('Reference data added successfully:', this.lastID);
    res.json({ id: this.lastID, message: 'Reference data added successfully' });
  });
});



// ===== CONTRACT TYPE PROPERTIES API =====

// Get contract type properties
app.get('/api/contract-types/:contractType/properties', (req, res) => {
  const { contractType } = req.params;
  console.log(`GET /api/contract-types/${contractType}/properties request`);

  const query = `
    SELECT * FROM ContractTypeProperties
    WHERE contractType = ? AND isActive = 1
    ORDER BY propertyName ASC
  `;

  db.all(query, [contractType], (err, rows) => {
    if (err) {
      console.error('Error getting contract type properties:', err);
      return res.status(500).json({ error: err.message });
    }

    console.log(`Contract type properties retrieved: ${rows.length} items`);
    res.json(rows);
  });
});

// Get all contract types with their properties
app.get('/api/contract-types-with-properties', (req, res) => {
  console.log('GET /api/contract-types-with-properties request');

  const query = `
    SELECT
      rd.itemValue as contractType,
      rd.itemLabel as contractTypeLabel,
      rd.sortOrder,
      ctp.propertyName,
      ctp.propertyValue
    FROM ReferenceData rd
    LEFT JOIN ContractTypeProperties ctp ON rd.itemValue = ctp.contractType AND ctp.isActive = 1
    WHERE rd.module = 'contracts'
    AND rd.listName = 'contractType'
    AND rd.isActive = 1
    AND rd.isDeleted = 0
    ORDER BY rd.sortOrder ASC, ctp.propertyName ASC
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error getting contract types with properties:', err);
      return res.status(500).json({ error: err.message });
    }

    // Group by contract type
    const contractTypes = {};
    rows.forEach(row => {
      if (!contractTypes[row.contractType]) {
        contractTypes[row.contractType] = {
          contractType: row.contractType,
          contractTypeLabel: row.contractTypeLabel,
          sortOrder: row.sortOrder,
          properties: {}
        };
      }

      if (row.propertyName) {
        contractTypes[row.contractType].properties[row.propertyName] = row.propertyValue;
      }
    });

    const result = Object.values(contractTypes);
    console.log(`Contract types with properties retrieved: ${result.length} types`);
    res.json(result);
  });
});

    const PORT = process.env.PORT || 5001;
    const HOST = process.env.HOST || '0.0.0.0';

    const server = app.listen(PORT, HOST, () => {
      console.log('🎉 ===================================');
      console.log('🚀 Contract Management Server Started');
      console.log('🎉 ===================================');
      console.log(`🌟 Server running on http://localhost:${PORT}`);
      console.log(`📱 Mobile access: http://[YOUR_IP]:${PORT}`);
      console.log('📊 Database ready for connections');
      console.log('✅ All systems operational');
      console.log('💡 To access from mobile, use your computer\'s IP address instead of localhost');
      console.log('🎉 ===================================');
    });

    // Handle server errors
    server.on('error', (error) => {
      console.error('❌ Server error:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use. Please close other applications using this port.`);
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// ===== ERROR HANDLER =====
// Import and use the error handler middleware
const errorHandler = require("./errorHandler.cjs");
app.use(errorHandler);

// Enhanced process termination handling
function gracefulShutdown(signal) {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

  if (db) {
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err);
      } else {
        console.log('✅ Database closed successfully');
      }
      console.log('👋 Server shutdown complete');
      process.exit(0);
    });
  } else {
    console.log('👋 Server shutdown complete');
    process.exit(0);
  }
}

// Handle different termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

// Start the server
startServer();
