const sqlite3 = require('sqlite3').verbose();

// Connect to database
const db = new sqlite3.Database('./contract-app.sqlite', (err) => {
  if (err) {
    console.error('Error connecting to database:', err.message);
    return;
  }
  console.log('Connected to SQLite database');
});

// Insert test contract with minimal required fields
const contractSQL = `
  INSERT INTO Contracts (
    contractNumber, contractSubject, clientId, contractType, contractDate, contractSigningDate, startDate, contractDurationYears,
    totalContractValue, monthlyAmount, paymentDay
  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`;

const contractData = [
  'TEST-2025-002',
  'عقد إيجار مستودعات - المنطقة الصناعية',
  1, // clientId
  'عقد إيجار',
  '2025-01-15', // contractDate
  '2025-01-15', // contractSigningDate
  '2025-02-01', // startDate
  5, // 5 years
  300000,
  5000,
  15 // payment day
];

db.run(contractSQL, contractData, function(err) {
  if (err) {
    console.error('Error inserting contract:', err.message);
  } else {
    console.log('✅ Contract created successfully with ID:', this.lastID);
  }
  
  // Close database
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err.message);
    } else {
      console.log('Database connection closed');
    }
  });
});
