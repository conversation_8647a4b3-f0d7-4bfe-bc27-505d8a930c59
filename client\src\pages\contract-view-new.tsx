import React from "react";
import { useQuery } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/hooks/use-language";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import {
  FileText,
  User,
  Calculator,
  Calendar,
  TrendingUp,
  ArrowLeft,
  Download,
  Printer,
  RefreshCw
} from "lucide-react";

export default function ContractViewNew() {
  const { id } = useParams();
  const [, setLocation] = useLocation();
  const { language, isRTL } = useLanguage();
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();

  // إضافة CSS للطباعة
  React.useEffect(() => {
    const printStyles = `
      @media print {
        /* إخفاء السايد بار والهيدر فقط */
        .sidebar, .header, nav, .navigation, [data-sidebar], [data-header] {
          display: none !important;
        }

        /* إخفاء عناصر التنقل */
        .print\\:hidden {
          display: none !important;
        }

        /* تحسين الصفحة للطباعة */
        @page {
          margin: 0.5in;
          size: A4;
        }

        /* تحسين الجسم */
        body {
          font-size: 10px !important;
          line-height: 1.2 !important;
          margin: 0 !important;
          padding: 0 !important;
          background: white !important;
        }

        /* تحسين الحاوي الرئيسي */
        .print-container {
          width: 100% !important;
          max-width: none !important;
          margin: 0 !important;
          padding: 5px !important;
        }

        /* تقليل المسافات */
        .print-section {
          margin-bottom: 4px !important;
          padding: 3px !important;
        }

        /* تحسين الجداول */
        table {
          font-size: 9px !important;
          width: 100% !important;
          border-collapse: collapse !important;
        }

        th, td {
          padding: 2px 3px !important;
          font-size: 9px !important;
          border: 1px solid #ccc !important;
        }

        /* تحسين العناوين */
        h1 { font-size: 16px !important; margin: 3px 0 !important; }
        h2 { font-size: 14px !important; margin: 2px 0 !important; }
        h3 { font-size: 12px !important; margin: 2px 0 !important; }

        /* تحسين الكروت */
        .print-card {
          border: 1px solid #ddd !important;
          margin-bottom: 4px !important;
          padding: 3px !important;
          break-inside: avoid;
        }

        /* تحسين الهيدر */
        .print-header {
          padding: 4px !important;
          margin-bottom: 4px !important;
        }

        /* إضافة اسم المستخدم في الأسفل */
        .print-footer {
          position: fixed;
          bottom: 10px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 8px;
          color: #666;
          text-align: center;
        }

        /* تحسين المساحات */
        .space-y-8 > * + * {
          margin-top: 4px !important;
        }

        .space-y-6 > * + * {
          margin-top: 3px !important;
        }

        .gap-4 {
          gap: 3px !important;
        }

        .p-6 {
          padding: 3px !important;
        }

        /* إزالة مساحات السايد بار */
        .ml-64, .pl-64 {
          margin-left: 0 !important;
          padding-left: 0 !important;
        }

        /* تحسين الشبكة للطباعة */
        .grid {
          display: grid !important;
          gap: 2px !important;
        }
      }
    `;

    const styleElement = document.createElement('style');
    styleElement.textContent = printStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Fetch contract data with enhanced calculation results
  const { data: contractResponse, isLoading, error, refetch } = useQuery({
    queryKey: ['contract', id],
    queryFn: async () => {
      const response = await fetch(`http://localhost:5001/api/contracts/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch contract');
      }
      return response.json();
    },
    enabled: !!id,
  });

  // Fetch receivables for this contract
  const { data: receivablesResponse } = useQuery({
    queryKey: ['receivables', id],
    queryFn: async () => {
      const response = await fetch(`http://localhost:5001/api/receivables?contractId=${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch receivables');
      }
      return response.json();
    },
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل بيانات العقد...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">خطأ في تحميل بيانات العقد</p>
          <Button onClick={() => refetch()}>إعادة المحاولة</Button>
        </div>
      </div>
    );
  }

  if (!contractResponse) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p>العقد غير موجود</p>
      </div>
    );
  }

  const contract = contractResponse;
  const calculationResults = contractResponse.calculationResults || {};
  const receivables = receivablesResponse?.receivables || [];

  // Debug: Log receivables data
  console.log('🔍 Receivables Response:', receivablesResponse);
  console.log('🔍 Receivables Array:', receivables);

  // الحصول على اسم المستخدم للطباعة
  const currentUser = "المستخدم الحالي"; // يمكن استبداله بالمستخدم الفعلي من السياق





  return (
    <div className="space-y-6 print-container" dir="rtl">
      <div className="space-y-8 p-6 bg-white print:p-3 print:space-y-3" dir="rtl">
        {/* Contract Header */}
        <div className="text-center border-b-2 border-blue-600 pb-6 print:pb-3 print-header">
          <div className="bg-gradient-to-r from-blue-50 to-green-50 p-6 print:p-3 rounded-lg relative print:bg-white">
            {/* أزرار التحكم */}
            <div className="absolute top-4 left-4 flex items-center space-x-2 space-x-reverse print:hidden">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                تحميل
              </Button>
              <Button variant="outline" size="sm" onClick={() => window.print()}>
                <Printer className="h-4 w-4 mr-2" />
                طباعة
              </Button>
            </div>

            {/* زر العودة */}
            <div className="absolute top-4 right-4 print:hidden">
              <Button variant="outline" size="sm" onClick={() => setLocation('/contracts')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة
              </Button>
            </div>

            {/* النص في الوسط */}
            <div className="pt-8 print:pt-2">
              <h1 className="text-2xl print:text-base font-bold text-gray-800 mb-2 print:mb-1">كشف حساب عقد</h1>
              <div className="text-lg print:text-sm font-semibold text-blue-600 mb-2 print:mb-1">
                {contract.contractNumber}
              </div>
              <div className="text-sm print:text-xs text-gray-600">
                تاريخ العرض: {new Date().toLocaleDateString('ar-EG')} - {new Date().toLocaleTimeString('ar-EG')}
              </div>
            </div>
          </div>
        </div>

        {/* فوتر الطباعة - اسم المستخدم */}
        <div className="print-footer hidden print:block">
          طُبع بواسطة: {currentUser} | {new Date().toLocaleDateString('ar-EG')} - {new Date().toLocaleTimeString('ar-EG')}
        </div>

        {/* المقطع الأول: البيانات الأساسية */}
        <Card className="border-2 border-blue-200 print-card print:border print:border-gray-300">
          <CardHeader className="bg-blue-50 print:bg-white print:p-2">
            <CardTitle className="text-center flex items-center justify-center gap-2 text-blue-800 print:text-black print:text-sm">
              <User className="h-6 w-6 print:h-4 print:w-4" />
              البيانات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 print:p-2" dir="rtl">
            {/* بيانات العميل */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 text-right">
                بيانات العميل
              </h3>
              {(!contract.clientId || contract.clientId === 0) && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <span className="text-sm font-medium">⚠️ لم يتم اختيار عميل للعقد</span>
                  </div>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4 text-sm print:gap-1 print:text-xs" dir="rtl">
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[100px] print:min-w-[60px] print:text-xs">اسم العميل:</span>
                  <span className={`font-semibold print:text-xs ${!contract.clientName ? 'text-red-500' : ''}`}>
                    {contract.clientName || 'غير محدد'}
                  </span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[100px] print:min-w-[60px] print:text-xs">رقم العميل:</span>
                  <span className="font-semibold print:text-xs">
                    {contract.clientNumber || 'غير محدد'}
                  </span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[100px] print:min-w-[60px] print:text-xs">نوع العميل:</span>
                  <span className="font-semibold print:text-xs">{contract.clientType || 'أفراد'}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[100px] print:min-w-[60px] print:text-xs">تصنيف العميل:</span>
                  <span className="font-semibold print:text-xs">{contract.clientClassification || 'عادي'}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[100px] print:min-w-[60px] print:text-xs">عنوان العميل:</span>
                  <span className="font-semibold print:text-xs">{contract.clientAddress || 'غير محدد'}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[100px] print:min-w-[60px] print:text-xs">هاتف العميل:</span>
                  <span className="font-semibold print:text-xs">{contract.clientPhoneWhatsapp || 'غير محدد'}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 col-span-2 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[100px] print:min-w-[60px] print:text-xs">الضامن المالي:</span>
                  <span className="font-semibold print:text-xs">
                    {contract?.financialGuarantorId && contract.financialGuarantorId > 0
                      ? (contract.financialGuarantorName || 'يوجد ضامن')
                      : 'لا يوجد ضامن'}
                  </span>
                </div>
              </div>
            </div>

            {/* بيانات العقد الأساسية */}
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 text-right">
                بيانات العقد الأساسية
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm print:gap-1 print:text-xs" dir="rtl">
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[120px] print:min-w-[70px] print:text-xs">رقم العقد:</span>
                  <span className="font-semibold text-blue-600 print:text-xs print:text-black">{contract.contractNumber}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[120px] print:min-w-[70px] print:text-xs">موضوع العقد:</span>
                  <span className="font-semibold print:text-xs">{contract.contractSubject || 'غير محدد'}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[120px] print:min-w-[70px] print:text-xs">مدة العقد:</span>
                  <span className="font-semibold print:text-xs">{calculationResults?.summary?.totalYears || contract.contractDurationYears || 'غير محدد'} سنة</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[120px] print:min-w-[70px] print:text-xs">حالة العقد:</span>
                  <Badge variant={contract.contractStatus === 'نشط' ? 'default' : 'secondary'} className="print:text-xs print:bg-transparent print:border print:border-gray-400">
                    {contract.contractStatus || 'نشط'}
                  </Badge>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[120px] print:min-w-[70px] print:text-xs">مالك الأصل:</span>
                  <span className="font-semibold print:text-xs">{contract.assetOwner || 'غير محدد'}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[120px] print:min-w-[70px] print:text-xs">الإدارة المسئولة:</span>
                  <span className="font-semibold print:text-xs">{contract.responsibleDepartment || 'غير محدد'}</span>
                </div>
                <div className="flex items-center gap-3 print:gap-1 col-span-2 print:mb-0">
                  <span className="font-medium text-gray-600 min-w-[120px] print:min-w-[70px] print:text-xs">المنطقة:</span>
                  <span className="font-semibold print:text-xs">{contract.region || 'غير محدد'}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* المقطع الثاني: مكونات العقد */}
        {contract.products && contract.products.length > 0 && (
          <Card className="border-2 border-green-200 print-card print:border print:border-gray-300">
            <CardHeader className="bg-green-50 print:bg-white print:p-2">
              <CardTitle className="text-center flex items-center justify-center gap-2 text-green-800 print:text-black print:text-sm">
                <FileText className="h-6 w-6 print:h-4 print:w-4" />
                مكونات العقد
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 print:p-2" dir="rtl">
              <div className="space-y-4">
                {contract.products.map((product: any, index: number) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="grid grid-cols-2 gap-4 text-sm" dir="rtl">
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-gray-600 min-w-[120px]">اسم المنتج:</span>
                        <span className="font-semibold">{product.productLabel || `منتج ${index + 1}`}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-gray-600 min-w-[120px]">المساحة:</span>
                        <span className="font-semibold">{product.area} متر مربع</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-gray-600 min-w-[120px]">سعر المتر:</span>
                        <span className="font-semibold">{formatCurrency(product.meterPrice)}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-gray-600 min-w-[120px]">نوع الفوترة:</span>
                        <span className="font-semibold">{product.billingType}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-gray-600 min-w-[120px]">تاريخ التفعيل:</span>
                        <span className="font-semibold">{formatDate(product.activationDate)}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-gray-600 min-w-[120px]">تاريخ الانتهاء:</span>
                        <span className="font-semibold">{formatDate(product.endDate)}</span>
                      </div>
                      <div className="flex items-center gap-3 col-span-2">
                        <span className="font-medium text-gray-600 min-w-[120px]">نسبة الزيادة السنوية:</span>
                        <span className="font-semibold text-orange-600">
                          {product.hasAnnualIncrease && product.increaseValue > 0
                            ? `${product.increaseValue}% من السنة الثانية`
                            : 'لا توجد زيادة'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* المقطع الثالث: ملخص الالتزامات المالية */}
        <Card className="border-2 border-purple-200 print-card print:border print:border-gray-300">
          <CardHeader className="bg-purple-50 print:bg-white print:p-2">
            <CardTitle className="text-center flex items-center justify-center gap-2 text-purple-800 print:text-black print:text-sm">
              <Calculator className="h-6 w-6 print:h-4 print:w-4" />
              ملخص الالتزامات المالية
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 print:p-2" dir="rtl">
            <div className="grid grid-cols-2 gap-6">
              {/* الالتزامات الأساسية */}
              <div className="space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(calculationResults?.totalValue || contract.totalContractValue || 0)}
                  </div>
                  <div className="text-sm text-blue-800 mt-1">إجمالي قيمة العقد</div>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-center">
                    <div className="text-xl font-bold text-green-600">
                      {formatCurrency(calculationResults?.insuranceAmount || 0)}
                    </div>
                    <div className="text-sm text-green-800 mt-1">إجمالي التأمين</div>
                  </div>
                  <div className="text-center mt-2 text-xs text-gray-600">
                    نسبة التأمين: {contract.finalInsuranceRate || 0}%
                  </div>
                </div>
              </div>

              {/* الدفعات والغرامات */}
              <div className="space-y-4">
                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="text-center">
                    <div className="text-xl font-bold text-orange-600">
                      {formatCurrency(calculationResults?.advancePaymentAmount || contract.advancePaymentAmount || 0)}
                    </div>
                    <div className="text-sm text-orange-800 mt-1">إجمالي الدفعة المقدمة</div>
                  </div>
                  <div className="text-center mt-2 text-xs text-gray-600">
                    عدد الأشهر: {contract.advancePaymentMonths || 0} شهر
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="bg-red-50 p-3 rounded text-center">
                    <div className="font-bold text-red-600">
                      {contract.lateFeeValue || 0}%
                    </div>
                    <div className="text-xs text-red-800">نسبة غرامة التأخير</div>
                  </div>
                  <div className="bg-red-50 p-3 rounded text-center">
                    <div className="font-bold text-red-600">
                      {formatCurrency(contract.bouncedCheckFeeValue || 0)}
                    </div>
                    <div className="text-xs text-red-800">قيمة غرامة الارتداد</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* المقطع الرابع: ملخص الإجماليات السنوية */}
        {calculationResults?.yearlyAmounts && calculationResults.yearlyAmounts.length > 0 && (
          <Card className="border-2 border-indigo-200">
            <CardHeader className="bg-indigo-50">
              <CardTitle className="text-center flex items-center justify-center gap-2 text-indigo-800">
                <Calendar className="h-6 w-6" />
                ملخص الإجماليات السنوية
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6" dir="rtl">
              <div className="grid gap-4">
                {calculationResults.yearlyAmounts.map((yearData: any, index: number) => (
                  <div key={index} className="bg-gradient-to-r from-indigo-50 to-blue-50 p-4 rounded-lg border">
                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div className="text-center">
                        <div className="font-bold text-indigo-600 text-lg">السنة {yearData.year}</div>
                        <div className="text-xs text-gray-600">
                          {new Date().getFullYear() + index} م
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-800">
                          {formatCurrency(yearData.totalAmount)}
                        </div>
                        <div className="text-xs text-gray-600">إجمالي السنة</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-green-600">
                          {yearData.increaseRate ? `${yearData.increaseRate}%` : 'لا توجد زيادة'}
                        </div>
                        <div className="text-xs text-gray-600">نسبة الزيادة</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-blue-600">
                          {yearData.installmentsCount || 12}
                        </div>
                        <div className="text-xs text-gray-600">عدد الأقساط</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* المقطع الخامس: الإيرادات السنوية */}
        {calculationResults?.yearlyAmounts && calculationResults.yearlyAmounts.length > 0 ? (
          <Card className="border-2 border-emerald-200">
            <CardHeader className="bg-emerald-50">
              <CardTitle className="text-center flex items-center justify-center gap-2 text-emerald-800">
                <TrendingUp className="h-6 w-6" />
                الإيرادات السنوية
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6" dir="rtl">
              <div className="grid grid-cols-1 gap-4">
                {calculationResults.yearlyAmounts.map((yearData: any, index: number) => {
                  // حساب السنة الميلادية بناءً على تاريخ بداية العقد
                  const contractStartYear = contract.products?.[0]?.activationDate
                    ? new Date(contract.products[0].activationDate).getFullYear()
                    : new Date().getFullYear();
                  const calendarYear = contractStartYear + index;

                  return (
                    <div key={index} className="flex justify-between items-center p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border">
                      <div className="text-right">
                        <div className="text-2xl font-bold text-emerald-600">
                          {formatCurrency(yearData.totalAmount)}
                        </div>
                        <div className="text-sm text-gray-600">إجمالي الإيراد</div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div>
                          <div className="font-semibold text-gray-800">السنة الميلادية</div>
                          <div className="text-sm text-gray-600">السنة {yearData.year} من العقد</div>
                        </div>
                        <div className="bg-emerald-100 p-3 rounded-full">
                          <span className="font-bold text-emerald-700">
                            {calendarYear}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* إجمالي الإيرادات */}
              <div className="mt-6 p-4 bg-gradient-to-r from-emerald-100 to-green-100 rounded-lg border-2 border-emerald-300">
                <div className="flex justify-between items-center">
                  <div className="text-3xl font-bold text-emerald-700">
                    {formatCurrency(calculationResults.totalValue || 0)}
                  </div>
                  <div className="font-bold text-lg text-emerald-800">
                    إجمالي الإيرادات المتوقعة
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : null}

        {/* المقطع السادس: كشف حساب الاستحقاقات */}
        <Card className="border-2 border-amber-200 print-card print:border print:border-gray-300">
          <CardHeader className="bg-amber-50 print:bg-white print:p-2">
            <CardTitle className="text-center flex items-center justify-center gap-2 text-amber-800 print:text-black print:text-sm">
              <FileText className="h-6 w-6 print:h-4 print:w-4" />
              كشف حساب الاستحقاقات
            </CardTitle>
          </CardHeader>
          <CardContent dir="rtl" className="print:p-2">
            <div className="overflow-x-auto">
              <table className="w-full text-sm print:text-xs">
                <thead>
                  <tr className="border-b bg-gray-50">
                    <th className="p-2 text-center text-xs">رقم الفاتورة</th>
                    <th className="p-2 text-center text-xs">تاريخ الاستحقاق</th>
                    <th className="p-2 text-center text-xs">المبلغ المستحق</th>
                    <th className="p-2 text-center text-xs">حالة السداد</th>
                    <th className="p-2 text-center text-xs">المبلغ المسدد</th>
                    <th className="p-2 text-center text-xs">المتبقي</th>
                    <th className="p-2 text-center text-xs">رقم الشيك</th>
                    <th className="p-2 text-center text-xs">حالة الشيك</th>
                  </tr>
                </thead>
                <tbody>
                  {receivables.length > 0 ? receivables.map((receivable: any, index: number) => {
                    // حساب اللوجيك الجديد للمبالغ
                    const today = new Date();
                    const dueDate = new Date(receivable.dueDate);
                    const isDue = dueDate <= today; // هل حان موعد الاستحقاق؟
                    const paidAmount = receivable.paidAmount || 0;
                    const totalAmount = receivable.amount || 0;

                    // حساب المتبقي حسب اللوجيك الجديد
                    const remainingAmount = isDue ? (totalAmount - paidAmount) : 0;

                    // تحديد حالة السداد
                    let statusBadge = null;

                    if (paidAmount >= totalAmount) {
                      // تم السداد كاملاً
                      if (!isDue) {
                        statusBadge = <Badge className="bg-blue-100 text-blue-800 text-xs">مسدد مقدماً</Badge>;
                      } else {
                        statusBadge = <Badge className="bg-green-100 text-green-800 text-xs">مسدد</Badge>;
                      }
                    } else if (paidAmount > 0) {
                      // سداد جزئي
                      if (!isDue) {
                        statusBadge = <Badge className="bg-cyan-100 text-cyan-800 text-xs">سداد جزئي مقدم</Badge>;
                      } else {
                        statusBadge = <Badge className="bg-orange-100 text-orange-800 text-xs">سداد جزئي</Badge>;
                      }
                    } else {
                      // لم يتم السداد
                      if (!isDue) {
                        statusBadge = <Badge className="bg-gray-100 text-gray-800 text-xs">لم يحن موعده</Badge>;
                      } else {
                        // تحديد إذا كان متأخر أم مستحق
                        const daysDiff = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
                        if (daysDiff > 30) {
                          statusBadge = <Badge className="bg-red-100 text-red-800 text-xs">متأخر</Badge>;
                        } else {
                          statusBadge = <Badge className="bg-yellow-100 text-yellow-800 text-xs">مستحق</Badge>;
                        }
                      }
                    }

                    const getCheckStatusBadge = (status: string) => {
                      if (!status) return <span className="text-gray-400 text-xs">لا يوجد</span>;

                      switch (status) {
                        case 'في الخزينة':
                          return <Badge className="bg-blue-100 text-blue-800 text-xs">في الخزينة</Badge>;
                        case 'في العهدة':
                          return <Badge className="bg-purple-100 text-purple-800 text-xs">في العهدة</Badge>;
                        case 'قيد التحصيل':
                          return <Badge className="bg-orange-100 text-orange-800 text-xs">قيد التحصيل</Badge>;
                        case 'محصل':
                          return <Badge className="bg-green-100 text-green-800 text-xs">محصل</Badge>;
                        case 'مرتد':
                          return <Badge className="bg-red-100 text-red-800 text-xs">مرتد</Badge>;
                        case 'قانوني':
                          return <Badge className="bg-gray-100 text-gray-800 text-xs">قانوني</Badge>;
                        default:
                          return <Badge variant="outline" className="text-xs">{status}</Badge>;
                      }
                    };

                    return (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-2 text-center font-medium text-sm">{receivable.invoiceNumber || `INV-${receivable.id}`}</td>
                        <td className="p-2 text-center text-sm">
                          {formatDate(receivable.dueDate)}
                          {!isDue && (
                            <div className="text-xs text-gray-500 mt-1">
                              (بعد {Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))} يوم)
                            </div>
                          )}
                        </td>
                        <td className="p-2 text-center text-blue-600 font-bold text-sm">
                          {formatCurrency(totalAmount)}
                        </td>
                        <td className="p-2 text-center text-sm">
                          {statusBadge}
                        </td>
                        <td className="p-2 text-center text-green-600 font-bold text-sm">
                          {formatCurrency(paidAmount)}
                          {paidAmount > 0 && !isDue && (
                            <div className="text-xs text-blue-600 mt-1">(مقدماً)</div>
                          )}
                        </td>
                        <td className="p-2 text-center font-bold text-sm">
                          <span className={remainingAmount > 0 ? "text-red-600" : "text-gray-400"}>
                            {formatCurrency(remainingAmount)}
                          </span>
                          {!isDue && remainingAmount === 0 && paidAmount === 0 && (
                            <div className="text-xs text-gray-500 mt-1">(لم يحن موعده)</div>
                          )}
                        </td>
                        <td className="p-2 text-center text-sm">
                          {receivable.checkNumber || <span className="text-gray-400 text-xs">لا يوجد</span>}
                        </td>
                        <td className="p-2 text-center text-sm">
                          {getCheckStatusBadge(receivable.checkStatus)}
                        </td>
                      </tr>
                    );
                  }) : (
                    <tr>
                      <td colSpan={8} className="p-4 text-center text-gray-500">
                        لا توجد استحقاقات لهذا العقد
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* ملخص الاستحقاقات */}
            {receivables.length > 0 && (
              <div className="mt-6 grid grid-cols-5 gap-4">
                <div className="bg-blue-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {formatCurrency(receivables.reduce((sum: number, r: any) => sum + (r.amount || 0), 0))}
                  </div>
                  <div className="text-xs text-blue-800">إجمالي قيمة العقد</div>
                </div>
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-green-600">
                    {formatCurrency(receivables.reduce((sum: number, r: any) => sum + (r.paidAmount || 0), 0))}
                  </div>
                  <div className="text-xs text-green-800">إجمالي المسدد</div>
                </div>
                <div className="bg-red-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-red-600">
                    {formatCurrency(receivables.reduce((sum: number, r: any) => {
                      const today = new Date();
                      const dueDate = new Date(r.dueDate);
                      const isDue = dueDate <= today;
                      const remainingAmount = isDue ? ((r.amount || 0) - (r.paidAmount || 0)) : 0;
                      return sum + remainingAmount;
                    }, 0))}
                  </div>
                  <div className="text-xs text-red-800">المتبقي المستحق</div>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-gray-600">
                    {formatCurrency(receivables.reduce((sum: number, r: any) => {
                      const today = new Date();
                      const dueDate = new Date(r.dueDate);
                      const isDue = dueDate <= today;
                      const futureAmount = !isDue ? ((r.amount || 0) - (r.paidAmount || 0)) : 0;
                      return sum + futureAmount;
                    }, 0))}
                  </div>
                  <div className="text-xs text-gray-800">لم يحن موعده</div>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg text-center">
                  <div className="text-lg font-bold text-purple-600">
                    {receivables.filter((r: any) => r.checkNumber).length}
                  </div>
                  <div className="text-xs text-purple-800">عدد الشيكات</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}