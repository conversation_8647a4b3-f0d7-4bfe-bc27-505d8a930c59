import { QueryClient, QueryFunction } from "@tanstack/react-query";

// Use proxy in development, direct URL in production
const API_BASE_URL = import.meta.env.DEV ? '' : 'http://localhost:5001';

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  // Add base URL if not already present
  const fullUrl = url.startsWith("http") ? url : `${API_BASE_URL}${url}`;

  console.log('🌐 API Request:', {
    method,
    url,
    fullUrl,
    data,
    isDev: import.meta.env.DEV,
    apiBaseUrl: API_BASE_URL
  });

  const res = await fetch(fullUrl, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    // Remove credentials for now to avoid CORS issues with simple server
    // credentials: "include",
  });

  console.log('📡 API Response:', {
    status: res.status,
    statusText: res.statusText,
    ok: res.ok
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const url = queryKey[0] as string;
    const fullUrl = url.startsWith("http") ? url : `${API_BASE_URL}${url}`;

    const res = await fetch(fullUrl, {
      // Remove credentials for now to avoid CORS issues with simple server
      // credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
