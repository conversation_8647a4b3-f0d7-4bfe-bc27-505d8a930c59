import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Search, Filter, FileText, Building2, CheckCircle, XCircle, AlertCircle, AlertTriangle, Scale, CreditCard, Clock, Calendar, DollarSign, TrendingUp, ArrowUpRight, ArrowDownRight, Download } from 'lucide-react';
import { Eye, Edit } from 'lucide-react';
import { ReferenceLists<PERSON><PERSON>t } from '@/components/reference-lists-alert';
import { useDateFormat } from '@/hooks/use-date-format';
import { useCurrency } from '@/hooks/use-currency';
import { useToast } from '@/hooks/use-toast';
import { useQuery } from '@tanstack/react-query';

interface Cheque {
  id: number;
  chequeNumber: string;
  contractId: number;
  clientId: number;
  receivableId?: number;
  bankName: string;
  chequeAmount: number;
  chequeDate: string;
  dueDate: string;
  status: string;
  chequeStatus: string; // Added for table display
  receivedDate: string;
  statusDate?: string;
  entryNumber?: string;
  contractStatusAtReceipt?: string;
  shouldDepositToBank: boolean;
  bounceReason?: string;
  withdrawalReason?: string;
  notes?: string;
  clientName?: string;
  contractNumber?: string;
  contractSubject?: string;
  receivableNumber?: string;
  receivableDescription?: string;
}

interface Bank {
  value: string;
  label: string;
  returnDate?: string;
  returnReason?: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ChequeStats {
  totalCheques: number;
  pendingCheques: number;
  collectedCheques: number;
  bouncedCheques: number;
  totalValue: number;
  pendingValue: number;
}

const Cheques = () => {
  const { formatDateTime, formatDate } = useDateFormat();
  const { formatCurrency } = useCurrency();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [bankFilter, setBankFilter] = useState("all");
  const [selectedCheque, setSelectedCheque] = useState<Cheque | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const { toast } = useToast();

  // Get all cheques from API
  const { data: cheques, isLoading } = useQuery<Cheque[]>({
    queryKey: ["/api/cheques"],
    queryFn: async () => {
      const response = await fetch("/api/cheques");
      if (!response.ok) throw new Error("Failed to fetch cheques");
      return response.json();
    },
  });

  // Mock data for demonstration (remove when API is working)
  const mockCheques: Cheque[] = [
    {
      id: 1,
      chequeNumber: "CHQ-001",
      contractId: 1,
      contractNumber: "C-2024-001",
      clientId: 1,
      clientName: "محمد أحمد",
      bankName: "البنك الأهلي المصري",
      chequeAmount: 5000,
      chequeDate: "2024-01-15",
      dueDate: "2024-02-15",
      chequeStatus: "مودع",
      depositDate: "2024-01-16",
      notes: "شيك دفعة شهر يناير",
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-16T09:00:00Z"
    },
    {
      id: 2,
      chequeNumber: "CHQ-002",
      contractId: 2,
      contractNumber: "C-2024-002",
      clientId: 2,
      clientName: "فاطمة علي",
      bankName: "بنك مصر",
      chequeAmount: 3000,
      chequeDate: "2024-01-20",
      dueDate: "2024-02-20",
      chequeStatus: "مرتد",
      depositDate: "2024-01-21",
      bounceReason: "رصيد غير كافي",
      notes: "شيك مرتد - تم التواصل مع العميل",
      createdAt: "2024-01-20T10:00:00Z",
      updatedAt: "2024-01-22T14:30:00Z"
    }
  ];

  const actualCheques = cheques || mockCheques;

  const chequeStats: ChequeStats = {
    totalCheques: actualCheques.length,
    pendingCheques: actualCheques.filter(c => c.checkStatus === 'عهدة').length,
    collectedCheques: actualCheques.filter(c => c.checkStatus === 'محصل').length,
    bouncedCheques: actualCheques.filter(c => c.chequeStatus === 'مرتد').length,
    totalValue: actualCheques.reduce((sum, c) => sum + c.chequeAmount, 0),
    pendingValue: actualCheques.filter(c => c.chequeStatus === 'عهدة').reduce((sum, c) => sum + c.chequeAmount, 0)
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'محصل': return 'bg-green-100 text-green-800';
      case 'تحت التحصيل': return 'bg-blue-100 text-blue-800';
      case 'مرتد': return 'bg-red-100 text-red-800';
      case 'أمانة': return 'bg-purple-100 text-purple-800';
      case 'عهدة': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredCheques = actualCheques.filter(cheque => {
    if (!cheque) return false;

    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = searchTerm === "" ||
      (cheque.checkNumber && cheque.checkNumber.toLowerCase().includes(searchLower)) ||
      (cheque.clientName && cheque.clientName.toLowerCase().includes(searchLower)) ||
      (cheque.contractNumber && cheque.contractNumber.toLowerCase().includes(searchLower)) ||
      (cheque.bankName && cheque.bankName.toLowerCase().includes(searchLower));

    const matchesStatus = statusFilter === "all" || cheque.checkStatus === statusFilter;
    const matchesBank = bankFilter === "all" || (cheque.bankName && cheque.bankName === bankFilter);

    return matchesSearch && matchesStatus && matchesBank;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <CreditCard className="h-6 w-6 text-blue-600" />
                إدارة الشيكات
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                متابعة وإدارة شيكات العملاء
              </p>
            </div>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              إضافة شيك جديد
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Reference Lists Alert */}
      <ReferenceListsAlert pageName="cheques" />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CreditCard className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">إجمالي الشيكات</p>
                <p className="text-2xl font-bold">{chequeStats.totalCheques}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">شيكات معلقة</p>
                <p className="text-2xl font-bold">{chequeStats.pendingCheques}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">شيكات محصلة</p>
                <p className="text-2xl font-bold">{chequeStats.collectedCheques}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">شيكات مرتدة</p>
                <p className="text-2xl font-bold">{chequeStats.bouncedCheques}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">البحث</Label>
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="رقم الشيك، العميل، أو رقم العقد..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="status">حالة الشيك</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="معلق">معلق</SelectItem>
                  <SelectItem value="مودع">مودع</SelectItem>
                  <SelectItem value="محصل">محصل</SelectItem>
                  <SelectItem value="مرتد">مرتد</SelectItem>
                  <SelectItem value="ملغي">ملغي</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="bank">البنك</Label>
              <Select value={bankFilter} onValueChange={setBankFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="جميع البنوك" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع البنوك</SelectItem>
                  <SelectItem value="البنك الأهلي المصري">البنك الأهلي المصري</SelectItem>
                  <SelectItem value="بنك مصر">بنك مصر</SelectItem>
                  <SelectItem value="البنك التجاري الدولي">البنك التجاري الدولي</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end gap-2">
              <Button variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                تصدير
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cheques Table */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الشيكات ({filteredCheques.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الشيك</TableHead>
                  <TableHead>العميل</TableHead>
                  <TableHead>رقم العقد</TableHead>
                  <TableHead>البنك</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>تاريخ الاستحقاق</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCheques.map((cheque) => (
                  <TableRow key={cheque.id}>
                    <TableCell className="font-medium">{cheque.chequeNumber}</TableCell>
                    <TableCell>{cheque.clientName}</TableCell>
                    <TableCell>{cheque.contractNumber}</TableCell>
                    <TableCell>{cheque.bankName}</TableCell>
                    <TableCell>{formatCurrency(cheque.chequeAmount)}</TableCell>
                    <TableCell>{formatDate(cheque.dueDate)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(cheque.chequeStatus)}>
                        {cheque.chequeStatus}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedCheque(cheque);
                            setShowDetails(true);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Cheques;
