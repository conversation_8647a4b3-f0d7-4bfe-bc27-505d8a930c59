// ===== VALIDATION UTILITIES =====
// Author: Augment Code
// Description: Validation and sanitization utilities for the Contract Management System

/**
 * Sanitize string input
 * @param {string} str - Input string
 * @returns {string} - Sanitized string
 */
function sanitizeString(str) {
  if (typeof str !== 'string') return '';
  return str.trim().replace(/[<>]/g, '');
}

/**
 * Sanitize number input
 * @param {any} num - Input number
 * @returns {number} - Sanitized number
 */
function sanitizeNumber(num) {
  const parsed = parseFloat(num);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Validate client data
 * @param {object} data - Client data object
 * @returns {object} - Validation result
 */
function validateClientData(data) {
  const errors = [];

  console.log('🔍 Validating client email:', data.clientEmail);

  if (!data.clientName || !sanitizeString(data.clientName)) {
    errors.push('اسم العميل مطلوب');
  }

  if (!data.clientId || !sanitizeString(data.clientId)) {
    errors.push('رقم العميل مطلوب');
  }

  if (!data.clientType || !sanitizeString(data.clientType)) {
    errors.push('نوع العميل مطلوب');
  }

  if (!data.clientPhoneWhatsapp || !sanitizeString(data.clientPhoneWhatsapp)) {
    errors.push('رقم الهاتف/واتساب مطلوب');
  }

  if (data.clientEmail && data.clientEmail.trim() && data.clientEmail !== 'لا يوجد' && !isValidEmail(data.clientEmail)) {
    console.log('❌ Email validation failed for:', data.clientEmail);
    errors.push('صيغة البريد الإلكتروني غير صحيحة');
  }

  if (data.clientPhoneWhatsapp && data.clientPhoneWhatsapp.trim() && !isValidPhone(data.clientPhoneWhatsapp)) {
    errors.push('رقم الهاتف غير صحيح');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate contract data
 * @param {object} data - Contract data object
 * @returns {object} - Validation result
 */
function validateContractData(data) {
  const errors = [];
  
  if (!data.contractNumber || !sanitizeString(data.contractNumber)) {
    errors.push('رقم العقد مطلوب');
  }
  
  if (!data.clientId) {
    errors.push('معرف العميل مطلوب');
  }
  
  if (!data.contractType || !sanitizeString(data.contractType)) {
    errors.push('نوع العقد مطلوب');
  }
  
  if (!data.startDate) {
    errors.push('تاريخ بداية العقد مطلوب');
  }
  
  if (!data.totalContractValue || sanitizeNumber(data.totalContractValue) <= 0) {
    errors.push('قيمة العقد الإجمالية مطلوبة ويجب أن تكون أكبر من صفر');
  }
  
  if (!data.contractDurationYears || sanitizeNumber(data.contractDurationYears) <= 0) {
    errors.push('مدة العقد بالسنوات مطلوبة ويجب أن تكون أكبر من صفر');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate product data
 * @param {object} data - Product data object
 * @returns {object} - Validation result
 */
function validateProductData(data) {
  const errors = [];
  
  if (!data.productLabel || !sanitizeString(data.productLabel)) {
    errors.push('اسم المنتج مطلوب');
  }
  
  if (!data.area || sanitizeNumber(data.area) <= 0) {
    errors.push('المساحة مطلوبة ويجب أن تكون أكبر من صفر');
  }
  
  if (!data.meterPrice || sanitizeNumber(data.meterPrice) <= 0) {
    errors.push('سعر المتر مطلوب ويجب أن يكون أكبر من صفر');
  }
  
  if (!data.billingType || !sanitizeString(data.billingType)) {
    errors.push('نوع الفوترة مطلوب');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate settings data
 * @param {object} data - Settings data object
 * @returns {object} - Validation result
 */
function validateSettingsData(data) {
  const errors = [];
  
  if (!data.companyName || !sanitizeString(data.companyName)) {
    errors.push('اسم الشركة مطلوب');
  }
  
  if (!data.programName || !sanitizeString(data.programName)) {
    errors.push('اسم البرنامج مطلوب');
  }
  
  if (!data.currency || !sanitizeString(data.currency)) {
    errors.push('العملة مطلوبة');
  }
  
  if (data.companyEmail && data.companyEmail.trim() && !isValidEmail(data.companyEmail)) {
    errors.push('صيغة البريد الإلكتروني للشركة غير صحيحة');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate email format
 * @param {string} email - Email address
 * @returns {boolean} - True if valid
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number format
 * @param {string} phone - Phone number
 * @returns {boolean} - True if valid
 */
function isValidPhone(phone) {
  // Basic phone validation - adjust regex as needed for your requirements
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,15}$/;
  return phoneRegex.test(phone);
}

module.exports = {
  validateClientData,
  validateContractData,
  validateProductData,
  validateSettingsData,
  sanitizeString,
  sanitizeNumber,
  isValidEmail,
  isValidPhone
};
