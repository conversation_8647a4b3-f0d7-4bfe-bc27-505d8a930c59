import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertCashReceiptSchema, insertChequeReceiptSchema, type InsertCashReceipt, type InsertChequeReceipt, type CashReceipt, type ChequeReceipt } from "@shared/schema";
import { useCurrency } from "@/hooks/use-currency";
import { useDateFormat } from "@/hooks/use-date-format";
import {
  Plus,
  Wallet,
  Search,
  Filter,
  Edit,
  Trash2,
  Receipt,
  CreditCard,
  FileText,
  Building2,
  Calendar,
  Landmark,
  AlertTriangle
} from "lucide-react";

type TreasuryPayment = (CashReceipt & { paymentMethod: 'نقدي' }) | (ChequeReceipt & { paymentMethod: 'شيك' });
type InsertTreasuryPayment = (InsertCashReceipt & { paymentMethod: 'نقدي' }) | (InsertChequeReceipt & { paymentMethod: 'شيك' });

export default function TreasuryPayments() {
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPayment, setEditingPayment] = useState<TreasuryPayment | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterMethod, setFilterMethod] = useState("all");
  const [selectedContractId, setSelectedContractId] = useState<number | null>(null);
  const [contractReceivables, setContractReceivables] = useState<any[]>([]);
  const [selectedReceivables, setSelectedReceivables] = useState<number[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<'نقدي' | 'شيك'>('نقدي');

  const cashForm = useForm<InsertCashReceipt>({
    resolver: zodResolver(insertCashReceiptSchema),
    defaultValues: {
      paymentType: 'مرتبطة بعقد',
      isActive: true,
    },
  });

  const chequeForm = useForm<InsertChequeReceipt>({
    resolver: zodResolver(insertChequeReceiptSchema),
    defaultValues: {
      paymentType: 'مرتبطة بعقد',
      checkStatus: 'عهدة',
      isActive: true,
    },
  });

  const currentForm = paymentMethod === 'نقدي' ? cashForm : chequeForm;

  // Get all cash receipts
  const { data: cashReceipts, isLoading: cashLoading } = useQuery<CashReceipt[]>({
    queryKey: ['/api/cash-receipts'],
  });

  // Get all cheque receipts
  const { data: chequeReceipts, isLoading: chequeLoading } = useQuery<ChequeReceipt[]>({
    queryKey: ['/api/cheque-receipts'],
  });

  // Get contracts for dropdown
  const { data: contracts } = useQuery({
    queryKey: ['/api/contracts'],
  });

  // Function to fetch contract receivables
  const fetchContractReceivables = async (contractId: number) => {
    try {
      const response = await fetch(`/api/contracts/${contractId}/receivables`);
      const data = await response.json();
      setContractReceivables(data);
      setSelectedReceivables([]); // Reset selected receivables when contract changes
    } catch (error) {
      console.error('Error fetching contract receivables:', error);
      setContractReceivables([]);
      setSelectedReceivables([]);
    }
  };

  // Function to handle receivable selection
  const handleReceivableSelection = (receivableId: number, isSelected: boolean) => {
    let newSelectedReceivables;
    if (isSelected) {
      newSelectedReceivables = [...selectedReceivables, receivableId];
    } else {
      newSelectedReceivables = selectedReceivables.filter(id => id !== receivableId);
    }
    setSelectedReceivables(newSelectedReceivables);

    // Auto-update amount when selection changes
    const newTotal = contractReceivables
      .filter(r => newSelectedReceivables.includes(r.id))
      .reduce((total, r) => total + (r.remainingAmount || r.amount), 0);

    if (newTotal > 0) {
      const currentForm = paymentMethod === 'نقدي' ? cashForm : chequeForm;
      currentForm.setValue('amount', newTotal);
    }
  };

  // Function to select all receivables
  const handleSelectAllReceivables = (isSelected: boolean) => {
    let newSelectedReceivables;
    if (isSelected) {
      const unpaidReceivables = contractReceivables
        .filter(r => r.status !== 'مدفوع')
        .map(r => r.id);
      newSelectedReceivables = unpaidReceivables;
    } else {
      newSelectedReceivables = [];
    }
    setSelectedReceivables(newSelectedReceivables);

    // Auto-update amount when selection changes
    const newTotal = contractReceivables
      .filter(r => newSelectedReceivables.includes(r.id))
      .reduce((total, r) => total + (r.remainingAmount || r.amount), 0);

    const currentForm = paymentMethod === 'نقدي' ? cashForm : chequeForm;
    currentForm.setValue('amount', newTotal);
  };

  // Calculate total amount for selected receivables
  const calculateSelectedTotal = () => {
    return contractReceivables
      .filter(r => selectedReceivables.includes(r.id))
      .reduce((total, r) => total + (r.remainingAmount || r.amount), 0);
  };

  // Create cash receipt mutation
  const createCashReceiptMutation = useMutation({
    mutationFn: (data: InsertCashReceipt) =>
      fetch('/api/cash-receipts', {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      }).then(res => {
        if (!res.ok) throw new Error("Failed to create cash receipt");
        return res.json();
      }),
    onSuccess: () => {
      toast({
        title: "تم تسجيل الإيصال النقدي",
        description: "تم تسجيل الإيصال النقدي بنجاح!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/cash-receipts'] });
      setDialogOpen(false);
      cashForm.reset();
      setEditingPayment(null);
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في تسجيل الإيصال النقدي",
        description: `خطأ: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Create cheque receipt mutation
  const createChequeReceiptMutation = useMutation({
    mutationFn: (data: InsertChequeReceipt) =>
      fetch('/api/cheque-receipts', {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      }).then(res => {
        if (!res.ok) throw new Error("Failed to create cheque receipt");
        return res.json();
      }),
    onSuccess: () => {
      toast({
        title: "تم تسجيل إيصال الشيك",
        description: "تم تسجيل إيصال الشيك بنجاح وترحيله لصفحة الشيكات!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/cheque-receipts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cheques'] });
      setDialogOpen(false);
      chequeForm.reset();
      setEditingPayment(null);
    },
    onError: (error: any) => {
      toast({
        title: "خطأ في تسجيل إيصال الشيك",
        description: `خطأ: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onSubmit = async (data: any) => {
    // التحقق من وجود استحقاقات مختارة للمدفوعات المرتبطة بعقد
    if (data.paymentType === 'مرتبطة بعقد' && selectedReceivables.length === 0) {
      toast({
        title: "خطأ في البيانات",
        description: "يجب اختيار استحقاق واحد على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      if (data.paymentType === 'مرتبطة بعقد' && selectedReceivables.length > 0) {
        // إنشاء إيصال منفصل لكل استحقاق مختار
        const selectedReceivableData = contractReceivables.filter(r =>
          selectedReceivables.includes(r.id)
        );

        let successCount = 0;
        let totalReceivables = selectedReceivableData.length;

        for (const receivable of selectedReceivableData) {
          const remainingAmount = receivable.remainingAmount || receivable.amount;

          // إنشاء بيانات الإيصال لهذا الاستحقاق
          const receiptData = {
            ...data,
            receivableId: receivable.id,
            receivableDescription: receivable.description,
            receivableStartDate: receivable.dueDate,
            receivableEndDate: receivable.dueDate,
            amount: remainingAmount, // استخدام المبلغ المتبقي للاستحقاق
            paymentStatus: 'كامل', // افتراض أن كل استحقاق سيتم دفعه بالكامل
            // إضافة معرف فريد لرقم الإيصال
            receiptNumber: `${data.receiptNumber}-${receivable.installmentNumber}`,
          };

          try {
            if (paymentMethod === 'نقدي') {
              await createCashReceiptMutation.mutateAsync(receiptData as InsertCashReceipt);
            } else {
              await createChequeReceiptMutation.mutateAsync(receiptData as InsertChequeReceipt);
            }
            successCount++;
          } catch (error) {
            console.error(`Error creating receipt for receivable ${receivable.id}:`, error);
          }
        }

        if (successCount === totalReceivables) {
          toast({
            title: "تم تسجيل الإيصالات بنجاح",
            description: `تم إنشاء ${successCount} إيصال لتغطية الاستحقاقات المختارة`,
          });
        } else if (successCount > 0) {
          toast({
            title: "تم تسجيل بعض الإيصالات",
            description: `تم إنشاء ${successCount} من ${totalReceivables} إيصال`,
            variant: "destructive",
          });
        } else {
          toast({
            title: "فشل في تسجيل الإيصالات",
            description: "لم يتم إنشاء أي إيصال",
            variant: "destructive",
          });
        }

        // إعادة تعيين النموذج والحالة
        setDialogOpen(false);
        setSelectedReceivables([]);
        setContractReceivables([]);
        setSelectedContractId(null);
        if (paymentMethod === 'نقدي') {
          cashForm.reset();
        } else {
          chequeForm.reset();
        }

      } else {
        // للمدفوعات غير المرتبطة بعقد - النظام العادي
        if (paymentMethod === 'نقدي') {
          createCashReceiptMutation.mutate(data as InsertCashReceipt);
        } else {
          createChequeReceiptMutation.mutate(data as InsertChequeReceipt);
        }
      }
    } catch (error) {
      console.error('Error in onSubmit:', error);
      toast({
        title: "خطأ في تسجيل الإيصال",
        description: "حدث خطأ غير متوقع",
        variant: "destructive",
      });
    }
  };

  const openNewPaymentDialog = () => {
    setEditingPayment(null);
    setPaymentMethod('نقدي');
    setSelectedContractId(null);
    setContractReceivables([]);
    setSelectedReceivables([]);
    cashForm.reset({
      paymentType: 'مرتبطة بعقد',
      isActive: true,
    });
    chequeForm.reset({
      paymentType: 'مرتبطة بعقد',
      checkStatus: 'عهدة',
      isActive: true,
    });
    setDialogOpen(true);
  };

  // Combine and filter payments
  const allPayments: TreasuryPayment[] = [
    ...(cashReceipts?.map(r => ({ ...r, paymentMethod: 'نقدي' as const })) || []),
    ...(chequeReceipts?.map(r => ({ ...r, paymentMethod: 'شيك' as const })) || [])
  ];

  const filteredPayments = allPayments.filter(payment => {
    const matchesSearch = searchTerm === "" || 
      payment.receiptNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.clientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.contractNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ('checkNumber' in payment && payment.checkNumber?.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesTypeFilter = filterType === "all" || payment.paymentType === filterType;
    const matchesMethodFilter = filterMethod === "all" || payment.paymentMethod === filterMethod;
    
    return matchesSearch && matchesTypeFilter && matchesMethodFilter;
  });

  const contractPayments = filteredPayments.filter(p => p.paymentType === 'مرتبطة بعقد');
  const nonContractPayments = filteredPayments.filter(p => p.paymentType === 'غير مرتبطة بعقد');
  const cashPayments = filteredPayments.filter(p => p.paymentMethod === 'نقدي');
  const chequePayments = filteredPayments.filter(p => p.paymentMethod === 'شيك');
  const totalAmount = filteredPayments.reduce((sum, p) => sum + (p.amount || 0), 0);

  const isLoading = cashLoading || chequeLoading;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <Wallet className="h-6 w-6 text-blue-600" />
                مدفوعات خزينة الشركة
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                إدارة الإيصالات النقدية وإيصالات الشيكات المستلمة بخزينة الشركة
              </p>
            </div>
            <Button onClick={openNewPaymentDialog} className="gap-2">
              <Plus className="h-4 w-4" />
              إيصال جديد
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي الإيصالات</p>
                <p className="text-2xl font-bold text-blue-600">
                  {isLoading ? <Skeleton className="h-8 w-20" /> : filteredPayments.length}
                </p>
              </div>
              <Receipt className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إيصالات نقدية</p>
                <p className="text-2xl font-bold text-green-600">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : cashPayments.length}
                </p>
              </div>
              <Wallet className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إيصالات شيكات</p>
                <p className="text-2xl font-bold text-orange-600">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : chequePayments.length}
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">مرتبطة بعقود</p>
                <p className="text-2xl font-bold text-purple-600">
                  {isLoading ? <Skeleton className="h-8 w-16" /> : contractPayments.length}
                </p>
              </div>
              <FileText className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي المبالغ</p>
                <p className="text-2xl font-bold text-red-600 currency">
                  {isLoading ? <Skeleton className="h-8 w-24" /> : formatCurrency(totalAmount)}
                </p>
              </div>
              <Building2 className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث برقم الإيصال أو رقم الشيك أو اسم العميل أو رقم العقد..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-48">
                <Filter className="h-4 w-4 ml-2" />
                <SelectValue placeholder="تصفية النوع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأنواع</SelectItem>
                <SelectItem value="مرتبطة بعقد">مرتبطة بعقد</SelectItem>
                <SelectItem value="غير مرتبطة بعقد">غير مرتبطة بعقد</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterMethod} onValueChange={setFilterMethod}>
              <SelectTrigger className="w-48">
                <Wallet className="h-4 w-4 ml-2" />
                <SelectValue placeholder="طريقة الدفع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الطرق</SelectItem>
                <SelectItem value="نقدي">نقدي</SelectItem>
                <SelectItem value="شيك">شيك</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            سجل مدفوعات الخزينة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">رقم الإيصال</TableHead>
                  <TableHead className="text-right">التاريخ</TableHead>
                  <TableHead className="text-right">المبلغ</TableHead>
                  <TableHead className="text-right">طريقة الدفع</TableHead>
                  <TableHead className="text-right">النوع</TableHead>
                  <TableHead className="text-right">التفاصيل</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredPayments.length > 0 ? (
                  filteredPayments.map((payment) => (
                    <TableRow key={`${payment.paymentMethod}-${payment.id}`}>
                      <TableCell className="font-medium">{payment.receiptNumber}</TableCell>
                      <TableCell>{formatDate(payment.paymentDate)}</TableCell>
                      <TableCell className="font-bold text-green-600 currency">
                        {formatCurrency(payment.amount)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {payment.paymentMethod === 'شيك' ? (
                            <>
                              <CreditCard className="h-4 w-4" />
                              <span>شيك</span>
                              {'checkNumber' in payment && (
                                <span className="text-sm text-muted-foreground">
                                  ({payment.checkNumber})
                                </span>
                              )}
                            </>
                          ) : (
                            <>
                              <Wallet className="h-4 w-4" />
                              <span>نقدي</span>
                            </>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={payment.paymentType === 'مرتبطة بعقد' ? 'default' : 'secondary'}>
                          {payment.paymentType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {payment.paymentType === 'مرتبطة بعقد' ? (
                          <div className="text-sm">
                            <div className="font-medium">{payment.clientName}</div>
                            <div className="text-muted-foreground">
                              عقد: {payment.contractNumber}
                            </div>
                            {payment.paymentStatus && (
                              <Badge variant="outline" className="mt-1">
                                سداد {payment.paymentStatus}
                              </Badge>
                            )}
                            {payment.paymentMethod === 'شيك' && 'checkStatus' in payment && (
                              <Badge
                                variant={
                                  payment.checkStatus === 'عهدة' ? 'secondary' :
                                  payment.checkStatus === 'تحت التحصيل' ? 'default' :
                                  'outline'
                                }
                                className="mt-1 ml-2"
                              >
                                {payment.checkStatus}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <div className="text-sm">
                            <Badge variant="outline">
                              {payment.nonContractType}
                            </Badge>
                            {payment.paymentMethod === 'شيك' && 'checkStatus' in payment && (
                              <Badge
                                variant={
                                  payment.checkStatus === 'عهدة' ? 'secondary' :
                                  payment.checkStatus === 'تحت التحصيل' ? 'default' :
                                  'outline'
                                }
                                className="mt-1 ml-2"
                              >
                                {payment.checkStatus}
                              </Badge>
                            )}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // TODO: Implement edit functionality
                              toast({
                                title: "قريباً",
                                description: "ميزة التعديل ستكون متاحة قريباً",
                              });
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // TODO: Implement delete functionality
                              toast({
                                title: "قريباً",
                                description: "ميزة الحذف ستكون متاحة قريباً",
                              });
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <Wallet className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium text-muted-foreground">لا توجد مدفوعات</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        لم يتم تسجيل أي مدفوعات خزينة حتى الآن
                      </p>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Payment Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPayment ? 'تعديل الإيصال' : 'إيصال جديد'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={currentForm.handleSubmit(onSubmit)} className="space-y-6">
            {/* Payment Method Selection */}
            <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
              <h3 className="font-medium text-blue-800">نوع الإيصال</h3>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  type="button"
                  variant={paymentMethod === 'نقدي' ? 'default' : 'outline'}
                  onClick={() => {
                    setPaymentMethod('نقدي');
                    // Generate cash receipt number
                    const timestamp = Date.now();
                    const receiptNumber = `CASH-${timestamp}`;
                    cashForm.setValue('receiptNumber', receiptNumber);
                  }}
                  className="h-16 flex flex-col gap-2"
                >
                  <Wallet className="h-6 w-6" />
                  <span>إيصال نقدي</span>
                </Button>
                <Button
                  type="button"
                  variant={paymentMethod === 'شيك' ? 'default' : 'outline'}
                  onClick={() => {
                    setPaymentMethod('شيك');
                    // Generate cheque receipt number
                    const timestamp = Date.now();
                    const receiptNumber = `CHK-${timestamp}`;
                    chequeForm.setValue('receiptNumber', receiptNumber);
                  }}
                  className="h-16 flex flex-col gap-2"
                >
                  <CreditCard className="h-6 w-6" />
                  <span>إيصال شيك</span>
                </Button>
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="receiptNumber">رقم الإيصال *</Label>
                <Input
                  id="receiptNumber"
                  {...currentForm.register('receiptNumber')}
                  placeholder={paymentMethod === 'نقدي' ? 'مثال: CASH-001' : 'مثال: CHK-001'}
                  className="receipt-number"
                  readOnly
                />
                {currentForm.formState.errors.receiptNumber && (
                  <p className="text-sm text-red-600 mt-1">
                    {currentForm.formState.errors.receiptNumber.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="paymentDate">تاريخ الاستلام *</Label>
                <Input
                  id="paymentDate"
                  type="date"
                  {...currentForm.register('paymentDate')}
                />
                {currentForm.formState.errors.paymentDate && (
                  <p className="text-sm text-red-600 mt-1">
                    {currentForm.formState.errors.paymentDate.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="amount">المبلغ المستلم *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  {...currentForm.register('amount', { valueAsNumber: true })}
                  placeholder="أدخل المبلغ"
                  className="amount-field"
                />
                {currentForm.formState.errors.amount && (
                  <p className="text-sm text-red-600 mt-1">
                    {currentForm.formState.errors.amount.message}
                  </p>
                )}
              </div>
            </div>

            {/* Cheque Details - Only show for cheque payments */}
            {paymentMethod === 'شيك' && (
              <div className="space-y-4 p-4 border rounded-lg bg-green-50">
                <h3 className="font-medium text-green-800">بيانات الشيك</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="checkNumber">رقم الشيك *</Label>
                    <Input
                      id="checkNumber"
                      {...chequeForm.register('checkNumber')}
                      placeholder="أدخل رقم الشيك"
                      className="receipt-number"
                    />
                    {chequeForm.formState.errors.checkNumber && (
                      <p className="text-sm text-red-600 mt-1">
                        {chequeForm.formState.errors.checkNumber.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="checkDate">تاريخ الشيك *</Label>
                    <Input
                      id="checkDate"
                      type="date"
                      {...chequeForm.register('checkDate')}
                    />
                    {chequeForm.formState.errors.checkDate && (
                      <p className="text-sm text-red-600 mt-1">
                        {chequeForm.formState.errors.checkDate.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="bankName">اسم البنك *</Label>
                    <Input
                      id="bankName"
                      {...chequeForm.register('bankName')}
                      placeholder="أدخل اسم البنك"
                    />
                    {chequeForm.formState.errors.bankName && (
                      <p className="text-sm text-red-600 mt-1">
                        {chequeForm.formState.errors.bankName.message}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="checkStatus">حالة الشيك</Label>
                  <Select
                    value={chequeForm.watch('checkStatus')}
                    onValueChange={(value) => chequeForm.setValue('checkStatus', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حالة الشيك" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="عهدة">عهدة</SelectItem>
                      <SelectItem value="تحت التحصيل">تحت التحصيل</SelectItem>
                      <SelectItem value="أمانة">أمانة</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Payment Type */}
            <div>
              <Label htmlFor="paymentType">نوع الإيصال *</Label>
              <Select
                value={currentForm.watch('paymentType')}
                onValueChange={(value) => currentForm.setValue('paymentType', value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع الإيصال" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="مرتبطة بعقد">مرتبطة بعقد</SelectItem>
                  <SelectItem value="غير مرتبطة بعقد">غير مرتبطة بعقد</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Contract-related fields */}
            {currentForm.watch('paymentType') === 'مرتبطة بعقد' && (
              <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
                <h3 className="font-medium text-blue-800">بيانات العقد</h3>

                {/* Contract Selection */}
                <div>
                  <Label htmlFor="contractSelect">اختيار العقد *</Label>
                  <Select
                    value={selectedContractId?.toString() || ''}
                    onValueChange={(value) => {
                      const contractId = parseInt(value);
                      setSelectedContractId(contractId);

                      // Find selected contract and populate fields
                      const selectedContract = contracts?.find(c => c.id === contractId);
                      if (selectedContract) {
                        currentForm.setValue('contractId', contractId);
                        currentForm.setValue('contractNumber', selectedContract.contractNumber);
                        currentForm.setValue('contractSubject', selectedContract.contractSubject);
                        currentForm.setValue('clientName', selectedContract.clientName);

                        // Fetch contract receivables
                        fetchContractReceivables(contractId);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر العقد" />
                    </SelectTrigger>
                    <SelectContent>
                      {contracts?.map((contract) => (
                        <SelectItem key={contract.id} value={contract.id.toString()}>
                          <div className="flex flex-col">
                            <span className="font-medium">{contract.contractNumber}</span>
                            <span className="text-sm text-muted-foreground">
                              {contract.clientName} - {contract.contractSubject}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Contract Receivables Selection Table */}
                {contractReceivables.length > 0 && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label>اختيار الاستحقاقات *</Label>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectAllReceivables(true)}
                          disabled={contractReceivables.filter(r => r.status !== 'مدفوع').length === 0}
                        >
                          اختيار الكل
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectAllReceivables(false)}
                          disabled={selectedReceivables.length === 0}
                        >
                          إلغاء الكل
                        </Button>
                      </div>
                    </div>

                    <div className="border rounded-lg overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow className="bg-gray-50">
                            <TableHead className="w-12 text-center">اختيار</TableHead>
                            <TableHead className="text-right">رقم القسط</TableHead>
                            <TableHead className="text-right">كود الفاتورة</TableHead>
                            <TableHead className="text-right">المبلغ</TableHead>
                            <TableHead className="text-right">المتبقي</TableHead>
                            <TableHead className="text-right">تاريخ الاستحقاق</TableHead>
                            <TableHead className="text-right">الحالة</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {contractReceivables.map((receivable) => {
                            const isSelected = selectedReceivables.includes(receivable.id);
                            const isPaid = receivable.status === 'مدفوع';
                            const remainingAmount = receivable.remainingAmount || receivable.amount;

                            return (
                              <TableRow
                                key={receivable.id}
                                className={`${isSelected ? 'bg-blue-50' : ''} ${isPaid ? 'opacity-50' : ''}`}
                              >
                                <TableCell className="text-center">
                                  <input
                                    type="checkbox"
                                    checked={isSelected}
                                    disabled={isPaid}
                                    onChange={(e) => handleReceivableSelection(receivable.id, e.target.checked)}
                                    className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                                  />
                                </TableCell>
                                <TableCell className="font-medium">
                                  قسط رقم {receivable.installmentNumber}
                                </TableCell>
                                <TableCell>
                                  <div className="text-xs text-blue-600 font-mono">
                                    {receivable.invoiceCode}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <span className="currency font-medium">
                                    {formatCurrency(receivable.amount)}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  <span className={`currency font-medium ${remainingAmount > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                                    {formatCurrency(remainingAmount)}
                                  </span>
                                </TableCell>
                                <TableCell>
                                  {formatDate(receivable.dueDate)}
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant={
                                      receivable.status === 'مدفوع' ? 'default' :
                                      receivable.status === 'متأخر' ? 'destructive' :
                                      receivable.status === 'مستحق' ? 'secondary' : 'outline'
                                    }
                                  >
                                    {receivable.status}
                                  </Badge>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Selected Receivables Summary */}
                    {selectedReceivables.length > 0 && (
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium text-green-800">
                              تم اختيار {selectedReceivables.length} استحقاق
                            </h4>
                            <p className="text-sm text-green-600">
                              إجمالي المبلغ المطلوب: <span className="currency font-bold">{formatCurrency(calculateSelectedTotal())}</span>
                            </p>
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Auto-fill payment amount with selected total
                              const total = calculateSelectedTotal();
                              currentForm.setValue('amount', total);
                            }}
                          >
                            ملء المبلغ تلقائياً
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                <div>
                  <Label htmlFor="paymentStatus">حالة السداد</Label>
                  <Select
                    value={currentForm.watch('paymentStatus') || ''}
                    onValueChange={(value) => currentForm.setValue('paymentStatus', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حالة السداد" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="كامل">سداد كامل</SelectItem>
                      <SelectItem value="جزئي">سداد جزئي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Non-contract fields */}
            {currentForm.watch('paymentType') === 'غير مرتبطة بعقد' && (
              <div className="space-y-4 p-4 border rounded-lg bg-orange-50">
                <h3 className="font-medium text-orange-800">نوع الإيصال</h3>

                <div>
                  <Label htmlFor="nonContractType">نوع الإيصال *</Label>
                  <Select
                    value={currentForm.watch('nonContractType') || ''}
                    onValueChange={(value) => currentForm.setValue('nonContractType', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الإيصال" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="رسوم تفاوض">رسوم تفاوض</SelectItem>
                      <SelectItem value="جدية عرض">جدية عرض</SelectItem>
                      <SelectItem value="تأمين ابتدائي">تأمين ابتدائي</SelectItem>
                      <SelectItem value="رسوم طلب">رسوم طلب</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {/* Notes */}
            <div>
              <Label htmlFor="notes">ملاحظات</Label>
              <Textarea
                id="notes"
                {...currentForm.register('notes')}
                placeholder="أدخل أي ملاحظات إضافية"
                rows={3}
              />
            </div>

            {/* Warning for contract payments without selected receivables */}
            {currentForm.watch('paymentType') === 'مرتبطة بعقد' &&
             contractReceivables.length > 0 &&
             selectedReceivables.length === 0 && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-800">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    يجب اختيار استحقاق واحد على الأقل لإتمام عملية الدفع
                  </span>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setDialogOpen(false)}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={
                  createCashReceiptMutation.isPending ||
                  createChequeReceiptMutation.isPending ||
                  (currentForm.watch('paymentType') === 'مرتبطة بعقد' &&
                   contractReceivables.length > 0 &&
                   selectedReceivables.length === 0)
                }
              >
                {editingPayment ? 'تحديث الإيصال' : 'تسجيل الإيصال'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
