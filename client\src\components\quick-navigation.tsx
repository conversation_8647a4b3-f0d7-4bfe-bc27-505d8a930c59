// Quick Navigation Component - نظام التنقل السريع المترابط
// يوفر روابط سريعة للتنقل بين العميل وعقوده واستحقاقاته ومدفوعاته

import React from 'react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  FileText, 
  DollarSign, 
  CreditCard, 
  Eye, 
  ArrowRight,
  Calendar,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { useLanguage } from "@/hooks/use-language";
import { useSettings } from "@/hooks/use-settings";
import { useDateFormat } from "@/hooks/use-date-format";
import { cn } from "@/lib/utils";

interface QuickNavigationProps {
  type: 'client' | 'contract' | 'receivable' | 'payment';
  data: any;
  relatedData?: {
    client?: any;
    contracts?: any[];
    receivables?: any[];
    payments?: any[];
  };
  onNavigate?: (path: string, data?: any) => void;
}

export const QuickNavigation: React.FC<QuickNavigationProps> = ({
  type,
  data,
  relatedData = {},
  onNavigate
}) => {
  const { isRTL } = useLanguage();
  const { formatCurrency } = useSettings();
  const { formatDate } = useDateFormat();

  const handleNavigate = (path: string, navData?: any) => {
    if (onNavigate) {
      onNavigate(path, navData);
    } else {
      // Default navigation using window.location
      window.location.href = path;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
      case 'مدفوع':
      case 'مكتمل':
        return 'bg-green-100 text-green-800';
      case 'متأخر':
      case 'منتهي':
        return 'bg-red-100 text-red-800';
      case 'مستحق':
      case 'معلق':
        return 'bg-yellow-100 text-yellow-800';
      case 'لم يحن موعده':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Client Navigation
  if (type === 'client') {
    const { contracts = [], receivables = [], payments = [] } = relatedData;
    const activeContracts = contracts.filter((c: any) => c.contractStatus === 'نشط');
    const overdueReceivables = receivables.filter((r: any) => r.status === 'متأخر');
    const totalContractValue = contracts.reduce((sum: number, c: any) => sum + (c.totalContractValue || 0), 0);

    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className={cn("flex items-center gap-2", isRTL ? "flex-row-reverse" : "")}>
            <User className="h-5 w-5 text-blue-600" />
            <span>تنقل سريع - {data.clientName}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Client Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-600">العقود النشطة</p>
              <p className="text-xl font-bold text-blue-600">{activeContracts.length}</p>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-gray-600">إجمالي القيمة</p>
              <p className="text-lg font-bold text-green-600">{formatCurrency(totalContractValue)}</p>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <p className="text-sm text-gray-600">الاستحقاقات</p>
              <p className="text-xl font-bold text-yellow-600">{receivables.length}</p>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <p className="text-sm text-gray-600">متأخرة</p>
              <p className="text-xl font-bold text-red-600">{overdueReceivables.length}</p>
            </div>
          </div>

          <Separator />

          {/* Quick Actions */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-700">إجراءات سريعة</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/contracts?clientId=${data.id}`)}
              >
                <FileText className="h-4 w-4" />
                عرض جميع العقود ({contracts.length})
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/receivables?clientId=${data.id}`)}
              >
                <DollarSign className="h-4 w-4" />
                عرض الاستحقاقات ({receivables.length})
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/payments?clientId=${data.id}`)}
              >
                <CreditCard className="h-4 w-4" />
                عرض المدفوعات ({payments.length})
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/clients/edit/${data.id}`)}
              >
                <Eye className="h-4 w-4" />
                تعديل بيانات العميل
              </Button>
            </div>
          </div>

          {/* Recent Contracts */}
          {contracts.length > 0 && (
            <>
              <Separator />
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700">العقود الحديثة</h4>
                {contracts.slice(0, 3).map((contract: any) => (
                  <div 
                    key={contract.id} 
                    className="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
                    onClick={() => handleNavigate(`/contracts/view/${contract.id}`)}
                  >
                    <div>
                      <p className="font-medium text-sm">{contract.contractNumber}</p>
                      <p className="text-xs text-gray-600">{contract.contractType}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(contract.contractStatus)}>
                        {contract.contractStatus}
                      </Badge>
                      <ArrowRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    );
  }

  // Contract Navigation
  if (type === 'contract') {
    const { client, receivables = [], payments = [] } = relatedData;
    const overdueReceivables = receivables.filter((r: any) => r.status === 'متأخر');
    const paidReceivables = receivables.filter((r: any) => r.status === 'مدفوع');

    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className={cn("flex items-center gap-2", isRTL ? "flex-row-reverse" : "")}>
            <FileText className="h-5 w-5 text-green-600" />
            <span>تنقل سريع - {data.contractNumber}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Contract Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-gray-600">قيمة العقد</p>
              <p className="text-lg font-bold text-green-600">{formatCurrency(data.totalContractValue)}</p>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-600">الاستحقاقات</p>
              <p className="text-xl font-bold text-blue-600">{receivables.length}</p>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-gray-600">مدفوع</p>
              <p className="text-xl font-bold text-green-600">{paidReceivables.length}</p>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <p className="text-sm text-gray-600">متأخر</p>
              <p className="text-xl font-bold text-red-600">{overdueReceivables.length}</p>
            </div>
          </div>

          <Separator />

          {/* Client Info */}
          {client && (
            <div 
              className="flex items-center justify-between p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100"
              onClick={() => handleNavigate(`/clients/view/${client.id}`)}
            >
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">{client.clientName}</p>
                  <p className="text-sm text-gray-600">{client.clientType}</p>
                </div>
              </div>
              <ArrowRight className="h-4 w-4 text-blue-600" />
            </div>
          )}

          {/* Quick Actions */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-700">إجراءات سريعة</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/receivables?contractId=${data.id}`)}
              >
                <DollarSign className="h-4 w-4" />
                استحقاقات العقد ({receivables.length})
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/payments?contractId=${data.id}`)}
              >
                <CreditCard className="h-4 w-4" />
                مدفوعات العقد ({payments.length})
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/contracts/edit/${data.id}`)}
              >
                <Eye className="h-4 w-4" />
                تعديل العقد
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="justify-start gap-2"
                onClick={() => handleNavigate(`/contracts/${data.id}/installments`)}
              >
                <Calendar className="h-4 w-4" />
                جدول الأقساط
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default fallback
  return (
    <Card className="w-full">
      <CardContent className="p-4">
        <p className="text-gray-500">نظام التنقل السريع غير متاح لهذا النوع</p>
      </CardContent>
    </Card>
  );
};

export default QuickNavigation;
