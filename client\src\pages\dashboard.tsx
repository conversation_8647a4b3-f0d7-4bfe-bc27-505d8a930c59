import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Users,
  FileSignature,
  DollarSign,
  AlertTriangle,
  TrendingUp,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckCircle,
  Clock,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Wallet,
  CreditCard,
  Building,
  Receipt,
  TrendingDown,
  Eye,
  MoreHorizontal
} from "lucide-react";
import { formatNumber } from "@/lib/formatters";
import { useCurrency } from "@/hooks/use-currency";
import { useLanguage } from "@/hooks/use-language";
import { useDateFormat } from "@/hooks/use-date-format";
import { Separator } from "@/components/ui/separator";
import labels from "@/lib/i18n";
import type { DashboardStats } from "@shared/schema";
import { useLocation } from "wouter";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";

export default function Dashboard() {
  const { language } = useLanguage();
  const t = labels[language];
  const { formatCurrency } = useCurrency();
  const { formatDate } = useDateFormat();
  const [, setLocation] = useLocation();

  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['/api/dashboard/stats'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/stats');
      if (!response.ok) throw new Error('Failed to fetch stats');
      return response.json();
    }
  });

  const { data: recentContracts, isLoading: contractsLoading } = useQuery({
    queryKey: ['/api/contracts'],
    queryFn: async () => {
      const response = await fetch('/api/contracts');
      if (!response.ok) throw new Error('Failed to fetch contracts');
      const data = await response.json();
      return data.slice(0, 5); // Get last 5 contracts
    }
  });

  const { data: overduePayments, isLoading: paymentsLoading } = useQuery({
    queryKey: ['/api/dashboard/overdue-payments'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/overdue-payments');
      if (!response.ok) throw new Error('Failed to fetch overdue payments');
      return response.json();
    }
  });

  // Additional queries for integrated data
  const { data: receivables, isLoading: receivablesLoading } = useQuery({
    queryKey: ['/api/receivables'],
    queryFn: async () => {
      const response = await fetch('/api/receivables');
      if (!response.ok) throw new Error('Failed to fetch receivables');
      const data = await response.json();
      return data?.receivables || [];
    }
  });

  const { data: clients, isLoading: clientsLoading } = useQuery({
    queryKey: ['/api/clients'],
    queryFn: async () => {
      const response = await fetch('/api/clients');
      if (!response.ok) throw new Error('Failed to fetch clients');
      const data = await response.json();
      return data?.slice(0, 5) || []; // Get recent clients
    }
  });

  const { data: expiringContracts, isLoading: expiringLoading } = useQuery({
    queryKey: ['/api/dashboard/expiring-contracts'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/expiring-contracts');
      if (!response.ok) throw new Error('Failed to fetch expiring contracts');
      return response.json();
    }
  });

  const { data: recentActivities, isLoading: activitiesLoading } = useQuery({
    queryKey: ['/api/dashboard/recent-activities'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/recent-activities');
      if (!response.ok) throw new Error('Failed to fetch recent activities');
      return response.json();
    }
  });

  // Fetch real chart data
  const { data: monthlyRevenueData, isLoading: revenueLoading } = useQuery({
    queryKey: ['/api/dashboard/monthly-revenue'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/monthly-revenue');
      if (!response.ok) throw new Error('Failed to fetch monthly revenue');
      return response.json();
    }
  });

  const { data: contractStatusData, isLoading: contractDistributionLoading } = useQuery({
    queryKey: ['/api/dashboard/contract-distribution'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/contract-distribution');
      if (!response.ok) throw new Error('Failed to fetch contract distribution');
      return response.json();
    }
  });

  const { data: paymentTrendsData, isLoading: paymentTrendsLoading } = useQuery({
    queryKey: ['/api/dashboard/payment-trends'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/payment-trends');
      if (!response.ok) throw new Error('Failed to fetch payment trends');
      return response.json();
    }
  });

  // Enhanced stats cards with modern design
  const statsCards = [
    {
      title: "إجمالي العملاء",
      value: stats?.totalClients || 0,
      icon: Users,
      gradient: "from-blue-500 to-blue-600",
      bgGradient: "from-blue-50 to-blue-100",
      description: "عدد العملاء المسجلين",
      change: "+12%",
      trend: "up"
    },
    {
      title: "العقود النشطة",
      value: stats?.activeContracts || 0,
      icon: FileSignature,
      gradient: "from-green-500 to-green-600",
      bgGradient: "from-green-50 to-green-100",
      description: "العقود قيد التنفيذ",
      change: "+8%",
      trend: "up"
    },
    {
      title: "المبالغ المستحقة",
      value: formatCurrency(stats?.outstandingAmount || 0),
      icon: DollarSign,
      gradient: "from-orange-500 to-orange-600",
      bgGradient: "from-orange-50 to-orange-100",
      description: "إجمالي المستحقات",
      change: "-5%",
      trend: "down"
    },
    {
      title: "المدفوعات المتأخرة",
      value: stats?.overduePayments || 0,
      icon: AlertTriangle,
      gradient: "from-red-500 to-red-600",
      bgGradient: "from-red-50 to-red-100",
      description: "تحتاج متابعة فورية",
      change: "+3%",
      trend: "up"
    },
  ];

  // Check if chart data is loading
  const isChartsLoading = revenueLoading || contractDistributionLoading || paymentTrendsLoading;

  // Navigation function for quick access icons
  const handleQuickNavigation = (path: string) => {
    setLocation(path);
  };

  // Dynamic alerts based on real data
  const alerts = [
    ...(stats?.overduePayments > 0 ? [{
      type: "error",
      title: "مدفوعات متأخرة",
      description: `${stats.overduePayments} استحقاق متأخر يحتاج متابعة`,
      icon: AlertTriangle,
      bgColor: "bg-red-50 dark:bg-red-900/20",
      borderColor: "border-red-200 dark:border-red-800",
      textColor: "text-red-800 dark:text-red-200"
    }] : []),
    ...(expiringContracts?.length > 0 ? [{
      type: "warning",
      title: "عقود تنتهي قريباً",
      description: `${expiringContracts.length} عقد ينتهي خلال 30 يوماً`,
      icon: Clock,
      bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
      borderColor: "border-yellow-200 dark:border-yellow-800",
      textColor: "text-yellow-800 dark:text-yellow-200"
    }] : []),
    ...(stats?.inactiveContracts > 0 ? [{
      type: "info",
      title: "عقود غير نشطة",
      description: `${stats.inactiveContracts} عقد في حالة غير نشطة`,
      icon: Calendar,
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      borderColor: "border-blue-200 dark:border-blue-800",
      textColor: "text-blue-800 dark:text-blue-200"
    }] : []),
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container mx-auto px-6 py-8 space-y-8">

        {/* Quick Access Icons Section */}
        <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-lg">
          <CardContent className="p-8">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
              {/* العملاء */}
              <div
                onClick={() => handleQuickNavigation('/clients')}
                className="flex flex-col items-center gap-4 p-6 rounded-xl hover:bg-blue-50 transition-colors cursor-pointer group"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <Users className="h-10 w-10 text-white" />
                </div>
                <span className="text-base font-medium text-slate-700 text-center">العملاء</span>
              </div>

              {/* العقود */}
              <div
                onClick={() => handleQuickNavigation('/contracts')}
                className="flex flex-col items-center gap-4 p-6 rounded-xl hover:bg-green-50 transition-colors cursor-pointer group"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <FileSignature className="h-10 w-10 text-white" />
                </div>
                <span className="text-base font-medium text-slate-700 text-center">العقود</span>
              </div>

              {/* الاستحقاقات */}
              <div
                onClick={() => handleQuickNavigation('/receivables')}
                className="flex flex-col items-center gap-4 p-6 rounded-xl hover:bg-yellow-50 transition-colors cursor-pointer group"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <DollarSign className="h-10 w-10 text-white" />
                </div>
                <span className="text-base font-medium text-slate-700 text-center">الاستحقاقات</span>
              </div>

              {/* المدفوعات */}
              <div
                onClick={() => handleQuickNavigation('/payments')}
                className="flex flex-col items-center gap-4 p-6 rounded-xl hover:bg-purple-50 transition-colors cursor-pointer group"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <Wallet className="h-10 w-10 text-white" />
                </div>
                <span className="text-base font-medium text-slate-700 text-center">المدفوعات</span>
              </div>

              {/* الشيكات */}
              <div
                onClick={() => handleQuickNavigation('/cheques')}
                className="flex flex-col items-center gap-4 p-6 rounded-xl hover:bg-indigo-50 transition-colors cursor-pointer group"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <CreditCard className="h-10 w-10 text-white" />
                </div>
                <span className="text-base font-medium text-slate-700 text-center">الشيكات</span>
              </div>

              {/* التقارير */}
              <div
                onClick={() => handleQuickNavigation('/reports')}
                className="flex flex-col items-center gap-4 p-6 rounded-xl hover:bg-orange-50 transition-colors cursor-pointer group"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform">
                  <BarChart3 className="h-10 w-10 text-white" />
                </div>
                <span className="text-base font-medium text-slate-700 text-center">التقارير</span>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className={`relative overflow-hidden bg-gradient-to-br ${stat.bgGradient} border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105`}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <div className={`w-10 h-10 bg-gradient-to-br ${stat.gradient} rounded-lg flex items-center justify-center shadow-md`}>
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <p className="text-sm font-medium text-slate-700">{stat.title}</p>
                      </div>
                      {statsLoading ? (
                        <Skeleton className="h-8 w-20" />
                      ) : (
                        <p className="text-3xl font-bold text-slate-800">{stat.value}</p>
                      )}
                      <div className="flex items-center gap-2">
                        {stat.trend === "up" && (
                          <div className="flex items-center gap-1 px-2 py-1 bg-green-100 rounded-full">
                            <ArrowUpIcon className="h-3 w-3 text-green-600" />
                            <span className="text-xs font-medium text-green-600">{stat.change}</span>
                          </div>
                        )}
                        {stat.trend === "down" && (
                          <div className="flex items-center gap-1 px-2 py-1 bg-red-100 rounded-full">
                            <ArrowDownIcon className="h-3 w-3 text-red-600" />
                            <span className="text-xs font-medium text-red-600">{stat.change}</span>
                          </div>
                        )}
                        <span className="text-xs text-slate-600">{stat.description}</span>
                      </div>
                    </div>
                    <div className="absolute top-4 left-4 opacity-10">
                      <Icon className="h-16 w-16" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Monthly Revenue Chart */}
          <Card className="lg:col-span-2 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-slate-800">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  الإيرادات الشهرية
                </CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="text-xs">6 أشهر</Button>
                  <Button variant="ghost" size="sm" className="text-xs">السنة</Button>
                </div>
              </div>
              <p className="text-sm text-slate-600">تطور الإيرادات والعقود خلال الأشهر الماضية</p>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                {revenueLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Skeleton className="h-8 w-32 mx-auto mb-4" />
                      <Skeleton className="h-4 w-48 mx-auto" />
                    </div>
                  </div>
                ) : monthlyRevenueData && monthlyRevenueData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={monthlyRevenueData}>
                      <defs>
                        <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
                      <XAxis
                        dataKey="month"
                        stroke="#64748B"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        stroke="#64748B"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${value / 1000}ك`}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                        formatter={(value: any) => [formatCurrency(value), 'الإيرادات']}
                      />
                      <Area
                        type="monotone"
                        dataKey="revenue"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        fill="url(#revenueGradient)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-slate-500">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>لا توجد بيانات إيرادات متاحة</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Contract Status Pie Chart */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <PieChart className="h-5 w-5 text-green-600" />
                توزيع العقود
              </CardTitle>
              <p className="text-sm text-slate-600">حالة العقود الحالية</p>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {contractDistributionLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Skeleton className="h-16 w-16 rounded-full mx-auto mb-4" />
                      <Skeleton className="h-4 w-32 mx-auto" />
                    </div>
                  </div>
                ) : contractStatusData && contractStatusData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={contractStatusData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {contractStatusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-slate-500">
                    <div className="text-center">
                      <PieChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>لا توجد بيانات عقود متاحة</p>
                    </div>
                  </div>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2 mt-4">
                {contractDistributionLoading ? (
                  Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-2">
                      <Skeleton className="w-3 h-3 rounded-full" />
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-8" />
                    </div>
                  ))
                ) : contractStatusData && contractStatusData.length > 0 ? (
                  contractStatusData.map((item, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-xs text-slate-600">{item.name}</span>
                      <span className="text-xs font-medium text-slate-800">{item.value}</span>
                    </div>
                  ))
                ) : null}
              </div>
            </CardContent>
          </Card>
        </div>

      {/* Integrated System Overview */}
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-slate-800">
            <Activity className="h-5 w-5 text-blue-600" />
            نظرة شاملة على ترابط النظام
          </CardTitle>
          <p className="text-sm text-slate-600">
            عرض متكامل للعلاقات بين العملاء والعقود والاستحقاقات والمدفوعات
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Clients Overview */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <h4 className="font-medium">العملاء</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>إجمالي العملاء</span>
                  <span className="font-medium">{stats?.totalClients || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>عملاء نشطون</span>
                  <span className="font-medium text-green-600">
                    {stats?.activeClients || 0}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>عملاء جدد هذا الشهر</span>
                  <span className="font-medium text-blue-600">
                    {stats?.newClientsThisMonth || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Contracts Overview */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <FileSignature className="h-5 w-5 text-green-600" />
                <h4 className="font-medium">العقود</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>عقود نشطة</span>
                  <span className="font-medium text-green-600">
                    {stats?.activeContracts || 0}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>إجمالي القيمة</span>
                  <span className="font-medium">
                    {formatCurrency(stats?.totalContractValue || 0)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>عقود تنتهي قريباً</span>
                  <span className="font-medium text-yellow-600">
                    {stats?.contractsExpiringSoon || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Receivables Overview */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-yellow-600" />
                <h4 className="font-medium">الاستحقاقات</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>مستحقة اليوم</span>
                  <span className="font-medium text-yellow-600">
                    {receivables?.filter(r => {
                      const today = new Date().toISOString().split('T')[0];
                      return r.dueDate === today;
                    }).length || 0}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>متأخرة</span>
                  <span className="font-medium text-red-600">
                    {receivables?.filter(r => r.status === 'متأخر').length || 0}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>مدفوعة</span>
                  <span className="font-medium text-green-600">
                    {receivables?.filter(r => r.status === 'مدفوع').length || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Payments Overview */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <h4 className="font-medium">المدفوعات</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>مبالغ محصلة</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(stats?.collectedAmount || 0)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>مبالغ مستحقة</span>
                  <span className="font-medium text-yellow-600">
                    {formatCurrency(stats?.outstandingAmount || 0)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>مبالغ مستقبلية</span>
                  <span className="font-medium text-blue-600">
                    {formatCurrency(stats?.pendingAmount || 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>


        </CardContent>
      </Card>

      {/* Recent Activity - Integrated View */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Clients with their Contracts */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <Users className="h-5 w-5 text-blue-600" />
                العملاء الجدد وعقودهم
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-600 hover:bg-blue-50"
                onClick={() => handleQuickNavigation('/clients')}
              >
                عرض الكل
              </Button>
            </div>
            <p className="text-sm text-slate-600">آخر العملاء المضافين مع عقودهم</p>
          </CardHeader>
          <CardContent className="space-y-3">
            {clientsLoading ? (
              Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3">
                  <div className="flex items-center gap-3">
                    <Skeleton className="w-10 h-10 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              ))
            ) : clients && clients.length > 0 ? (
              clients.map((client: any) => {
                const clientContracts = recentContracts?.filter((c: any) => c.clientId === client.id) || [];
                return (
                  <div
                    key={client.id}
                    className="flex items-center justify-between p-3 hover:bg-muted/50 rounded-lg transition-colors cursor-pointer"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{client.clientName}</p>
                        <p className="text-xs text-muted-foreground">
                          {client.clientType} • {clientContracts.length} عقد
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={clientContracts.length > 0 ? "default" : "secondary"}>
                        {clientContracts.length > 0 ? "له عقود" : "جديد"}
                      </Badge>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-center text-muted-foreground py-8">لا توجد عملاء جدد</p>
            )}
          </CardContent>
        </Card>

        {/* Recent Receivables with Status */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <DollarSign className="h-5 w-5 text-yellow-600" />
                الاستحقاقات الحديثة
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="text-yellow-600 hover:bg-yellow-50"
                onClick={() => handleQuickNavigation('/receivables')}
              >
                عرض الكل
              </Button>
            </div>
            <p className="text-sm text-slate-600">آخر الاستحقاقات وحالتها</p>
          </CardHeader>
          <CardContent className="space-y-3">
            {receivablesLoading ? (
              Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3">
                  <div className="flex items-center gap-3">
                    <Skeleton className="w-8 h-8 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))
            ) : receivables && receivables.length > 0 ? (
              receivables.slice(0, 5).map((receivable: any) => {
                const statusColor = receivable.status === 'مدفوع' ? 'text-green-600' :
                                  receivable.status === 'متأخر' ? 'text-red-600' :
                                  receivable.status === 'مستحق' ? 'text-yellow-600' :
                                  'text-blue-600';
                const bgColor = receivable.status === 'مدفوع' ? 'bg-green-100' :
                               receivable.status === 'متأخر' ? 'bg-red-100' :
                               receivable.status === 'مستحق' ? 'bg-yellow-100' :
                               'bg-blue-100';

                return (
                  <div
                    key={receivable.id}
                    className="flex items-center justify-between p-3 hover:bg-muted/50 rounded-lg transition-colors cursor-pointer"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 ${bgColor} rounded-full flex items-center justify-center`}>
                        <DollarSign className={`h-4 w-4 ${statusColor}`} />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{receivable.contractNumber}</p>
                        <p className="text-xs text-muted-foreground">
                          {receivable.clientName} • {formatDate(receivable.dueDate)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {formatCurrency(receivable.amount)}
                      </p>
                      <Badge variant="outline" className={statusColor}>
                        {receivable.status}
                      </Badge>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-center text-muted-foreground py-8">لا توجد استحقاقات</p>
            )}
          </CardContent>
        </Card>
      </div>

        {/* Payment Trends Chart */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <BarChart3 className="h-5 w-5 text-orange-600" />
                اتجاهات المدفوعات
              </CardTitle>
              <p className="text-sm text-slate-600">مقارنة المبالغ المحصلة والمستحقة</p>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {paymentTrendsLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Skeleton className="h-8 w-32 mx-auto mb-4" />
                      <Skeleton className="h-4 w-48 mx-auto" />
                    </div>
                  </div>
                ) : paymentTrendsData && paymentTrendsData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={paymentTrendsData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
                      <XAxis
                        dataKey="month"
                        stroke="#64748B"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        stroke="#64748B"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${value / 1000}ك`}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                        formatter={(value: any, name: string) => [
                          formatCurrency(value),
                          name === 'collected' ? 'محصل' : 'مستحق'
                        ]}
                      />
                      <Bar dataKey="collected" fill="#10B981" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="outstanding" fill="#F59E0B" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-slate-500">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>لا توجد بيانات مدفوعات متاحة</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Alerts Panel */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                التنبيهات والإشعارات
              </CardTitle>
              <p className="text-sm text-slate-600">تنبيهات مهمة تحتاج متابعة</p>
            </CardHeader>
            <CardContent className="space-y-3">
              {alerts.map((alert, index) => {
                const Icon = alert.icon;
                return (
                  <div
                    key={index}
                    className="p-4 bg-gradient-to-r from-white to-slate-50 border border-slate-200 rounded-xl shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start gap-3">
                      <div className={`w-8 h-8 ${alert.bgColor} rounded-lg flex items-center justify-center`}>
                        <Icon className={`h-4 w-4 ${alert.textColor}`} />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-slate-800">
                          {alert.title}
                        </p>
                        <p className="text-xs text-slate-600 mt-1">
                          {alert.description}
                        </p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
              <Button variant="outline" className="w-full mt-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-700 hover:bg-blue-100">
                عرض جميع التنبيهات
              </Button>
            </CardContent>
          </Card>
        </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Contracts */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <FileSignature className="h-5 w-5 text-green-600" />
                العقود الحديثة
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="text-green-600 hover:bg-green-50"
                onClick={() => handleQuickNavigation('/contracts')}
              >
                عرض الكل
              </Button>
            </div>
            <p className="text-sm text-slate-600">آخر العقود المضافة</p>
          </CardHeader>
          <CardContent className="space-y-3">
            {contractsLoading ? (
              Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3">
                  <div className="flex items-center gap-3">
                    <Skeleton className="w-8 h-8 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))
            ) : recentContracts && recentContracts.length > 0 ? (
              recentContracts.map((contract: any) => (
                <div
                  key={contract.id}
                  className="flex items-center justify-between p-3 hover:bg-muted/50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                      <FileSignature className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">{contract.contractNumber}</p>
                      <p className="text-xs text-muted-foreground">{contract.clientName}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {formatCurrency(contract.totalContractValue)}
                    </p>
                    <p className="text-xs text-muted-foreground">اليوم</p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-muted-foreground py-8">{t.noData}</p>
            )}
          </CardContent>
        </Card>

        {/* Recent Payments */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-slate-800">
                <CheckCircle className="h-5 w-5 text-red-600" />
                المدفوعات المتأخرة
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="text-red-600 hover:bg-red-50"
                onClick={() => handleQuickNavigation('/payments')}
              >
                عرض الكل
              </Button>
            </div>
            <p className="text-sm text-slate-600">المدفوعات التي تحتاج متابعة</p>
          </CardHeader>
          <CardContent className="space-y-3">
            {paymentsLoading ? (
              Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3">
                  <div className="flex items-center gap-3">
                    <Skeleton className="w-8 h-8 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))
            ) : overduePayments && overduePayments.length > 0 ? (
              overduePayments.slice(0, 5).map((payment: any) => (
                <div
                  key={payment.id}
                  className="flex items-center justify-between p-3 hover:bg-muted/50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                      <Clock className="h-4 w-4 text-red-600 dark:text-red-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">قسط متأخر</p>
                      <p className="text-xs text-muted-foreground">
                        استحقاق: {payment.dueDate}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-red-600">
                      {formatCurrency(payment.totalAmount)}
                    </p>
                    <p className="text-xs text-muted-foreground">متأخر</p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-muted-foreground py-8">لا توجد مدفوعات متأخرة</p>
            )}
          </CardContent>
        </Card>
      </div>
      </div>
    </div>
  );
}
