// ===== CENTRALIZED ERROR HANDLING MIDDLEWARE =====
// Author: Augment Code
// Description: Centralized middleware for handling all types of errors and formatting them as JSON

// Load environment variables
require('dotenv').config();

const { AppError } = require('../errors/CustomErrors.cjs');
const { errorLogger } = require('../errors/ErrorLogger.cjs');
const ErrorMessages = require('../errors/ErrorMessages.cjs');
const ErrorHelpers = require('../errors/ErrorHelpers.cjs');

/**
 * Error Handler Middleware
 * This middleware should be the last middleware in the Express app
 */
class ErrorHandler {
  
  /**
   * Main error handling middleware
   */
  static handle(err, req, res, next) {
    // Convert unknown errors to AppError
    const error = ErrorHelpers.createFromUnknown(err);

    // Log the error with full context
    errorLogger.logRequestError(error, req, res);

    // Determine if we should include stack trace
    const includeStack = process.env.NODE_ENV === 'development' || process.env.INCLUDE_STACK_TRACE === 'true';

    // Get enhanced error message based on error type
    let enhancedMessage = error.message;
    let guidance = null;
    let actionSuggestion = null;

    // Map common error codes to enhanced messages
    if (error.errorCode) {
      const errorMapping = this.getEnhancedErrorMapping(error.errorCode, error.details);
      if (errorMapping) {
        enhancedMessage = errorMapping.message;
        guidance = errorMapping.guidance;
        actionSuggestion = errorMapping.action;
      }
    }

    // Prepare enhanced error response
    const errorResponse = {
      success: false,
      error: {
        message: enhancedMessage,
        guidance: guidance,
        action: actionSuggestion,
        statusCode: error.statusCode,
        errorCode: error.errorCode,
        severity: this.mapStatusCodeToSeverity(error.statusCode),
        timestamp: error.timestamp || new Date().toISOString(),
        retryable: this.isRetryableError(error.errorCode),
        ...(error.details && { details: error.details }),
        ...(includeStack && { stack: error.stack }),
        ...(process.env.NODE_ENV === 'development' && {
          requestId: req.id || req.headers['x-request-id'] || 'unknown',
          path: req.path,
          method: req.method
        })
      }
    };

    // Set appropriate status code
    const statusCode = error.statusCode || 500;

    // Add specific headers based on error type
    if (error.errorCode === 'RATE_LIMIT_EXCEEDED' && error.details?.retryAfter) {
      res.set('Retry-After', error.details.retryAfter);
    }

    if (error.errorCode === 'UNAUTHORIZED') {
      res.set('WWW-Authenticate', 'Bearer');
    }

    // Send error response
    res.status(statusCode).json(errorResponse);
  }

  /**
   * Get enhanced error mapping for common error codes
   */
  static getEnhancedErrorMapping(errorCode, details = {}) {
    const errorMappings = {
      'VALIDATION_ERROR': {
        message: 'بيانات غير صحيحة',
        guidance: 'يرجى التحقق من البيانات المدخلة وإصلاح الأخطاء المشار إليها',
        action: 'review_form'
      },
      'DUPLICATE_ENTRY': {
        message: 'العميل مسجل من قبل',
        guidance: 'رقم العميل هذا موجود في النظام مسبقاً. يمكنك البحث عن العميل الموجود أو استخدام رقم عميل مختلف',
        action: 'search_existing'
      },
      'CLIENT_ID_EXISTS': {
        message: 'العميل مسجل من قبل',
        guidance: 'رقم العميل هذا موجود في النظام مسبقاً. يمكنك البحث عن العميل الموجود أو استخدام رقم عميل مختلف',
        action: 'search_client'
      },
      'FOREIGN_KEY_CONSTRAINT': {
        message: 'مرجع البيانات غير صحيح',
        guidance: 'البيانات المرجعية المطلوبة غير موجودة. يرجى التحقق من صحة البيانات المرتبطة',
        action: 'check_references'
      },
      'REQUIRED_FIELD_MISSING': {
        message: 'حقول مطلوبة مفقودة',
        guidance: 'يوجد حقول مطلوبة لم يتم ملؤها. يرجى إكمال جميع الحقول المطلوبة',
        action: 'complete_form'
      },
      'DATABASE_BUSY': {
        message: 'قاعدة البيانات مشغولة',
        guidance: 'النظام مشغول حالياً. يرجى المحاولة مرة أخرى بعد قليل',
        action: 'retry_later'
      },
      'DATABASE_LOCKED': {
        message: 'قاعدة البيانات مقفلة',
        guidance: 'قاعدة البيانات مقفلة مؤقتاً. يرجى المحاولة مرة أخرى',
        action: 'retry_now'
      },
      'INVALID_JSON': {
        message: 'تنسيق البيانات غير صحيح',
        guidance: 'البيانات المرسلة بتنسيق غير صحيح. يرجى المحاولة مرة أخرى',
        action: 'retry_request'
      },
      'REQUEST_TIMEOUT': {
        message: 'انتهت مهلة العملية',
        guidance: 'استغرقت العملية وقتاً أطول من المتوقع. يرجى المحاولة مرة أخرى',
        action: 'retry_now'
      },
      'RATE_LIMIT_EXCEEDED': {
        message: 'تم تجاوز حد الطلبات',
        guidance: 'تم إرسال طلبات كثيرة في وقت قصير. يرجى الانتظار قليلاً قبل المحاولة مرة أخرى',
        action: 'wait_retry'
      }
    };

    return errorMappings[errorCode] || null;
  }

  /**
   * Map status code to severity level
   */
  static mapStatusCodeToSeverity(statusCode) {
    if (statusCode >= 500) return 'critical';
    if (statusCode >= 400) return 'error';
    if (statusCode >= 300) return 'warning';
    return 'info';
  }

  /**
   * Check if error is retryable
   */
  static isRetryableError(errorCode) {
    const retryableErrors = [
      'DATABASE_BUSY',
      'DATABASE_LOCKED',
      'REQUEST_TIMEOUT',
      'NETWORK_ERROR',
      'RATE_LIMIT_EXCEEDED'
    ];
    return retryableErrors.includes(errorCode);
  }

  /**
   * Handle 404 Not Found errors
   */
  static handleNotFound(req, res, next) {
    const error = new AppError(
      `المسار ${req.originalUrl} غير موجود`,
      404,
      'ROUTE_NOT_FOUND',
      {
        path: req.originalUrl,
        method: req.method
      }
    );

    next(error);
  }

  /**
   * Handle async errors wrapper
   */
  static asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Validation error handler for express-validator
   */
  static handleValidationErrors(req, res, next) {
    const { validationResult } = require('express-validator');
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      const validationError = new AppError(
        'فشل في التحقق من صحة البيانات',
        400,
        'VALIDATION_ERROR',
        {
          errors: errors.array(),
          fields: errors.array().map(err => err.param)
        }
      );

      return next(validationError);
    }

    next();
  }

  /**
   * Handle multer file upload errors
   */
  static handleMulterErrors(err, req, res, next) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      const error = new AppError(
        'حجم الملف كبير جداً',
        400,
        'FILE_TOO_LARGE',
        {
          maxSize: err.limit,
          field: err.field
        }
      );
      return next(error);
    }

    if (err.code === 'LIMIT_FILE_COUNT') {
      const error = new AppError(
        'عدد الملفات كبير جداً',
        400,
        'TOO_MANY_FILES',
        {
          maxCount: err.limit,
          field: err.field
        }
      );
      return next(error);
    }

    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      const error = new AppError(
        'نوع الملف غير مدعوم',
        400,
        'UNSUPPORTED_FILE_TYPE',
        {
          field: err.field
        }
      );
      return next(error);
    }

    // Pass other multer errors to general error handler
    next(err);
  }

  /**
   * Handle JSON parsing errors
   */
  static handleJSONErrors(err, req, res, next) {
    if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
      const error = new AppError(
        'تنسيق JSON غير صحيح',
        400,
        'INVALID_JSON',
        {
          position: err.body,
          message: err.message
        }
      );
      return next(error);
    }

    next(err);
  }

  /**
   * Handle database connection errors
   */
  static handleDatabaseErrors(err, req, res, next) {
    // SQLite specific errors
    if (err.code && err.code.startsWith('SQLITE_')) {
      let message = 'خطأ في قاعدة البيانات';
      let errorCode = 'DATABASE_ERROR';

      switch (err.code) {
        case 'SQLITE_CONSTRAINT':
        case 'SQLITE_CONSTRAINT_UNIQUE':
          message = 'البيانات موجودة مسبقاً';
          errorCode = 'DUPLICATE_ENTRY';
          break;
        case 'SQLITE_CONSTRAINT_FOREIGNKEY':
          message = 'مرجع البيانات غير صحيح';
          errorCode = 'FOREIGN_KEY_CONSTRAINT';
          break;
        case 'SQLITE_CONSTRAINT_NOTNULL':
          message = 'حقل مطلوب مفقود';
          errorCode = 'REQUIRED_FIELD_MISSING';
          break;
        case 'SQLITE_BUSY':
          message = 'قاعدة البيانات مشغولة، يرجى المحاولة مرة أخرى';
          errorCode = 'DATABASE_BUSY';
          break;
        case 'SQLITE_LOCKED':
          message = 'قاعدة البيانات مقفلة';
          errorCode = 'DATABASE_LOCKED';
          break;
      }

      const error = new AppError(
        message,
        500,
        errorCode,
        {
          originalCode: err.code,
          originalMessage: err.message,
          errno: err.errno
        }
      );

      return next(error);
    }

    next(err);
  }

  /**
   * Handle timeout errors
   */
  static handleTimeoutErrors(err, req, res, next) {
    if (err.code === 'ETIMEDOUT' || err.timeout) {
      const error = new AppError(
        'انتهت مهلة العملية',
        408,
        'REQUEST_TIMEOUT',
        {
          timeout: err.timeout,
          operation: req.path
        }
      );
      return next(error);
    }

    next(err);
  }

  /**
   * Create comprehensive error handling middleware stack
   */
  static createMiddlewareStack() {
    return [
      // Handle JSON parsing errors
      this.handleJSONErrors,
      
      // Handle multer file upload errors
      this.handleMulterErrors,
      
      // Handle database errors
      this.handleDatabaseErrors,
      
      // Handle timeout errors
      this.handleTimeoutErrors,
      
      // Main error handler (must be last)
      this.handle
    ];
  }

  /**
   * Log unhandled promise rejections
   */
  static setupUnhandledRejectionHandler() {
    process.on('unhandledRejection', (reason, promise) => {
      console.error('🚨 Unhandled Promise Rejection:', reason);
      errorLogger.logError(
        ErrorHelpers.createFromUnknown(reason, 'Unhandled Promise Rejection'),
        { promise: promise.toString() }
      );

      // In production, you might want to gracefully shutdown
      if (process.env.NODE_ENV === 'production') {
        console.log('🛑 Shutting down due to unhandled promise rejection...');
        process.exit(1);
      }
    });
  }

  /**
   * Log uncaught exceptions
   */
  static setupUncaughtExceptionHandler() {
    process.on('uncaughtException', (error) => {
      console.error('🚨 Uncaught Exception:', error);
      errorLogger.logError(
        ErrorHelpers.createFromUnknown(error, 'Uncaught Exception'),
        { type: 'uncaughtException' }
      );

      // Always exit on uncaught exceptions
      console.log('🛑 Shutting down due to uncaught exception...');
      process.exit(1);
    });
  }

  /**
   * Setup all error handlers
   */
  static setup() {
    this.setupUnhandledRejectionHandler();
    this.setupUncaughtExceptionHandler();
    
    console.log('✅ Error handlers setup completed');
  }
}

module.exports = ErrorHandler;
