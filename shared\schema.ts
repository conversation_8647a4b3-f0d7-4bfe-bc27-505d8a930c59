import { z } from "zod";

// Reference Data Schema
export const insertReferenceDataSchema = z.object({
  id: z.number().optional(),
  module: z.string().optional(),
  listName: z.string().min(1, "List name is required"),
  itemValue: z.string().min(1, "Item value is required"),
  itemLabel: z.string().min(1, "Item label is required"),
  sortOrder: z.number().default(0),
  isActive: z.boolean().default(true),
  isDeleted: z.boolean().default(false),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

export type InsertReferenceData = z.infer<typeof insertReferenceDataSchema>;
export type ReferenceData = InsertReferenceData & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Reference List Schema (from client schema)
export const insertReferenceListSchema = z.object({
  module: z.string().min(1, "Module is required"),
  listName: z.string().min(1, "List name is required"),
  items: z.array(z.string().min(1, "Item cannot be empty")).min(1, "At least one item is required"),
});

export type InsertReferenceList = z.infer<typeof insertReferenceListSchema>;

// Reference Data Item Schema (from client schema)
export const referenceDataItemSchema = z.object({
  id: z.number(),
  module: z.string(),
  listName: z.string(),
  itemValue: z.string(),
  itemLabel: z.string(),
  sortOrder: z.number(),
  isActive: z.boolean(),
  isDeleted: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type ReferenceDataItem = z.infer<typeof referenceDataItemSchema>;

// Reference List Summary Schema (from client schema)
export const referenceListSummarySchema = z.object({
  module: z.string(),
  listName: z.string(),
  itemCount: z.number(),
});

export type ReferenceListSummary = z.infer<typeof referenceListSummarySchema>;

// Client Schema (simplified)
export const insertClientSchema = z.object({
  clientId: z.string().min(1, "Client ID is required"),
  clientType: z.string().min(1, "Client type is required"),
  clientName: z.string().min(1, "Client name is required"),
  clientPhoneWhatsapp: z.string().min(1, "Phone/WhatsApp is required"),
  isActive: z.boolean().default(true),
  clientAddress: z.string().optional(),
  clientPhone2: z.string().optional(),
  clientPhone3: z.string().optional(),
  clientEmail: z.string().optional(),
  clientNotes: z.string().optional(),
  clientFinancial_Category: z.string().optional(),
  clientLegal_Rep: z.string().optional(),
  clientReg_Number: z.string().optional(),
  clientTaxReg_Number: z.string().optional(),
  clientLegal_Status: z.string().optional(),
  clientRemarks: z.string().optional(),
  clientID_Image: z.string().optional(),
  clientDocuments: z.string().optional(),
});

export type InsertClient = z.infer<typeof insertClientSchema>;
export type Client = InsertClient & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Enhanced Contract Product Schema
export const contractProductSchema = z.object({
  id: z.number().optional(),
  productLabel: z.string().min(1, "Product label is required"),
  area: z.number().min(0, "Area must be positive"),
  meterPrice: z.number().min(0, "Meter price must be positive"),
  activationDate: z.string().optional(),
  endDate: z.string().optional(),
  billingType: z.enum(['شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي', 'غير منتظم']).default('شهري'),
  irregularBillingMonths: z.number().optional(),
  taxInfo: z.boolean().default(false),
  taxRate: z.number().min(0).max(100).default(0),
  financialAccountingStartDate: z.string().optional(),
  financialAccountingEndDate: z.string().optional(),
  accountingDuration: z.number().min(0).default(0),
  hasAnnualIncrease: z.boolean().default(false),
  increaseStartYear: z.number().min(1).default(2),
  increaseType: z.enum(['مبلغ ثابت', 'نسبة مئوية', 'نسبة مركبة']).default('نسبة مئوية'),
  increaseValue: z.number().min(0).default(0),
});

export type ContractProduct = z.infer<typeof contractProductSchema>;

// Contract Partner Schema
export const contractPartnerSchema = z.object({
  id: z.number().optional(),
  contractId: z.number().optional(),
  partnerId: z.number().min(1, "Partner ID is required"),
  partnerType: z.enum(['شريك', 'تحالف']).default('شريك'),
  partnershipPercentage: z.number().min(0).max(100).default(0),
  isActive: z.boolean().default(true),
});

export type ContractPartner = z.infer<typeof contractPartnerSchema>;

// Contract Attachment Schema
export const contractAttachmentSchema = z.object({
  id: z.number().optional(),
  contractId: z.number().optional(),
  fileName: z.string().min(1, "File name is required"),
  fileType: z.string().min(1, "File type is required"),
  fileSize: z.number().min(0, "File size must be positive"),
  filePath: z.string().min(1, "File path is required"),
  uploadedBy: z.string().optional(),
  uploadedAt: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type ContractAttachment = z.infer<typeof contractAttachmentSchema>;

// Revenue Recognition Schema
export const revenueRecognitionSchema = z.object({
  id: z.number().optional(),
  contractId: z.number().min(1, "Contract ID is required"),
  installmentId: z.number().min(1, "Installment ID is required"),
  fiscalYear: z.number().min(2020, "Fiscal year must be valid"),
  fiscalMonth: z.number().min(1).max(12, "Fiscal month must be between 1-12"),
  recognizedAmount: z.number().min(0, "Recognized amount must be positive"),
  cumulativeAmount: z.number().min(0, "Cumulative amount must be positive"),
  recognitionDate: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type RevenueRecognition = z.infer<typeof revenueRecognitionSchema>;

// Enhanced Contract Financial Terms Schema
export const contractFinancialTermsSchema = z.object({
  totalContractValue: z.number().min(0, "Total value must be positive"),
  monthlyAmount: z.number().min(0, "Monthly amount must be positive"),
  paymentDay: z.number().min(1).max(31).default(1),
  paymentFrequency: z.enum(['شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي', 'غير منتظم']).default('شهري'),
  irregularPaymentMonths: z.number().optional(),
  firstInstallmentDate: z.string().optional(),

  // Annual Increases
  annualIncreaseType: z.enum(['لا يوجد', 'مبلغ ثابت', 'نسبة مئوية', 'نسبة مركبة']).default('لا يوجد'),
  annualIncreaseValue: z.number().min(0).default(0),
  annualIncreaseStartYear: z.number().min(1).default(2),

  // Final Insurance
  finalInsuranceRate: z.number().min(0).max(100).default(0),

  // Penalties
  lateFeeType: z.enum(['مبلغ ثابت', 'نسبة مئوية']).default('نسبة مئوية'),
  lateFeeValue: z.number().min(0).default(0),
  gracePeriodDays: z.number().min(0).default(0),
  bouncedCheckFeeType: z.enum(['مبلغ ثابت', 'نسبة مئوية']).default('مبلغ ثابت'),
  bouncedCheckFeeValue: z.number().min(0).default(0),
});

export type ContractFinancialTerms = z.infer<typeof contractFinancialTermsSchema>;

// Enhanced Contract Schema
export const insertContractSchema = z.object({
  contractNumber: z.string().min(1, "Contract number is required"),
  contractInternalId: z.string().optional(),
  contractDescription: z.string().optional(),
  contractSubject: z.string().min(1, "Contract subject is required"),
  clientId: z.number().min(1, "Client is required"),
  contractType: z.string().min(1, "Contract type is required"),
  contractStatus: z.enum(['نشط', 'غير نشط', 'منتهي', 'ملغي', 'معلق']).default('نشط'),
  contractDate: z.string().min(1, "تاريخ البداية بالعقد مطلوب"),
  contractSigningDate: z.string().min(1, "Signing date is required"),
  financialActivationDate: z.string().optional(),
  startDate: z.string().min(1, "Start date is required"),
  actualStartDate: z.string().optional(),
  endDate: z.string().optional(),
  actualEndDate: z.string().optional(),
  contractDurationYears: z.number().min(0, "Duration must be positive"),
  assetOwner: z.string().optional(),
  ownershipPercentage: z.number().min(0).max(100).default(100),
  responsibleDepartment: z.string().min(1, "الإدارة المسؤولة مطلوبة"),
  region: z.string().min(1, "المنطقة محل العقد مطلوبة"),
  financialGuarantorId: z.number().optional(),
  parentContractId: z.number().optional(),
  numberOfProducts: z.number().min(1).default(1),
  hasUnifiedActivationDate: z.boolean().default(true),

  // Financial Terms
  totalContractValue: z.number().min(0, "Total value must be positive"),
  monthlyAmount: z.number().min(0, "Monthly amount must be positive"),
  paymentDay: z.number().min(1).max(31).default(1),
  paymentFrequency: z.enum(['شهري', 'ربع سنوي', 'نصف سنوي', 'سنوي', 'غير منتظم']).default('شهري'),
  irregularPaymentMonths: z.number().optional(),
  firstInstallmentDate: z.string().optional(),
  paymentMethod: z.string().optional(),

  // Final Insurance
  finalInsuranceRate: z.number().min(0).max(100).default(0),

  // Advance Payment
  advancePaymentMonths: z.number().min(0).default(0),
  advancePaymentAmount: z.number().min(0).default(0),

  // Check Management
  checkStatus: z.string().default('لم يتقدم بالشيكات'),

  // Annual Increases
  annualIncreaseType: z.enum(['لا يوجد', 'مبلغ ثابت', 'نسبة مئوية', 'نسبة مركبة']).default('لا يوجد'),
  annualIncreaseValue: z.number().min(0).default(0),
  annualIncreaseStartYear: z.number().min(1).default(2),

  // Penalties
  lateFeeType: z.enum(['مبلغ ثابت', 'نسبة مئوية']).default('نسبة مئوية'),
  lateFeeValue: z.number().min(0).default(0),
  gracePeriodDays: z.number().min(0).default(0),
  bouncedCheckFeeType: z.enum(['مبلغ ثابت', 'نسبة مئوية']).default('مبلغ ثابت'),
  bouncedCheckFeeValue: z.number().min(0).default(0),

  // Additional Information
  additionalFees: z.string().optional(),
  importantNotes: z.string().optional(),
  notes: z.string().optional(),

  isActive: z.boolean().default(true),
});

export type InsertContract = z.infer<typeof insertContractSchema>;
export type Contract = InsertContract & {
  id: number;
  createdAt: string;
  updatedAt: string;
  clientName?: string;
  clientType?: string;
  clientPhoneWhatsapp?: string;
  clientAddress?: string;

  // Related data
  products?: ContractProduct[];
  partners?: ContractPartner[];

  // System-managed fields (for display and reports only)
  systemFlags?: string | null;
  autoTerminationSuggested?: number;
  consecutiveMissedPayments?: number;
  totalMissedPayments?: number;
};

// Contract Installment Schema
export const contractInstallmentSchema = z.object({
  installmentId: z.number().optional(),
  contractId: z.number().min(1, "Contract ID is required"),
  productId: z.number().optional(),
  productLabel: z.string().optional(),
  installmentNumber: z.number().min(1, "Installment number is required"),
  installmentDate: z.string().min(1, "Installment date is required"),
  installmentAmount: z.number().min(0, "Installment amount must be positive"),
  baseAmount: z.number().min(0, "Base amount must be positive"),
  taxAmount: z.number().min(0).default(0),
  yearOfContract: z.number().min(1, "Year of contract is required"),
  paymentDueDate: z.string().min(1, "Payment due date is required"),
  remainingAmount: z.number().min(0, "Remaining amount must be positive"),
  isPaid: z.boolean().default(false),
  paidDate: z.string().optional(),
  paidAmount: z.number().min(0).default(0),
  penaltyAmount: z.number().min(0).default(0),
  lateDays: z.number().min(0).default(0),
  paymentMethod: z.string().optional(),
  checkNumber: z.string().optional(),
  bankName: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type ContractInstallment = z.infer<typeof contractInstallmentSchema>;

// Payment Schema
export const insertPaymentSchema = z.object({
  contractId: z.number().min(1, "Contract ID is required"),
  installmentId: z.number().optional(),
  paymentNumber: z.number().min(1, "Payment number is required"),
  dueDate: z.string().min(1, "Due date is required"),
  amount: z.number().min(0, "Amount must be positive"),
  lateFee: z.number().min(0).default(0),
  bouncedCheckFee: z.number().min(0).default(0),
  totalAmount: z.number().min(0, "Total amount must be positive"),
  isPaid: z.boolean().default(false),
  paidDate: z.string().optional(),
  paymentMethod: z.enum(['نقدي', 'شيك', 'تحويل بنكي', 'بطاقة ائتمان']).optional(),
  checkNumber: z.string().optional(),
  bankName: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type InsertPayment = z.infer<typeof insertPaymentSchema>;
export type Payment = InsertPayment & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Financial Summary interface
export interface FinancialSummary {
  totalInstallments: number;
  totalContractValue: number;
  totalBaseValue: number;
  totalTaxValue: number;
  totalPaid: number;
  totalOutstanding: number;
  totalPenalties: number;
  paidInstallments: number;
  overdueInstallments: number;
  paymentProgress: number;
  nextPaymentDate?: string;
  nextPaymentAmount?: number;
}

// Treasury Payments Schema
export const insertTreasuryPaymentSchema = z.object({
  receiptNumber: z.string().min(1, "رقم الإيصال مطلوب"),
  paymentDate: z.string().min(1, "تاريخ الدفع مطلوب"),
  amount: z.number().min(0, "المبلغ يجب أن يكون أكبر من صفر"),
  paymentType: z.enum(['مرتبطة بعقد', 'غير مرتبطة بعقد']),

  // للمدفوعات المرتبطة بعقد
  contractId: z.number().optional(),
  receivableId: z.number().optional(),
  contractNumber: z.string().optional(),
  contractSubject: z.string().optional(),
  clientName: z.string().optional(),
  receivableDescription: z.string().optional(),
  receivableStartDate: z.string().optional(),
  receivableEndDate: z.string().optional(),
  paymentStatus: z.enum(['كامل', 'جزئي']).optional(),

  // للمدفوعات غير المرتبطة بعقد
  nonContractType: z.enum(['رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب']).optional(),

  // تفاصيل طريقة الدفع
  paymentMethod: z.enum(['نقدي', 'شيك']),

  // للشيكات
  checkNumber: z.string().optional(),
  checkDate: z.string().optional(),
  bankName: z.string().optional(),
  checkStatus: z.enum(['عهدة', 'تحت التحصيل', 'أمانة']).optional(),

  // حقول إضافية
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type InsertTreasuryPayment = z.infer<typeof insertTreasuryPaymentSchema>;
export type TreasuryPayment = InsertTreasuryPayment & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Bank Payments Schema
export const insertBankPaymentSchema = z.object({
  bankTransactionNumber: z.string().min(1, "رقم الحركة البنكية مطلوب"),
  paymentDate: z.string().min(1, "تاريخ الدفع مطلوب"),
  amount: z.number().min(0, "المبلغ يجب أن يكون أكبر من صفر"),
  bankName: z.string().min(1, "اسم البنك مطلوب"),
  paymentType: z.enum(['مرتبطة بعقد', 'غير مرتبطة بعقد']),

  // للمدفوعات المرتبطة بعقد
  contractId: z.number().optional(),
  receivableId: z.number().optional(),
  contractNumber: z.string().optional(),
  contractSubject: z.string().optional(),
  clientName: z.string().optional(),
  receivableDescription: z.string().optional(),
  receivableStartDate: z.string().optional(),
  receivableEndDate: z.string().optional(),
  paymentStatus: z.enum(['كامل', 'جزئي']).optional(),

  // للمدفوعات غير المرتبطة بعقد
  nonContractType: z.enum(['رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب']).optional(),

  // حقول إضافية
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type InsertBankPayment = z.infer<typeof insertBankPaymentSchema>;
export type BankPayment = InsertBankPayment & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Cheques Schema
export const insertChequeSchema = z.object({
  checkNumber: z.string().min(1, "رقم الشيك مطلوب"),
  checkDate: z.string().min(1, "تاريخ الشيك مطلوب"),
  amount: z.number().min(0, "المبلغ يجب أن يكون أكبر من صفر"),
  bankName: z.string().min(1, "اسم البنك مطلوب"),
  checkStatus: z.enum(['عهدة', 'تحت التحصيل', 'أمانة', 'محصل', 'مرتد']).default('عهدة'),

  // ربط بالمدفوعة المصدر
  sourceType: z.enum(['treasury', 'bank']),
  sourcePaymentId: z.number().min(1, "معرف المدفوعة المصدر مطلوب"),

  // بيانات العقد (اختيارية)
  contractId: z.number().optional(),
  contractNumber: z.string().optional(),
  contractSubject: z.string().optional(),
  clientName: z.string().optional(),

  // بيانات الاستحقاق (اختيارية)
  receivableId: z.number().optional(),
  receivableDescription: z.string().optional(),

  // تواريخ مهمة
  receivedDate: z.string().min(1, "تاريخ الاستلام مطلوب"),
  depositDate: z.string().optional(),
  collectionDate: z.string().optional(),
  returnDate: z.string().optional(),

  // ملاحظات
  notes: z.string().optional(),
  returnReason: z.string().optional(),

  isActive: z.boolean().default(true),
});

export type InsertCheque = z.infer<typeof insertChequeSchema>;
export type Cheque = InsertCheque & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Cash Receipts Schema (إيصالات استلام نقدية)
export const insertCashReceiptSchema = z.object({
  receiptNumber: z.string().min(1, "رقم الإيصال مطلوب"),
  paymentDate: z.string().min(1, "تاريخ الدفع مطلوب"),
  amount: z.number().min(0, "المبلغ يجب أن يكون أكبر من صفر"),
  paymentType: z.enum(['مرتبطة بعقد', 'غير مرتبطة بعقد']),

  // للمدفوعات المرتبطة بعقد
  contractId: z.number().optional(),
  receivableId: z.number().optional(),
  contractNumber: z.string().optional(),
  contractSubject: z.string().optional(),
  clientName: z.string().optional(),
  receivableDescription: z.string().optional(),
  receivableStartDate: z.string().optional(),
  receivableEndDate: z.string().optional(),
  paymentStatus: z.enum(['كامل', 'جزئي']).optional(),

  // للمدفوعات غير المرتبطة بعقد
  nonContractType: z.enum(['رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب']).optional(),

  // حقول إضافية
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type InsertCashReceipt = z.infer<typeof insertCashReceiptSchema>;
export type CashReceipt = InsertCashReceipt & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Cheque Receipts Schema (إيصالات استلام شيكات)
export const insertChequeReceiptSchema = z.object({
  receiptNumber: z.string().min(1, "رقم الإيصال مطلوب"),
  paymentDate: z.string().min(1, "تاريخ الدفع مطلوب"),
  amount: z.number().min(0, "المبلغ يجب أن يكون أكبر من صفر"),
  paymentType: z.enum(['مرتبطة بعقد', 'غير مرتبطة بعقد']),

  // للمدفوعات المرتبطة بعقد
  contractId: z.number().optional(),
  receivableId: z.number().optional(),
  contractNumber: z.string().optional(),
  contractSubject: z.string().optional(),
  clientName: z.string().optional(),
  receivableDescription: z.string().optional(),
  receivableStartDate: z.string().optional(),
  receivableEndDate: z.string().optional(),
  paymentStatus: z.enum(['كامل', 'جزئي']).optional(),

  // للمدفوعات غير المرتبطة بعقد
  nonContractType: z.enum(['رسوم تفاوض', 'جدية عرض', 'تأمين ابتدائي', 'رسوم طلب']).optional(),

  // بيانات الشيك
  checkNumber: z.string().min(1, "رقم الشيك مطلوب"),
  checkDate: z.string().min(1, "تاريخ الشيك مطلوب"),
  bankName: z.string().min(1, "اسم البنك مطلوب"),
  checkStatus: z.enum(['عهدة', 'تحت التحصيل', 'أمانة']).default('عهدة'),

  // حقول إضافية
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type InsertChequeReceipt = z.infer<typeof insertChequeReceiptSchema>;
export type ChequeReceipt = InsertChequeReceipt & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Note: ContractFinancialDetails schema removed as it was duplicate of contract fields

// Settings Schema
export const insertSettingSchema = z.object({
  key: z.string().min(1, "Setting key is required"),
  value: z.string(),
  description: z.string().optional(),
  category: z.string().optional(),
  isActive: z.boolean().default(true),
});

export type InsertSetting = z.infer<typeof insertSettingSchema>;
export type Setting = InsertSetting & {
  id: number;
  createdAt: string;
  updatedAt: string;
};

// Dashboard Stats interface (for dashboard and reports pages)
export interface DashboardStats {
  totalClients: number;
  totalContracts: number;
  activeContracts: number;
  totalContractValue: number;
  monthlyRevenue: number;
  overduePayments: number;
  todayPayments: any[];
  receivables: any[];
}
