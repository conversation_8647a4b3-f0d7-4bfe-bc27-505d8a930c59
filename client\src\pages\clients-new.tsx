import { useState, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useEnhancedErrorHandler } from "@/hooks/use-enhanced-error-handler";
import { FormErrorDisplay, useFormErrors } from "@/components/form-error-display";
import { ErrorHelpSystem } from "@/components/error-help-system";
import { DuplicateClientAlert, useDuplicateClientAlert } from "@/components/duplicate-client-alert";
import { insertClientSchema, type InsertClient } from "@shared/schema";
import { ClientCard } from "@/components/client-card";
import { IdImageUpload } from "@/components/id-image-upload";
import { DocumentUpload, type DocumentFile } from "@/components/document-upload";
import { useLanguage } from "@/hooks/use-language";
import { useSettings } from "@/contexts/settings-context";
import { generateClientId } from "@/lib/formatters";
import { cn } from "@/lib/utils";
import { getBase64Size, formatFileSize } from "@/lib/image-utils";
import { ArrowRight, Save, Users, Building2, Eye, AlertTriangle } from "lucide-react";
import labels from "@/lib/i18n";

export default function ClientsNew() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { generateClientNumber } = useSettings();
  const queryClient = useQueryClient();
  const [showPreview, setShowPreview] = useState(false);
  const [idImage, setIdImage] = useState<string | null>(null);
  const [documents, setDocuments] = useState<DocumentFile[]>([]);
  const { language, isRTL } = useLanguage();
  const t = labels[language];

  // Memoize the default values to prevent re-renders
  const defaultValues = useMemo(() => ({
    clientId: generateClientNumber(),
    clientType: "أفراد",
    clientName: "",
    clientAddress: "",
    clientPhoneWhatsapp: "",
    clientPhone2: "",
    clientPhone3: "",
    clientEmail: "",
    clientNotes: "",
    clientFinancialGuarantee: "",
    clientFinancial_Category: "",
    clientLegal_Rep: "",
    clientPartner: "",
    clientReg_Number: "",
    clientTaxReg_Number: "",
    clientLegal_Status: "",
    clientRemarks: "",
    clientID_Image: "",
    clientDocuments: "",
    isActive: true,
  }), [generateClientNumber]);

  const form = useForm<InsertClient>({
    resolver: zodResolver(insertClientSchema),
    defaultValues,
  });

  // Enhanced error handling
  const { handleError, handleValidationError } = useEnhancedErrorHandler();
  const { errors, addError, removeError, clearErrors, hasErrors } = useFormErrors();
  const { duplicateAlert, showDuplicateAlert, hideDuplicateAlert } = useDuplicateClientAlert();

  const createClientMutation = useMutation({
    mutationFn: async (data: InsertClient) => {
      console.log('🔥 Sending client data:', data);
      const response = await fetch('/api/clients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Server error response:', errorData);
        console.error('❌ Error details:', errorData.details);

        let errorMessage = errorData.error || 'Failed to create client';

        // إذا كان هناك تفاصيل إضافية، استخدمها
        if (errorData.details && typeof errorData.details === 'string') {
          errorMessage = errorData.details;
        } else if (errorData.details && Array.isArray(errorData.details)) {
          errorMessage += ': ' + errorData.details.join(', ');
        }

        // إنشاء خطأ مع تفاصيل الاستجابة
        const error = new Error(errorMessage);
        // إضافة تفاصيل الخطأ للكائن
        Object.assign(error, errorData);
        throw error;
      }
      return response.json();
    },
    onSuccess: async () => {
      // Invalidate all client queries (including search queries)
      await queryClient.invalidateQueries({
        queryKey: ["/api/clients"],
        exact: false // This will invalidate all queries that start with ['/api/clients']
      });

      // Force refetch all client queries to ensure immediate update
      await queryClient.refetchQueries({
        queryKey: ["/api/clients"],
        exact: false
      });

      toast({
        title: t.success,
        description: isRTL ? "تم إنشاء العميل بنجاح" : "Client created successfully",
        duration: 3000,
      });
      setLocation("/clients");
    },
    onError: async (error: any) => {
      console.error('Create client error:', error);

      // محاولة الحصول على تفاصيل الخطأ من الاستجابة
      let errorData = null;
      try {
        if (error.response) {
          errorData = await error.response.json();
        } else if (typeof error === 'object' && error.errorCode) {
          errorData = error;
        }
      } catch (e) {
        console.log('Could not parse error response');
      }

      const errorMessage = error.message || '';
      const clientId = form.getValues('clientId');

      // إذا كان الخطأ متعلق بتكرار رقم العميل
      if (errorData?.errorCode === 'CLIENT_ID_EXISTS' ||
          errorMessage.includes('العميل مسجل من قبل') ||
          errorMessage.includes('رقم العميل مستخدم من قبل') ||
          errorMessage.includes('duplicate') ||
          errorMessage.includes('UNIQUE constraint failed')) {

        // إظهار تنبيه العميل المكرر مع معلومات العميل الموجود
        showDuplicateAlert(
          errorData?.duplicateClientId || clientId,
          errorData?.existingClient
        );

        // إظهار toast مبسط
        toast({
          title: "العميل مسجل من قبل",
          description: errorData?.guidance || `رقم العميل ${clientId} موجود في النظام مسبقاً`,
          variant: "destructive",
          duration: 5000,
        });

        return;
      }

      // للأخطاء الأخرى، استخدم المعالج المحسن
      handleError(error, {
        operation: 'create_client',
        context: {
          clientId: form.getValues('clientId'),
          clientName: form.getValues('clientName')
        },
        onRetry: () => {
          // Retry the mutation
          createClientMutation.mutate(form.getValues());
        },
        onAction: (action: string) => {
          switch (action) {
            case 'search_client':
            case 'search_existing':
              // الانتقال لصفحة العملاء مع البحث عن الرقم المدخل
              setLocation(`/clients?search=${encodeURIComponent(clientId)}`);
              break;
            case 'generate_id':
            case 'generate_new_id':
              // إنشاء رقم عميل جديد
              const newId = generateClientNumber();
              form.setValue('clientId', newId);
              // إظهار رسالة تأكيد
              toast({
                title: "تم إنشاء رقم عميل جديد",
                description: `الرقم الجديد: ${newId}`,
                variant: "default",
                duration: 3000,
              });
              break;
            case 'review_form':
              // Focus on first error field
              const firstError = Object.keys(form.formState.errors)[0];
              if (firstError) {
                const element = document.querySelector(`[name="${firstError}"]`) as HTMLElement;
                element?.focus();
              }
              break;
          }
        }
      });
    },
  });

  const onSubmit = (data: InsertClient) => {
    console.log('🚀 Client form submitted!', data);

    // Clear previous errors
    clearErrors();

    // Enhanced validation
    const validationErrors: Array<{field: string, message: string, guidance?: string}> = [];

    // Validate client ID
    if (!data.clientId || data.clientId.trim().length < 3) {
      validationErrors.push({
        field: 'clientId',
        message: 'رقم العميل مطلوب ويجب أن يكون 3 أرقام على الأقل',
        guidance: 'أدخل رقم الهوية الوطنية أو رقم مخصص للعميل'
      });
    }

    // Validate client name
    if (!data.clientName || data.clientName.trim().length < 2) {
      validationErrors.push({
        field: 'clientName',
        message: 'اسم العميل مطلوب ويجب أن يكون حرفين على الأقل',
        guidance: 'أدخل الاسم الكامل للعميل'
      });
    }

    // Validate phone number if provided
    if (data.clientPhoneWhatsapp && data.clientPhoneWhatsapp.trim()) {
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,15}$/;
      if (!phoneRegex.test(data.clientPhoneWhatsapp)) {
        validationErrors.push({
          field: 'clientPhoneWhatsapp',
          message: 'رقم الهاتف غير صحيح',
          guidance: 'أدخل رقم هاتف صحيح (مثال: 0501234567 أو +************)'
        });
      }
    }

    // Validate email if provided
    if (data.clientEmail && data.clientEmail.trim() && data.clientEmail !== 'لا يوجد') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.clientEmail)) {
        validationErrors.push({
          field: 'clientEmail',
          message: 'البريد الإلكتروني غير صحيح',
          guidance: 'أدخل عنوان بريد إلكتروني صحيح (مثال: <EMAIL>)'
        });
      }
    }

    // If there are validation errors, show them and stop submission
    if (validationErrors.length > 0) {
      validationErrors.forEach(error => {
        addError(error.field, error.message, error.guidance, 'warning');
      });

      // Focus on first error field
      const firstErrorField = validationErrors[0].field;
      setTimeout(() => {
        const element = document.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
        element?.focus();
        element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100);

      return;
    }

    // Helper function to set default values for optional fields only
    const setDefaultValue = (value: string | undefined | null): string => {
      return value && value.trim() !== "" ? value.trim() : "لا يوجد";
    };

    // Add image and documents to the data with default values for optional fields only
    const submitData = {
      // Required fields - keep as is
      clientId: data.clientId,
      clientType: data.clientType,
      clientName: data.clientName,
      clientPhoneWhatsapp: data.clientPhoneWhatsapp,
      isActive: data.isActive,

      // Optional fields - set default values
      clientAddress: setDefaultValue(data.clientAddress),
      clientPhone2: setDefaultValue(data.clientPhone2),
      clientPhone3: setDefaultValue(data.clientPhone3),
      clientEmail: setDefaultValue(data.clientEmail),
      clientNotes: setDefaultValue(data.clientNotes),
      clientFinancial_Category: setDefaultValue(data.clientFinancial_Category),
      clientLegal_Rep: setDefaultValue(data.clientLegal_Rep),
      clientReg_Number: setDefaultValue(data.clientReg_Number),
      clientTaxReg_Number: setDefaultValue(data.clientTaxReg_Number),
      clientLegal_Status: setDefaultValue(data.clientLegal_Status),
      clientRemarks: setDefaultValue(data.clientRemarks),

      // File fields
      clientID_Image: idImage || "",
      clientDocuments: JSON.stringify(documents),
    };

    console.log('Creating client with data:', submitData); // Debug log
    createClientMutation.mutate(submitData);
  };

  const handlePreview = () => {
    setShowPreview(!showPreview);
  };

  const handlePrint = () => {
    window.print();
  };

  // Calculate total data size
  const getTotalDataSize = () => {
    let totalSize = 0;

    if (idImage) {
      totalSize += getBase64Size(idImage);
    }

    documents.forEach(doc => {
      totalSize += doc.size;
    });

    return totalSize;
  };

  const totalSize = getTotalDataSize();
  const isDataSizeLarge = totalSize > 10 * 1024 * 1024; // 10MB warning

  // Get current form values for preview
  const currentFormValues = form.watch();

  if (showPreview) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className={cn(
                "flex items-center space-x-4",
                isRTL ? "space-x-reverse" : ""
              )}>
                <Button variant="ghost" size="sm" onClick={() => setShowPreview(false)}>
                  <ArrowRight className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                  {isRTL ? "العودة للتعديل" : "Back to Edit"}
                </Button>
                <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <div className={cn(
                  "flex items-center space-x-2",
                  isRTL ? "space-x-reverse" : ""
                )}>
                  <Eye className="h-5 w-5 text-primary" />
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    {isRTL ? "معاينة كارت العميل" : "Client Card Preview"}
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="py-8">
          <ClientCard
            client={{
              ...currentFormValues,
              clientID_Image: idImage,
              clientDocuments: JSON.stringify(documents)
            } as any}
            contracts={[]}
            showPrintButton={true}
            showEditButton={false}
            onPrint={handlePrint}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className={cn(
              "flex items-center space-x-4",
              isRTL ? "space-x-reverse" : ""
            )}>
              <Link href="/clients">
                <Button variant="ghost" size="sm">
                  <ArrowRight className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                  {isRTL ? "العودة للعملاء" : "Back to Clients"}
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
              <div className={cn(
                "flex items-center space-x-2",
                isRTL ? "space-x-reverse" : ""
              )}>
                <Users className="h-5 w-5 text-primary" />
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  {t.addClient}
                </h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Duplicate Client Alert */}
        {duplicateAlert.show && (
          <div className="mb-6">
            <DuplicateClientAlert
              duplicateClientId={duplicateAlert.clientId}
              existingClient={duplicateAlert.existingClient}
              onSearchClient={() => {
                const clientId = duplicateAlert.clientId;
                setLocation(`/clients?search=${encodeURIComponent(clientId)}`);
                hideDuplicateAlert();
              }}
              onGenerateNewId={() => {
                const newId = generateClientNumber();
                form.setValue('clientId', newId);
                hideDuplicateAlert();
                toast({
                  title: "تم إنشاء رقم عميل جديد",
                  description: `الرقم الجديد: ${newId}`,
                  variant: "default",
                  duration: 3000,
                });
              }}
              onDismiss={hideDuplicateAlert}
            />
          </div>
        )}

        {/* Enhanced Error Display */}
        {hasErrors && (
          <div className="mb-6">
            <FormErrorDisplay
              errors={errors}
              onDismiss={clearErrors}
              onFieldFocus={(fieldName) => {
                const element = document.querySelector(`[name="${fieldName}"]`) as HTMLElement;
                element?.focus();
                element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }}
              title="يرجى تصحيح الأخطاء التالية قبل المتابعة:"
            />
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>{isRTL ? "البيانات الأساسية" : "Basic Information"}</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clientId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.clientId} *</FormLabel>
                      <FormControl>
                        <Input
                          key="clientId-input"
                          placeholder={isRTL ? "مثال: 1234567890" : "e.g., 1234567890"}
                          className="border-2 border-yellow-400 focus:border-yellow-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.clientType} *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="border-2 border-yellow-400 focus:border-yellow-500">
                            <SelectValue placeholder={isRTL ? "اختر نوع العميل" : "Select client type"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="أفراد">{t.clientTypes.individuals}</SelectItem>
                          <SelectItem value="شركات">{t.clientTypes.companies}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientName"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{t.clientName} *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={isRTL ? "أدخل اسم العميل" : "Enter client name"}
                          className="border-2 border-yellow-400 focus:border-yellow-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientAddress"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{t.address}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={isRTL ? "أدخل عنوان العميل (اختياري - سيتم وضع 'لا يوجد' إذا ترك فارغاً)" : "Enter client address (optional - will show 'لا يوجد' if left empty)"}
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhoneWhatsapp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم الهاتف/واتساب" : "Phone/WhatsApp"} *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="+201234567890"
                          className="border-2 border-yellow-400 focus:border-yellow-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhone2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم هاتف إضافي" : "Additional Phone"}</FormLabel>
                      <FormControl>
                        <Input placeholder="+201234567890" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientPhone3"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم هاتف ثالث" : "Third Phone"}</FormLabel>
                      <FormControl>
                        <Input placeholder="+201234567890" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.email}</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientNotes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{t.notes}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={isRTL ? "أي ملاحظات إضافية عن العميل" : "Any additional notes about the client"}
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Financial & Legal Information */}
            <Card>
              <CardHeader>
                <CardTitle className={cn(
                  "flex items-center space-x-2",
                  isRTL ? "space-x-reverse" : ""
                )}>
                  <Building2 className="h-5 w-5 text-primary" />
                  <span>{isRTL ? "المعلومات المالية والقانونية" : "Financial & Legal Information"}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="clientFinancial_Category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t.financialCategory}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || ""}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={isRTL ? "اختر التصنيف المالي" : "Select financial category"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ممتاز">ممتاز</SelectItem>
                          <SelectItem value="جيد">جيد</SelectItem>
                          <SelectItem value="متوسط">متوسط</SelectItem>
                          <SelectItem value="ضعيف">ضعيف</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientLegal_Rep"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "الممثل القانوني" : "Legal Representative"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "اسم الممثل القانوني" : "Legal representative name"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientReg_Number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم التسجيل" : "Registration Number"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "رقم التسجيل التجاري" : "Commercial registration number"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientTaxReg_Number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "رقم التسجيل الضريبي" : "Tax Registration Number"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "رقم التسجيل الضريبي" : "Tax registration number"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientLegal_Status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{isRTL ? "الحالة القانونية" : "Legal Status"}</FormLabel>
                      <FormControl>
                        <Input placeholder={isRTL ? "مثال: مؤسسة فردية، شركة محدودة" : "e.g., Sole Proprietorship, LLC"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="clientRemarks"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{isRTL ? "ملاحظات إضافية" : "Additional Remarks"}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={isRTL ? "ملاحظات إضافية مفصلة" : "Detailed additional remarks"}
                          className="resize-none"
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* ID Card Image */}
            <IdImageUpload
              image={idImage}
              onImageChange={setIdImage}
            />

            {/* Additional Documents */}
            <DocumentUpload
              documents={documents}
              onDocumentsChange={setDocuments}
              label={isRTL ? "المستندات الإضافية" : "Additional Documents"}
              maxFiles={10}
            />

            {/* Data Size Indicator */}
            {totalSize > 0 && (
              <div className={cn(
                "flex items-center gap-2 p-3 rounded-lg",
                isDataSizeLarge ? "bg-yellow-50 border border-yellow-200" : "bg-gray-50 border border-gray-200"
              )}>
                {isDataSizeLarge && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                <span className="text-sm">
                  {isRTL ? "حجم البيانات الإجمالي:" : "Total data size:"} {formatFileSize(totalSize)}
                </span>
                {isDataSizeLarge && (
                  <span className="text-xs text-yellow-600">
                    {isRTL ? "(حجم كبير - قد يستغرق وقتاً أطول للحفظ)" : "(Large size - may take longer to save)"}
                  </span>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className={cn(
              "flex gap-4 pt-6",
              isRTL ? "justify-between" : "justify-between"
            )}>
              <div className="flex gap-2">
                <ErrorHelpSystem
                  operation="create_client"
                  className="text-sm"
                />
              </div>

              <div className="flex gap-4">
                <Link href="/clients">
                  <Button variant="outline" type="button">
                    {t.cancel}
                  </Button>
                </Link>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePreview}
                  className="gap-2"
                >
                  <Eye className="h-4 w-4" />
                  {isRTL ? "معاينة كارت العميل" : "Preview Client Card"}
                </Button>
                <Button
                  type="submit"
                  disabled={createClientMutation.isPending}
                  className="gap-2"
                >
                  {createClientMutation.isPending ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  {createClientMutation.isPending ? t.loading : t.save}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </main>
    </div>
  );
}
