import React, { useState } from "react";
import { Link, useLocation } from "wouter";
import {
  BarChart3,
  Users,
  FileText,
  Receipt,
  CreditCard,
  TrendingUp,
  Settings,
  UserCheck,
  Wallet,
  PieChart,
  ChevronRight,
  ChevronLeft,
  Bell,
  Shield,
  UserCog,
  Building2,
  Database
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/hooks/use-language";
import { useSettings } from "@/contexts/settings-context";
import labels from "@/lib/i18n";

const navigationItems = [
  { id: "dashboard", path: "/", icon: BarChart3, label: "dashboard" },
  { id: "clients", path: "/clients", icon: UserCheck, label: "clients" },
  { id: "contracts", path: "/contracts", icon: FileText, label: "contracts" },
  { id: "dues", path: "/receivables", icon: Receipt, label: "dues" },
  { id: "payments", path: "/payments", icon: Wallet, label: "payments" },
  { id: "cheques-management", path: "/cheques-management", icon: CreditCard, label: "chequesManagement" },
  { id: "reports", path: "/reports", icon: PieChart, label: "reports" },
  { id: "alerts", path: "/alerts", icon: Bell, label: "alerts" },
  { id: "audit-trail", path: "/audit-trail", icon: Shield, label: "auditTrail" },

  { id: "reference-data", path: "/reference-data", icon: Database, label: "referenceData" },
  { id: "admin-database", path: "/admin-database", icon: Database, label: "adminDatabase" },
  { id: "settings", path: "/settings", icon: Settings, label: "settings" },
  { id: "users", path: "/users", icon: UserCog, label: "users" },
];

export function Sidebar() {
  const [location] = useLocation();
  const { language, isRTL } = useLanguage();
  const { settings } = useSettings();
  const t = labels[language];
  const [isExpanded, setIsExpanded] = useState(false);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    const timeout = setTimeout(() => {
      setIsExpanded(true);
    }, 1000); // 1 second delay
    setHoverTimeout(timeout);
  };

  const handleMouseLeave = () => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    setIsExpanded(false);
  };

  return (
    <aside
      className={cn(
        "group bg-sidebar dark:bg-sidebar shadow-lg border-sidebar-border dark:border-sidebar-border fixed h-full z-50 transition-all duration-300 ease-in-out flex flex-col overflow-hidden",
        isExpanded ? "w-64" : "w-16",
        isRTL ? "right-0 border-l" : "left-0 border-r"
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Header - Fixed */}
      <div className="p-3 border-b border-sidebar-border dark:border-sidebar-border flex-shrink-0">
        <div className={cn(
          "flex items-center gap-3 transition-all duration-300",
          isRTL ? "flex-row-reverse justify-start" : "flex-row justify-start"
        )}>
          <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
            {settings.companyLogo ? (
              <img
                src={settings.companyLogo}
                alt="Company Logo"
                className="w-full h-full object-contain"
              />
            ) : (
              <FileText className="h-5 w-5 text-primary-foreground" />
            )}
          </div>
          <div
            className={cn(
              "transition-opacity duration-300 overflow-hidden whitespace-nowrap",
              isExpanded ? "opacity-100" : "opacity-0",
              isRTL ? "text-right" : "text-left"
            )}
            dir={isRTL ? "rtl" : "ltr"}
          >
            <h1 className={cn(
              "text-sm font-semibold text-sidebar-foreground dark:text-sidebar-foreground",
              isRTL ? "text-right" : "text-left"
            )}>
              {settings.companyName || (isRTL ? "إدارة العقود" : "Contract Manager")}
            </h1>
            <p className={cn(
              "text-[10px] text-sidebar-foreground/60 dark:text-sidebar-foreground/60",
              isRTL ? "text-right" : "text-left"
            )}>
              {settings.programName || (isRTL ? "نظام إدارة العقود" : "Contract Management System")}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation - Scrollable */}
      <nav className="flex-1 overflow-y-auto p-2 space-y-1">
        <div className="space-y-1">
          {navigationItems.map((item) => {
            const isActive = location === item.path;
            const Icon = item.icon;

            return (
              <Link
                key={item.id}
                href={item.path}
                className={cn(
                  "group/item relative flex items-center gap-3 py-3 px-3 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                  isRTL ? "flex-row-reverse justify-start" : "flex-row justify-start",
                  isActive
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "text-sidebar-foreground/70 dark:text-sidebar-foreground/70 hover:bg-sidebar-accent/50 dark:hover:bg-sidebar-accent/50 hover:text-sidebar-foreground dark:hover:text-sidebar-foreground"
                )}
                title={t[item.label as keyof typeof t]}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                <span
                  className={cn(
                    "font-medium text-sm whitespace-nowrap overflow-hidden transition-all duration-300",
                    isExpanded ? "opacity-100 w-auto" : "opacity-0 w-0",
                    isRTL ? "text-right" : "text-left"
                  )}
                  dir={isRTL ? "rtl" : "ltr"}
                >
                  {t[item.label as keyof typeof t]}
                </span>

                {/* Tooltip for collapsed state */}
                <div
                  className={cn(
                    "absolute z-50 px-3 py-2 text-xs font-medium text-white bg-gray-900 dark:bg-gray-800 rounded-lg shadow-lg",
                    "transition-all duration-200 pointer-events-none whitespace-nowrap top-1/2 -translate-y-1/2",
                    isExpanded
                      ? "opacity-0"
                      : "opacity-0 group/item:hover:opacity-100",
                    isRTL
                      ? "left-full ml-3 text-right"
                      : "right-full mr-3 text-left"
                  )}
                  dir={isRTL ? "rtl" : "ltr"}
                >
                  {t[item.label as keyof typeof t]}
                  {/* Arrow */}
                  <div className={cn(
                    "absolute top-1/2 -translate-y-1/2 w-2 h-2 bg-gray-900 dark:bg-gray-800 rotate-45",
                    isRTL ? "right-0 translate-x-1/2" : "left-0 -translate-x-1/2"
                  )} />
                </div>
              </Link>
            );
          })}
        </div>


      </nav>
    </aside>
  );
}
