// ===== ENHANCED ERROR MESSAGES DICTIONARY =====
// Author: Augment Code
// Description: Comprehensive error messages dictionary with Arabic translations and user guidance

/**
 * Error Messages Dictionary
 * Provides user-friendly error messages with context and guidance
 */
class ErrorMessages {
  
  /**
   * Validation Error Messages
   */
  static VALIDATION = {
    // General validation
    REQUIRED_FIELD: {
      message: 'هذا الحقل مطلوب',
      guidance: 'يرجى ملء هذا الحقل قبل المتابعة',
      severity: 'warning'
    },
    INVALID_FORMAT: {
      message: 'تنسيق البيانات غير صحيح',
      guidance: 'يرجى التحقق من تنسيق البيانات المدخلة',
      severity: 'warning'
    },
    
    // Client validation
    CLIENT_ID_REQUIRED: {
      message: 'رقم العميل مطلوب',
      guidance: 'يرجى إدخال رقم العميل. يمكن أن يكون رقم الهوية أو رقم مخصص',
      severity: 'warning'
    },
    CLIENT_ID_INVALID: {
      message: 'رقم العميل غير صحيح',
      guidance: 'يجب أن يكون رقم العميل مكون من أرقام فقط ولا يقل عن 3 أرقام',
      severity: 'warning'
    },
    CLIENT_ID_EXISTS: {
      message: 'العميل مسجل من قبل',
      guidance: 'رقم العميل هذا موجود في النظام مسبقاً. يمكنك البحث عن العميل الموجود أو استخدام رقم عميل مختلف',
      severity: 'error',
      action: 'search_client',
      confirmable: false,
      showExistingClient: true
    },
    CLIENT_NAME_REQUIRED: {
      message: 'اسم العميل مطلوب',
      guidance: 'يرجى إدخال الاسم الكامل للعميل',
      severity: 'warning'
    },
    CLIENT_NAME_TOO_SHORT: {
      message: 'اسم العميل قصير جداً',
      guidance: 'يجب أن يكون اسم العميل مكون من حرفين على الأقل',
      severity: 'warning'
    },
    CLIENT_PHONE_INVALID: {
      message: 'رقم الهاتف غير صحيح',
      guidance: 'يرجى إدخال رقم هاتف صحيح (مثال: 0501234567 أو +************)',
      severity: 'warning'
    },
    CLIENT_EMAIL_INVALID: {
      message: 'البريد الإلكتروني غير صحيح',
      guidance: 'يرجى إدخال عنوان بريد إلكتروني صحيح (مثال: <EMAIL>)',
      severity: 'warning'
    },
    
    // Contract validation
    CONTRACT_CLIENT_REQUIRED: {
      message: 'يجب اختيار عميل للعقد',
      guidance: 'يرجى اختيار عميل من القائمة أو إضافة عميل جديد',
      severity: 'error',
      action: 'add_client'
    },
    CONTRACT_START_DATE_REQUIRED: {
      message: 'تاريخ بداية العقد مطلوب',
      guidance: 'يرجى تحديد تاريخ بداية العقد',
      severity: 'warning'
    },
    CONTRACT_START_DATE_PAST: {
      message: 'تاريخ بداية العقد في الماضي',
      guidance: 'تاريخ البداية المحدد قد مضى. هل تريد المتابعة؟',
      severity: 'warning',
      confirmable: true
    },
    CONTRACT_DURATION_INVALID: {
      message: 'مدة العقد غير صحيحة',
      guidance: 'يجب أن تكون مدة العقد أكبر من صفر',
      severity: 'warning'
    },
    CONTRACT_NO_PRODUCTS: {
      message: 'العقد لا يحتوي على منتجات',
      guidance: 'يجب إضافة منتج واحد على الأقل للعقد',
      severity: 'error',
      action: 'add_product'
    },
    CONTRACT_REGION_REQUIRED: {
      message: 'منطقة العقد مطلوبة',
      guidance: 'يرجى تحديد المنطقة محل العقد من القائمة',
      severity: 'warning'
    },
    CONTRACT_DEPARTMENT_REQUIRED: {
      message: 'القسم مطلوب',
      guidance: 'يرجى تحديد القسم المسؤول عن العقد',
      severity: 'warning'
    },
    
    // Product validation
    PRODUCT_NAME_REQUIRED: {
      message: 'اسم المنتج مطلوب',
      guidance: 'يرجى إدخال اسم وصفي للمنتج أو الخدمة',
      severity: 'warning'
    },
    PRODUCT_AREA_INVALID: {
      message: 'المساحة غير صحيحة',
      guidance: 'يجب أن تكون المساحة رقم موجب أكبر من صفر',
      severity: 'warning'
    },
    PRODUCT_PRICE_INVALID: {
      message: 'سعر المتر غير صحيح',
      guidance: 'يجب أن يكون سعر المتر رقم موجب أكبر من صفر',
      severity: 'warning'
    },
    PRODUCT_BILLING_TYPE_REQUIRED: {
      message: 'نوع الفوترة مطلوب',
      guidance: 'يرجى اختيار نوع الفوترة (شهري، ربع سنوي، نصف سنوي، سنوي)',
      severity: 'warning'
    },
    
    // Payment validation
    PAYMENT_AMOUNT_INVALID: {
      message: 'مبلغ الدفع غير صحيح',
      guidance: 'يجب أن يكون مبلغ الدفع رقم موجب أكبر من صفر',
      severity: 'warning'
    },
    PAYMENT_DATE_REQUIRED: {
      message: 'تاريخ الدفع مطلوب',
      guidance: 'يرجى تحديد تاريخ الدفع',
      severity: 'warning'
    },
    PAYMENT_METHOD_REQUIRED: {
      message: 'طريقة الدفع مطلوبة',
      guidance: 'يرجى اختيار طريقة الدفع (نقدي، شيك، تحويل بنكي)',
      severity: 'warning'
    },
    PAYMENT_RECEIPT_NUMBER_REQUIRED: {
      message: 'رقم الإيصال مطلوب',
      guidance: 'يرجى إدخال رقم الإيصال للدفعة',
      severity: 'warning'
    },
    
    // Settings validation
    COMPANY_NAME_REQUIRED: {
      message: 'اسم الشركة مطلوب',
      guidance: 'يرجى إدخال اسم الشركة في الإعدادات العامة',
      severity: 'error',
      action: 'open_settings'
    },
    CURRENCY_REQUIRED: {
      message: 'العملة مطلوبة',
      guidance: 'يرجى اختيار العملة الأساسية للنظام',
      severity: 'error',
      action: 'open_settings'
    }
  };

  /**
   * Database Error Messages
   */
  static DATABASE = {
    CONNECTION_FAILED: {
      message: 'فشل الاتصال بقاعدة البيانات',
      guidance: 'تعذر الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات النظام',
      severity: 'critical',
      technical: true
    },
    CONSTRAINT_VIOLATION: {
      message: 'البيانات موجودة مسبقاً',
      guidance: 'هذه البيانات مسجلة من قبل. يرجى التحقق من البيانات المدخلة',
      severity: 'error'
    },
    FOREIGN_KEY_VIOLATION: {
      message: 'مرجع البيانات غير صحيح',
      guidance: 'البيانات المرجعية غير موجودة. يرجى التحقق من صحة البيانات',
      severity: 'error'
    },
    NOT_NULL_VIOLATION: {
      message: 'حقل مطلوب مفقود',
      guidance: 'يوجد حقل مطلوب لم يتم ملؤه. يرجى مراجعة البيانات',
      severity: 'warning'
    },
    DATABASE_LOCKED: {
      message: 'قاعدة البيانات مقفلة',
      guidance: 'قاعدة البيانات مستخدمة حالياً. يرجى المحاولة بعد قليل',
      severity: 'warning',
      retryable: true
    },
    DATABASE_BUSY: {
      message: 'قاعدة البيانات مشغولة',
      guidance: 'النظام مشغول حالياً. يرجى المحاولة مرة أخرى',
      severity: 'warning',
      retryable: true
    }
  };

  /**
   * Network Error Messages
   */
  static NETWORK = {
    CONNECTION_FAILED: {
      message: 'فشل الاتصال بالخادم',
      guidance: 'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت',
      severity: 'error',
      retryable: true
    },
    TIMEOUT: {
      message: 'انتهت مهلة الاتصال',
      guidance: 'استغرقت العملية وقتاً أطول من المتوقع. يرجى المحاولة مرة أخرى',
      severity: 'warning',
      retryable: true
    },
    SERVER_ERROR: {
      message: 'خطأ في الخادم',
      guidance: 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً أو الاتصال بالدعم الفني',
      severity: 'error',
      technical: true
    },
    BAD_REQUEST: {
      message: 'طلب غير صحيح',
      guidance: 'البيانات المرسلة غير صحيحة. يرجى مراجعة البيانات المدخلة',
      severity: 'warning'
    },
    UNAUTHORIZED: {
      message: 'غير مصرح بالوصول',
      guidance: 'ليس لديك صلاحية لتنفيذ هذا الإجراء',
      severity: 'error'
    },
    FORBIDDEN: {
      message: 'ممنوع الوصول',
      guidance: 'هذا الإجراء غير مسموح. يرجى الاتصال بالمسؤول',
      severity: 'error'
    },
    NOT_FOUND: {
      message: 'البيانات غير موجودة',
      guidance: 'البيانات المطلوبة غير موجودة أو تم حذفها',
      severity: 'warning'
    }
  };

  /**
   * Business Logic Error Messages
   */
  static BUSINESS = {
    INSUFFICIENT_BALANCE: {
      message: 'الرصيد غير كافي',
      guidance: 'الرصيد المتاح غير كافي لإتمام هذه العملية',
      severity: 'warning'
    },
    INVALID_DATE_RANGE: {
      message: 'نطاق التاريخ غير صحيح',
      guidance: 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية',
      severity: 'warning'
    },
    CONTRACT_EXPIRED: {
      message: 'العقد منتهي الصلاحية',
      guidance: 'هذا العقد منتهي الصلاحية ولا يمكن تعديله',
      severity: 'error'
    },
    PAYMENT_ALREADY_EXISTS: {
      message: 'الدفعة موجودة مسبقاً',
      guidance: 'يوجد دفعة بنفس رقم الإيصال. يرجى التحقق من رقم الإيصال',
      severity: 'error'
    },
    REFERENCE_DATA_MISSING: {
      message: 'بيانات مرجعية مطلوبة',
      guidance: 'يجب إعداد البيانات المرجعية أولاً قبل المتابعة',
      severity: 'error',
      action: 'setup_reference_data'
    }
  };

  /**
   * File Operation Error Messages
   */
  static FILE = {
    UPLOAD_FAILED: {
      message: 'فشل رفع الملف',
      guidance: 'تعذر رفع الملف. يرجى التحقق من حجم الملف ونوعه',
      severity: 'error'
    },
    FILE_TOO_LARGE: {
      message: 'حجم الملف كبير جداً',
      guidance: 'حجم الملف يتجاوز الحد المسموح. الحد الأقصى 10 ميجابايت',
      severity: 'warning'
    },
    INVALID_FILE_TYPE: {
      message: 'نوع الملف غير مدعوم',
      guidance: 'نوع الملف غير مدعوم. الأنواع المدعومة: PDF, DOC, DOCX, JPG, PNG',
      severity: 'warning'
    },
    FILE_NOT_FOUND: {
      message: 'الملف غير موجود',
      guidance: 'الملف المطلوب غير موجود أو تم حذفه',
      severity: 'warning'
    }
  };

  /**
   * Get error message by code
   */
  static getMessage(category, code, context = {}) {
    const categoryMessages = this[category.toUpperCase()];
    if (!categoryMessages) {
      return this.getDefaultMessage();
    }

    const errorInfo = categoryMessages[code];
    if (!errorInfo) {
      return this.getDefaultMessage();
    }

    return {
      ...errorInfo,
      context,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get default error message
   */
  static getDefaultMessage() {
    return {
      message: 'حدث خطأ غير متوقع',
      guidance: 'يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني',
      severity: 'error',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Format error message for display
   */
  static formatForDisplay(category, code, context = {}) {
    const errorInfo = this.getMessage(category, code, context);
    
    return {
      title: errorInfo.message,
      description: errorInfo.guidance,
      variant: this.getSeverityVariant(errorInfo.severity),
      duration: this.getDurationBySeverity(errorInfo.severity),
      action: errorInfo.action,
      retryable: errorInfo.retryable,
      confirmable: errorInfo.confirmable,
      technical: errorInfo.technical
    };
  }

  /**
   * Get toast variant by severity
   */
  static getSeverityVariant(severity) {
    switch (severity) {
      case 'critical':
      case 'error':
        return 'destructive';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'destructive';
    }
  }

  /**
   * Get display duration by severity
   */
  static getDurationBySeverity(severity) {
    switch (severity) {
      case 'critical':
        return 10000; // 10 seconds
      case 'error':
        return 7000;  // 7 seconds
      case 'warning':
        return 5000;  // 5 seconds
      case 'info':
        return 3000;  // 3 seconds
      default:
        return 5000;
    }
  }

  /**
   * Get contextual error message for specific operations
   */
  static getContextualMessage(operation, errorType, details = {}) {
    const operationMessages = {
      'create_client': {
        title: 'خطأ في إنشاء العميل',
        prefix: 'فشل في إنشاء العميل:'
      },
      'update_client': {
        title: 'خطأ في تحديث العميل',
        prefix: 'فشل في تحديث بيانات العميل:'
      },
      'delete_client': {
        title: 'خطأ في حذف العميل',
        prefix: 'فشل في حذف العميل:'
      },
      'create_contract': {
        title: 'خطأ في إنشاء العقد',
        prefix: 'فشل في إنشاء العقد:'
      },
      'update_contract': {
        title: 'خطأ في تحديث العقد',
        prefix: 'فشل في تحديث العقد:'
      },
      'create_payment': {
        title: 'خطأ في تسجيل الدفعة',
        prefix: 'فشل في تسجيل الدفعة:'
      },
      'load_data': {
        title: 'خطأ في تحميل البيانات',
        prefix: 'فشل في تحميل البيانات:'
      }
    };

    const operationInfo = operationMessages[operation] || {
      title: 'خطأ في العملية',
      prefix: 'فشل في تنفيذ العملية:'
    };

    const baseError = this.getMessage('VALIDATION', errorType, details);

    return {
      ...baseError,
      title: operationInfo.title,
      message: `${operationInfo.prefix} ${baseError.message}`,
      operation
    };
  }
}

module.exports = ErrorMessages;
