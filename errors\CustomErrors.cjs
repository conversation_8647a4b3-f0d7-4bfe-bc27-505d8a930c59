// ===== CUSTOM ERROR CLASSES =====
// Author: Augment Code
// Description: Custom error classes for different types of application errors

/**
 * Base Application Error Class
 * All custom errors should extend this class
 */
class AppError extends Error {
  constructor(message, statusCode = 500, errorCode = 'INTERNAL_ERROR', details = null) {
    super(message);
    
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.isOperational = true; // Indicates this is an expected operational error
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Convert error to JSON format for API responses
   */
  toJSON() {
    return {
      error: {
        name: this.name,
        message: this.message,
        statusCode: this.statusCode,
        errorCode: this.errorCode,
        details: this.details,
        timestamp: this.timestamp,
        ...(process.env.NODE_ENV === 'development' && { stack: this.stack })
      }
    };
  }
}

/**
 * Validation Error - 400 Bad Request
 * Used for input validation failures
 */
class ValidationError extends AppError {
  constructor(message = 'بيانات غير صحيحة', details = null) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }

  static fromValidationResult(validationResult) {
    const details = validationResult.errors || validationResult.details || null;
    return new ValidationError('فشل في التحقق من صحة البيانات', details);
  }
}

/**
 * Unauthorized Error - 401 Unauthorized
 * Used for authentication failures
 */
class UnauthorizedError extends AppError {
  constructor(message = 'غير مصرح بالوصول') {
    super(message, 401, 'UNAUTHORIZED');
  }
}

/**
 * Forbidden Error - 403 Forbidden
 * Used for authorization failures (user is authenticated but doesn't have permission)
 */
class ForbiddenError extends AppError {
  constructor(message = 'ممنوع الوصول') {
    super(message, 403, 'FORBIDDEN');
  }
}

/**
 * Not Found Error - 404 Not Found
 * Used when requested resource is not found
 */
class NotFoundError extends AppError {
  constructor(message = 'المورد غير موجود', resource = null) {
    const details = resource ? { resource } : null;
    super(message, 404, 'NOT_FOUND', details);
  }
}

/**
 * Conflict Error - 409 Conflict
 * Used for resource conflicts (e.g., duplicate entries)
 */
class ConflictError extends AppError {
  constructor(message = 'تضارب في البيانات', details = null) {
    super(message, 409, 'CONFLICT', details);
  }
}

/**
 * Database Error - 500 Internal Server Error
 * Used for database-related errors
 */
class DatabaseError extends AppError {
  constructor(message = 'خطأ في قاعدة البيانات', originalError = null) {
    const details = originalError ? {
      originalMessage: originalError.message,
      code: originalError.code,
      errno: originalError.errno
    } : null;
    
    super(message, 500, 'DATABASE_ERROR', details);
    this.originalError = originalError;
  }
}

/**
 * External Service Error - 502 Bad Gateway
 * Used for external API/service failures
 */
class ExternalServiceError extends AppError {
  constructor(message = 'خطأ في الخدمة الخارجية', service = null, originalError = null) {
    const details = {
      service,
      ...(originalError && { originalMessage: originalError.message })
    };
    
    super(message, 502, 'EXTERNAL_SERVICE_ERROR', details);
    this.originalError = originalError;
  }
}

/**
 * Rate Limit Error - 429 Too Many Requests
 * Used when rate limits are exceeded
 */
class RateLimitError extends AppError {
  constructor(message = 'تم تجاوز الحد المسموح من الطلبات', retryAfter = null) {
    const details = retryAfter ? { retryAfter } : null;
    super(message, 429, 'RATE_LIMIT_EXCEEDED', details);
  }
}

/**
 * File Upload Error - 400 Bad Request
 * Used for file upload related errors
 */
class FileUploadError extends AppError {
  constructor(message = 'خطأ في رفع الملف', details = null) {
    super(message, 400, 'FILE_UPLOAD_ERROR', details);
  }
}

/**
 * Business Logic Error - 422 Unprocessable Entity
 * Used for business rule violations
 */
class BusinessLogicError extends AppError {
  constructor(message = 'خطأ في منطق العمل', rule = null) {
    const details = rule ? { rule } : null;
    super(message, 422, 'BUSINESS_LOGIC_ERROR', details);
  }
}

/**
 * Timeout Error - 408 Request Timeout
 * Used when operations timeout
 */
class TimeoutError extends AppError {
  constructor(message = 'انتهت مهلة العملية', operation = null) {
    const details = operation ? { operation } : null;
    super(message, 408, 'TIMEOUT_ERROR', details);
  }
}

/**
 * Configuration Error - 500 Internal Server Error
 * Used for configuration-related errors
 */
class ConfigurationError extends AppError {
  constructor(message = 'خطأ في الإعدادات', configKey = null) {
    const details = configKey ? { configKey } : null;
    super(message, 500, 'CONFIGURATION_ERROR', details);
  }
}

/**
 * Network Error - 503 Service Unavailable
 * Used for network-related errors
 */
class NetworkError extends AppError {
  constructor(message = 'خطأ في الشبكة', details = null) {
    super(message, 503, 'NETWORK_ERROR', details);
  }
}

/**
 * Maintenance Error - 503 Service Unavailable
 * Used when system is under maintenance
 */
class MaintenanceError extends AppError {
  constructor(message = 'النظام تحت الصيانة', estimatedTime = null) {
    const details = estimatedTime ? { estimatedTime } : null;
    super(message, 503, 'MAINTENANCE_MODE', details);
  }
}

// Export all error classes
module.exports = {
  AppError,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  DatabaseError,
  ExternalServiceError,
  RateLimitError,
  FileUploadError,
  BusinessLogicError,
  TimeoutError,
  ConfigurationError,
  NetworkError,
  MaintenanceError
};
