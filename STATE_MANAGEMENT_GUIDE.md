# دليل إدارة الحالة المركزية - Zustand

## نظرة عامة

تم تطبيق نظام إدارة حالة مركزي شامل باستخدام Zustand يوفر:
- إدارة حالة مركزية خفيفة وسريعة
- Custom hooks سهلة الاستخدام
- معالجة تلقائية للـ loading والأخطاء
- نظام إشعارات متقدم
- API middleware موحد
- دعم TypeScript كامل

## البنية الجديدة

### 📁 الملفات المنشأة:

#### 🏪 Stores:
```
client/src/store/
├── index.js                 # تصدير مركزي لجميع الـ stores
├── settingsStore.js         # إدارة الإعدادات
├── referenceDataStore.js    # إدارة البيانات المرجعية
└── appStore.js             # إدارة الحالة العامة للتطبيق
```

#### 🎣 Custom Hooks:
```
client/src/hooks/
└── useStores.js            # Custom hooks للتفاعل مع الـ stores
```

#### 🔧 Utilities:
```
client/src/lib/
└── apiMiddleware.js        # نظام API middleware موحد
```

#### 📄 المكونات المحدثة:
```
client/src/pages/
├── settings-updated.tsx     # مكون الإعدادات المحدث
├── reference-data-updated.tsx # مكون البيانات المرجعية المحدث
└── App-updated.tsx         # مكون التطبيق الرئيسي المحدث
```

#### 🧪 ملفات الاختبار:
```
client/src/
└── test-stores.tsx         # مكون اختبار النظام الجديد
```

## الـ Stores المتاحة

### 1. Settings Store
إدارة إعدادات التطبيق مع:
- تحميل وحفظ الإعدادات
- تتبع التغييرات غير المحفوظة
- معالجة الأخطاء
- Cache محلي

```javascript
import { useSettingsManager } from '@/hooks/useStores';

const { settings, updateSetting, saveSettings, status } = useSettingsManager();
```

### 2. Reference Data Store
إدارة البيانات المرجعية مع:
- تحميل القوائم المختلفة
- إضافة/تعديل/حذف العناصر
- البحث في القوائم
- إدارة حالات التحميل لكل قائمة

```javascript
import { useReferenceList } from '@/hooks/useStores';

const { items, addItem, updateItem, deleteItem } = useReferenceList('banks');
```

### 3. App Store
إدارة الحالة العامة مع:
- إدارة التحميل العام
- نظام الإشعارات
- إدارة الـ modals
- تتبع الأداء

```javascript
import { useNotificationManager, useLoadingManager } from '@/hooks/useStores';

const { showSuccess, showError } = useNotificationManager();
const { withLoading } = useLoadingManager();
```

### 4. Clients Store
إدارة العملاء مع:
- تحميل وإدارة قائمة العملاء
- إضافة/تعديل/حذف العملاء
- البحث والفلترة
- إدارة العميل المحدد وعقوده
- معالجة أخطاء شاملة

```javascript
import { useClientsManager, useClientSelection } from '@/hooks/useStores';

const { clients, addClient, updateClient, deleteClient } = useClientsManager();
const { selectedClient, selectClient } = useClientSelection();
```

### 5. Contracts Store
إدارة العقود مع:
- تحميل وإدارة قائمة العقود
- إضافة/تعديل/حذف العقود
- البحث والفلترة المتقدمة
- إدارة مكونات العقد وحساباته
- إنشاء PDF للعقود

```javascript
import { useContractsManager, useContractSelection } from '@/hooks/useStores';

const { contracts, addContract, generatePDF } = useContractsManager();
const { selectedContract, contractComponents } = useContractSelection();
```

### 6. Payments Store
إدارة المدفوعات مع:
- إدارة أنواع مختلفة (خزينة، بنك، نقدي، شيكات)
- إضافة/تعديل/حذف المدفوعات
- البحث والفلترة المتقدمة
- طباعة إيصالات الدفع
- إحصائيات المدفوعات

```javascript
import { usePaymentsManager, usePaymentTypes } from '@/hooks/useStores';

const { payments, addPayment, printReceipt } = usePaymentsManager();
const { treasuryPayments, bankPayments } = usePaymentTypes();
```

### 7. Receivables Store
إدارة الاستحقاقات مع:
- تتبع الاستحقاقات (مستحقة، قادمة، مسددة)
- تحديث حالات الاستحقاق
- تسجيل المدفوعات وربطها
- إنشاء فواتير للاستحقاقات
- حساب المتأخرات والمبالغ المستحقة

```javascript
import { useReceivablesManager, useReceivablesByStatus } from '@/hooks/useStores';

const { receivables, markAsPaid, generateInvoice } = useReceivablesManager();
const { overdueReceivables, upcomingReceivables } = useReceivablesByStatus();
```

### 8. Cheques Store
إدارة الشيكات مع:
- تتبع حالات الشيكات (خزينة، عهدة، تحصيل، محصل، مرتد)
- إضافة/تعديل/حذف الشيكات
- تحديث حالات الشيكات مع workflow
- البحث والفلترة المتقدمة
- معالجة الشيكات المرتدة

```javascript
import { useChequesManager, useChequesByStatus } from '@/hooks/useStores';

const { cheques, addCheque, updateStatus } = useChequesManager();
const { treasuryCheques, bouncedCheques } = useChequesByStatus();
```

### 9. Users Store
إدارة المستخدمين والصلاحيات مع:
- إدارة المستخدمين (إضافة، تعديل، حذف)
- إدارة الصلاحيات والأدوار
- تغيير كلمات المرور
- تتبع المستخدم الحالي
- فحص الصلاحيات

```javascript
import { useUsersManager, useCurrentUserManager } from '@/hooks/useStores';

const { users, addUser, updatePermissions } = useUsersManager();
const { currentUser, hasPermission } = useCurrentUserManager();
```

### 10. Reports Store
إدارة التقارير والإحصائيات مع:
- إحصائيات لوحة التحكم
- تقارير مالية متقدمة
- تقارير العقود والعملاء
- إنشاء تقارير مخصصة
- تصدير التقارير (PDF, Excel)

```javascript
import { useReportsManager, useReportTypes } from '@/hooks/useStores';

const { dashboardStats, generateReport, exportReport } = useReportsManager();
const { financialReports, contractReports } = useReportTypes();
```

## Custom Hooks المتاحة

### إدارة الإعدادات:
```javascript
// Hook شامل لإدارة الإعدادات
const { settings, updateSetting, saveSettings, status } = useSettingsManager();

// Hook للحصول على إعداد محدد
const companyName = useSetting('companyName', 'اسم افتراضي');

// Hook لإدارة نموذج الإعدادات
const { handleFieldChange, handleSubmit, hasUnsavedChanges } = useSettingsForm();
```

### إدارة البيانات المرجعية:
```javascript
// Hook لإدارة قائمة مرجعية محددة
const { items, addItem, updateItem, deleteItem, refreshList } = useReferenceList('banks');

// Hook للبحث في القوائم
const { results } = useReferenceSearch('banks', 'البنك الأهلي');

// Hook لتهيئة البيانات المرجعية
const { isInitialized } = useReferenceManager();
```

### إدارة الحالة العامة:
```javascript
// Hook لإدارة الإشعارات
const { showSuccess, showError, showWarning, showInfo } = useNotificationManager();

// Hook لإدارة التحميل
const { isLoading, withLoading } = useLoadingManager();

// Hook لاستدعاءات API
const { apiCall } = useApiCall();

// Hook لإرسال النماذج
const { submitForm } = useFormSubmit();
```

### إدارة العملاء:
```javascript
// Hook شامل لإدارة العملاء
const { clients, filteredClients, addClient, updateClient, deleteClient } = useClientsManager();

// Hook لإدارة تحديد العملاء
const { selectedClient, selectClient, clearSelection } = useClientSelection();

// Hook لإدارة الفلاتر والبحث
const { searchQuery, setSearchQuery, clearFilters } = useClientsFiltersManager();
```

### إدارة العقود:
```javascript
// Hook شامل لإدارة العقود
const { contracts, filteredContracts, addContract, generatePDF } = useContractsManager();

// Hook لإدارة تحديد العقود وتفاصيلها
const { selectedContract, contractComponents, selectContract } = useContractSelection();
```

### إدارة المدفوعات:
```javascript
// Hook شامل لإدارة المدفوعات
const { payments, filteredPayments, addPayment, printReceipt } = usePaymentsManager();

// Hook لإدارة أنواع المدفوعات
const { treasuryPayments, bankPayments, fetchByType } = usePaymentTypes();
```

### إدارة الاستحقاقات:
```javascript
// Hook شامل لإدارة الاستحقاقات
const { receivables, markAsPaid, generateInvoice } = useReceivablesManager();

// Hook لإدارة الاستحقاقات حسب الحالة
const { overdueReceivables, upcomingReceivables } = useReceivablesByStatus();
```

### إدارة الشيكات:
```javascript
// Hook شامل لإدارة الشيكات
const { cheques, addCheque, updateStatus, deleteCheque } = useChequesManager();

// Hook لإدارة الشيكات حسب الحالة
const { treasuryCheques, bouncedCheques, fetchByStatus } = useChequesByStatus();
```

### إدارة المستخدمين:
```javascript
// Hook شامل لإدارة المستخدمين
const { users, addUser, updatePermissions, changePassword } = useUsersManager();

// Hook لإدارة المستخدم الحالي
const { currentUser, hasPermission, userPermissions } = useCurrentUserManager();
```

### إدارة التقارير:
```javascript
// Hook شامل لإدارة التقارير
const { dashboardStats, generateReport, exportReport } = useReportsManager();

// Hook لإدارة أنواع التقارير
const { financialReports, contractReports, saveConfig } = useReportTypes();
```

### Hooks مساعدة:
```javascript
// Hook لتهيئة جميع الـ stores
useStoreInitializer();

// Hook للحصول على حالة التطبيق
const { isAppReady, isInitializing } = useAppStatus();
```

## كيفية الاستخدام

### 1. في المكونات العادية:

```javascript
import React from 'react';
import { useSettingsManager, useNotificationManager } from '@/hooks/useStores';

export default function MyComponent() {
  const { settings, updateSetting } = useSettingsManager();
  const { showSuccess } = useNotificationManager();

  const handleChange = (value) => {
    updateSetting('companyName', value);
    showSuccess('تم التحديث بنجاح');
  };

  return (
    <div>
      <h1>{settings.companyName}</h1>
      <button onClick={() => handleChange('اسم جديد')}>
        تحديث
      </button>
    </div>
  );
}
```

### 2. في النماذج:

```javascript
import React from 'react';
import { useSettingsForm } from '@/hooks/useStores';

export default function SettingsForm() {
  const {
    settings,
    handleFieldChange,
    handleSubmit,
    hasUnsavedChanges,
    isLoading
  } = useSettingsForm();

  return (
    <form onSubmit={handleSubmit}>
      <input
        value={settings.companyName}
        onChange={(e) => handleFieldChange('companyName', e.target.value)}
      />
      <button type="submit" disabled={isLoading || !hasUnsavedChanges}>
        حفظ
      </button>
    </form>
  );
}
```

### 3. مع API calls:

```javascript
import React from 'react';
import { useApiCall, useNotificationManager } from '@/hooks/useStores';

export default function DataComponent() {
  const { apiCall } = useApiCall();
  const { showSuccess } = useNotificationManager();

  const fetchData = async () => {
    const result = await apiCall(
      () => fetch('/api/data').then(res => res.json()),
      {
        loadingMessage: 'جاري تحميل البيانات...',
        successMessage: 'تم تحميل البيانات بنجاح',
        showSuccessNotification: true
      }
    );

    if (result.success) {
      console.log('Data:', result.data);
    }
  };

  return (
    <button onClick={fetchData}>
      تحميل البيانات
    </button>
  );
}
```

### 4. إدارة العملاء:

```javascript
import React, { useState } from 'react';
import { useClientsManager, useClientSelection } from '@/hooks/useStores';

export default function ClientsPage() {
  const [newClientName, setNewClientName] = useState('');
  const {
    clients,
    filteredClients,
    status,
    addClient,
    deleteClient
  } = useClientsManager();

  const { selectedClient, selectClient } = useClientSelection();

  const handleAddClient = async () => {
    const result = await addClient({
      clientName: newClientName,
      clientType: 'فرد',
      clientId: `CL-${Date.now()}`
    });

    if (result.success) {
      setNewClientName('');
    }
  };

  const handleDeleteClient = async (clientId) => {
    if (confirm('هل أنت متأكد؟')) {
      await deleteClient(clientId);
    }
  };

  return (
    <div>
      <h1>العملاء ({filteredClients.length})</h1>

      {/* Add Client Form */}
      <div>
        <input
          value={newClientName}
          onChange={(e) => setNewClientName(e.target.value)}
          placeholder="اسم العميل"
        />
        <button
          onClick={handleAddClient}
          disabled={status.isAdding}
        >
          إضافة عميل
        </button>
      </div>

      {/* Clients List */}
      {status.isLoading ? (
        <div>جاري التحميل...</div>
      ) : (
        <div>
          {filteredClients.map(client => (
            <div
              key={client.id}
              onClick={() => selectClient(client)}
              style={{
                border: selectedClient?.id === client.id ? '2px solid blue' : '1px solid gray',
                padding: '10px',
                margin: '5px',
                cursor: 'pointer'
              }}
            >
              <h3>{client.clientName}</h3>
              <p>{client.clientId} - {client.clientType}</p>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteClient(client.id);
                }}
                disabled={status.isDeleting}
              >
                حذف
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Selected Client Details */}
      {selectedClient && (
        <div>
          <h2>العميل المحدد: {selectedClient.clientName}</h2>
          <p>الكود: {selectedClient.clientId}</p>
        </div>
      )}
    </div>
  );
}
```

## إضافة Store جديد

### 1. إنشاء Store جديد:

```javascript
// client/src/store/clientsStore.js
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export const useClientsStore = create()(
  devtools(
    persist(
      immer((set, get) => ({
        // State
        clients: [],
        isLoading: false,
        error: null,

        // Actions
        actions: {
          async fetchClients() {
            set((state) => {
              state.isLoading = true;
              state.error = null;
            });

            try {
              const response = await fetch('/api/clients');
              const clients = await response.json();
              
              set((state) => {
                state.clients = clients;
                state.isLoading = false;
              });
            } catch (error) {
              set((state) => {
                state.isLoading = false;
                state.error = error.message;
              });
            }
          },

          async addClient(client) {
            // Implementation
          }
        }
      })),
      { name: 'clients-store' }
    ),
    { name: 'clients-store' }
  )
);

// Selectors
export const useClients = () => useClientsStore((state) => state.clients);
export const useClientsLoading = () => useClientsStore((state) => state.isLoading);
export const useClientsActions = () => useClientsStore((state) => state.actions);
```

### 2. إنشاء Custom Hooks:

```javascript
// في client/src/hooks/useStores.js
export const useClientsManager = () => {
  const clients = useClients();
  const isLoading = useClientsLoading();
  const actions = useClientsActions();

  useEffect(() => {
    if (clients.length === 0 && !isLoading) {
      actions.fetchClients();
    }
  }, [clients.length, isLoading, actions]);

  const addClient = useCallback(async (client) => {
    try {
      await actions.addClient(client);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }, [actions]);

  return {
    clients,
    isLoading,
    addClient,
    actions
  };
};
```

### 3. استخدام Store الجديد:

```javascript
import React from 'react';
import { useClientsManager } from '@/hooks/useStores';

export default function ClientsPage() {
  const { clients, isLoading, addClient } = useClientsManager();

  if (isLoading) {
    return <div>جاري التحميل...</div>;
  }

  return (
    <div>
      <h1>العملاء ({clients.length})</h1>
      {clients.map(client => (
        <div key={client.id}>{client.name}</div>
      ))}
    </div>
  );
}
```

## API Middleware

### الاستخدام الأساسي:

```javascript
import apiMiddleware from '@/lib/apiMiddleware';

// GET request
const data = await apiMiddleware.get('/api/clients');

// POST request
const result = await apiMiddleware.post('/api/clients', {
  name: 'عميل جديد',
  email: '<EMAIL>'
});

// مع خيارات إضافية
const data = await apiMiddleware.get('/api/clients', {
  loadingMessage: 'جاري تحميل العملاء...',
  errorMessage: 'فشل في تحميل العملاء',
  showErrorNotification: true
});
```

### مميزات API Middleware:

- **Cache تلقائي** للـ GET requests
- **Retry logic** للطلبات الفاشلة
- **Loading states** تلقائية
- **Error handling** موحد
- **Request deduplication** لمنع الطلبات المكررة
- **Performance tracking** لقياس أوقات الاستجابة

## نصائح للاستخدام

### 1. تنظيم الـ Stores:
- استخدم store منفصل لكل module رئيسي
- اجعل الـ actions واضحة ومحددة
- استخدم selectors للوصول للبيانات

### 2. إدارة الأخطاء:
- استخدم try/catch في الـ actions
- اعرض رسائل خطأ واضحة للمستخدم
- سجل الأخطاء للمراجعة

### 3. الأداء:
- استخدم selectors محددة بدلاً من الـ store كاملاً
- تجنب إعادة العرض غير الضرورية
- استخدم useCallback و useMemo عند الحاجة

### 4. الاختبار:
- اختبر الـ stores منفصلة عن المكونات
- استخدم mock data للاختبارات
- اختبر حالات الخطأ والتحميل

## الفوائد المحققة

- ✅ **كود أنظف**: لا حاجة لـ useState متكررة
- ✅ **إدارة مركزية**: جميع البيانات في مكان واحد
- ✅ **أداء محسن**: إعادة عرض محسنة وذكية
- ✅ **سهولة الصيانة**: كود منظم وقابل للتوسع
- ✅ **تجربة مطور محسنة**: DevTools وTypeScript
- ✅ **معالجة أخطاء موحدة**: نظام خطأ شامل

النظام الآن يوفر إدارة حالة متقدمة وقابلة للتوسع! 🎉
