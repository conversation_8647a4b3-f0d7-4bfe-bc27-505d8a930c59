function errorHandler(err, req, res, next) {
  console.error("❌ [ERROR HANDLER] =======================");
  console.error("📍 Error Message:", err.message);
  console.error("📄 Error Stack:", err.stack);
  console.error("📥 Request Body:", req.body);
  console.error("🧭 Request URL:", req.originalUrl);
  console.error("=========================================");

  res.status(err.status || 500).json({
    success: false,
    message: err.message || "حدث خطأ غير متوقع في الخادم",
    stack: process.env.NODE_ENV === "development" ? err.stack : undefined
  });
}

module.exports = errorHandler;
