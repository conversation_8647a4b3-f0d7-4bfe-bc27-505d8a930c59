// ===== ERROR HELPER FUNCTIONS =====
// Author: Augment Code
// Description: Helper functions for throwing different types of errors easily

const {
  AppError,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  DatabaseError,
  ExternalServiceError,
  RateLimitError,
  FileUploadError,
  BusinessLogicError,
  TimeoutError,
  ConfigurationError,
  NetworkError,
  MaintenanceError
} = require('./CustomErrors.cjs');

/**
 * Error Helper Class
 * Provides convenient methods for throwing different types of errors
 */
class ErrorHelpers {
  
  /**
   * Throw validation error
   */
  static throwValidationError(message = 'بيانات غير صحيحة', details = null) {
    throw new ValidationError(message, details);
  }

  /**
   * Throw validation error from validation result
   */
  static throwValidationErrorFromResult(validationResult) {
    throw ValidationError.fromValidationResult(validationResult);
  }

  /**
   * Throw unauthorized error
   */
  static throwUnauthorized(message = 'غير مصرح بالوصول') {
    throw new UnauthorizedError(message);
  }

  /**
   * Throw forbidden error
   */
  static throwForbidden(message = 'ممنوع الوصول') {
    throw new ForbiddenError(message);
  }

  /**
   * Throw not found error
   */
  static throwNotFound(message = 'المورد غير موجود', resource = null) {
    throw new NotFoundError(message, resource);
  }

  /**
   * Throw conflict error
   */
  static throwConflict(message = 'تضارب في البيانات', details = null) {
    throw new ConflictError(message, details);
  }

  /**
   * Throw database error
   */
  static throwDatabaseError(message = 'خطأ في قاعدة البيانات', originalError = null) {
    throw new DatabaseError(message, originalError);
  }

  /**
   * Throw external service error
   */
  static throwExternalServiceError(message = 'خطأ في الخدمة الخارجية', service = null, originalError = null) {
    throw new ExternalServiceError(message, service, originalError);
  }

  /**
   * Throw rate limit error
   */
  static throwRateLimitError(message = 'تم تجاوز الحد المسموح من الطلبات', retryAfter = null) {
    throw new RateLimitError(message, retryAfter);
  }

  /**
   * Throw file upload error
   */
  static throwFileUploadError(message = 'خطأ في رفع الملف', details = null) {
    throw new FileUploadError(message, details);
  }

  /**
   * Throw business logic error
   */
  static throwBusinessLogicError(message = 'خطأ في منطق العمل', rule = null) {
    throw new BusinessLogicError(message, rule);
  }

  /**
   * Throw timeout error
   */
  static throwTimeoutError(message = 'انتهت مهلة العملية', operation = null) {
    throw new TimeoutError(message, operation);
  }

  /**
   * Throw configuration error
   */
  static throwConfigurationError(message = 'خطأ في الإعدادات', configKey = null) {
    throw new ConfigurationError(message, configKey);
  }

  /**
   * Throw network error
   */
  static throwNetworkError(message = 'خطأ في الشبكة', details = null) {
    throw new NetworkError(message, details);
  }

  /**
   * Throw maintenance error
   */
  static throwMaintenanceError(message = 'النظام تحت الصيانة', estimatedTime = null) {
    throw new MaintenanceError(message, estimatedTime);
  }

  /**
   * Throw generic app error
   */
  static throwAppError(message, statusCode = 500, errorCode = 'INTERNAL_ERROR', details = null) {
    throw new AppError(message, statusCode, errorCode, details);
  }

  /**
   * Check condition and throw error if false
   */
  static assert(condition, errorType = 'AppError', message = 'Assertion failed', ...args) {
    if (!condition) {
      switch (errorType) {
        case 'ValidationError':
          this.throwValidationError(message, ...args);
          break;
        case 'UnauthorizedError':
          this.throwUnauthorized(message);
          break;
        case 'ForbiddenError':
          this.throwForbidden(message);
          break;
        case 'NotFoundError':
          this.throwNotFound(message, ...args);
          break;
        case 'ConflictError':
          this.throwConflict(message, ...args);
          break;
        case 'DatabaseError':
          this.throwDatabaseError(message, ...args);
          break;
        case 'BusinessLogicError':
          this.throwBusinessLogicError(message, ...args);
          break;
        default:
          this.throwAppError(message, ...args);
      }
    }
  }

  /**
   * Check if resource exists, throw NotFoundError if not
   */
  static assertExists(resource, resourceName = 'المورد', customMessage = null) {
    if (!resource) {
      const message = customMessage || `${resourceName} غير موجود`;
      this.throwNotFound(message, resourceName);
    }
    return resource;
  }

  /**
   * Check if user is authorized, throw UnauthorizedError if not
   */
  static assertAuthorized(user, customMessage = null) {
    if (!user) {
      const message = customMessage || 'يجب تسجيل الدخول أولاً';
      this.throwUnauthorized(message);
    }
    return user;
  }

  /**
   * Check if user has permission, throw ForbiddenError if not
   */
  static assertPermission(hasPermission, customMessage = null) {
    if (!hasPermission) {
      const message = customMessage || 'ليس لديك صلاحية للقيام بهذا الإجراء';
      this.throwForbidden(message);
    }
  }

  /**
   * Validate required fields
   */
  static validateRequired(data, requiredFields, customMessage = null) {
    const missingFields = [];
    
    requiredFields.forEach(field => {
      if (!data[field] && data[field] !== 0 && data[field] !== false) {
        missingFields.push(field);
      }
    });

    if (missingFields.length > 0) {
      const message = customMessage || `الحقول التالية مطلوبة: ${missingFields.join(', ')}`;
      this.throwValidationError(message, { missingFields });
    }
  }

  /**
   * Validate email format
   */
  static validateEmail(email, customMessage = null) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      const message = customMessage || 'صيغة البريد الإلكتروني غير صحيحة';
      this.throwValidationError(message, { field: 'email', value: email });
    }
  }

  /**
   * Validate string length
   */
  static validateLength(value, minLength = 0, maxLength = Infinity, fieldName = 'الحقل', customMessage = null) {
    if (typeof value !== 'string') {
      this.throwValidationError(`${fieldName} يجب أن يكون نص`);
    }

    if (value.length < minLength) {
      const message = customMessage || `${fieldName} يجب أن يكون على الأقل ${minLength} أحرف`;
      this.throwValidationError(message, { field: fieldName, minLength, actualLength: value.length });
    }

    if (value.length > maxLength) {
      const message = customMessage || `${fieldName} يجب أن يكون أقل من ${maxLength} حرف`;
      this.throwValidationError(message, { field: fieldName, maxLength, actualLength: value.length });
    }
  }

  /**
   * Validate number range
   */
  static validateRange(value, min = -Infinity, max = Infinity, fieldName = 'الرقم', customMessage = null) {
    if (typeof value !== 'number' || isNaN(value)) {
      this.throwValidationError(`${fieldName} يجب أن يكون رقم صحيح`);
    }

    if (value < min) {
      const message = customMessage || `${fieldName} يجب أن يكون على الأقل ${min}`;
      this.throwValidationError(message, { field: fieldName, min, actualValue: value });
    }

    if (value > max) {
      const message = customMessage || `${fieldName} يجب أن يكون أقل من ${max}`;
      this.throwValidationError(message, { field: fieldName, max, actualValue: value });
    }
  }

  /**
   * Wrap async function with error handling
   */
  static asyncWrapper(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Create error from unknown error
   */
  static createFromUnknown(error, defaultMessage = 'حدث خطأ غير متوقع') {
    if (error instanceof AppError) {
      return error;
    }

    if (error instanceof Error) {
      // Check for specific error types and convert them
      if (error.name === 'ValidationError') {
        return new ValidationError(error.message, error.details);
      }
      
      if (error.code === 'SQLITE_CONSTRAINT' || error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return new ConflictError('البيانات موجودة مسبقاً', { originalError: error.message });
      }

      if (error.code && error.code.startsWith('SQLITE_')) {
        return new DatabaseError('خطأ في قاعدة البيانات', error);
      }

      // Generic error conversion
      return new AppError(error.message || defaultMessage, 500, 'INTERNAL_ERROR', {
        originalError: error.message,
        stack: error.stack
      });
    }

    // Handle non-Error objects
    return new AppError(defaultMessage, 500, 'INTERNAL_ERROR', { originalError: String(error) });
  }
}

module.exports = ErrorHelpers;
