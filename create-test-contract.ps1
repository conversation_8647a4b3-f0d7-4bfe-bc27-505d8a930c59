$json = Get-Content 'test-contract.json' -Raw

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5001/api/contracts' -Method POST -Body $json -ContentType 'application/json'
    Write-Output "Contract created successfully!"
    Write-Output "Contract ID: $($response.contractId)"
    Write-Output "Response: $($response | ConvertTo-Json)"
} catch {
    Write-Output "Error creating contract:"
    Write-Output $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Output "Response body: $responseBody"
    }
}
