# 🔄 ميزة الفوترة غير المنتظمة

## 🎯 نظرة عامة

تم إضافة ميزة الفوترة غير المنتظمة لكل منتج في العقد، مما يسمح بتخصيص فترات الدفع بطريقتين:

### 📋 الطرق المتاحة:

#### 1. **الفترات المنتظمة (Uniform Intervals)**
- تحديد **إجمالي عدد الدفعات**
- تحديد **الفترة بين كل دفعة والأخرى بالأشهر**
- مثال: 6 دفعات كل شهرين = دفعة كل شهرين لمدة سنة

#### 2. **الفترات المخصصة (Custom Intervals)**
- تحديد الفترة بين كل دفعة والتي تليها بشكل منفصل
- مثال: الدفعة الأولى بعد شهر، الثانية بعد 3 أشهر، الثالثة بعد شهرين، إلخ

## 🛠️ التحديثات المطبقة

### 1. **قاعدة البيانات**
```sql
-- تم تحديث CHECK constraint لجدول Contracts
paymentFrequency CHECK (paymentFrequency IN (
  'شهري', 
  'ربع سنوي', 
  'نصف سنوي', 
  'سنوي', 
  'غير منتظم'  -- ✅ جديد
))
```

### 2. **واجهة المستخدم**

#### ✅ قسم جديد في كل منتج: "إعدادات الفوترة"
- **نوع الفوترة:** قائمة منسدلة تشمل "غير منتظم"
- **عدد الدفعات السنوية:** يتم حسابه تلقائياً
- **إعدادات الفوترة غير المنتظمة:** تظهر عند اختيار "غير منتظم"

#### ✅ خيارات الفوترة غير المنتظمة:

**أ. الفترات المنتظمة:**
- حقل: إجمالي عدد الدفعات
- حقل: الفترة بين الدفعات (بالأشهر)

**ب. الفترات المخصصة:**
- قائمة ديناميكية للفترات
- إمكانية إضافة/حذف فترات
- عرض ملخص: إجمالي الدفعات والمدة

### 3. **منطق البرمجة**

#### ✅ دالة calculatePaymentFrequency محدثة:
```javascript
// تتحقق من صحة إعدادات الفوترة غير المنتظمة
// تعود إلى "شهري" إذا كانت الإعدادات غير صحيحة
```

#### ✅ حقول جديدة في نموذج المنتج:
```javascript
{
  customPaymentType: "",        // "uniform" أو "custom"
  totalInstallments: 0,         // للفترات المنتظمة
  monthsBetweenPayments: 0,     // للفترات المنتظمة
  customIntervals: []           // للفترات المخصصة
}
```

## 🎨 تصميم الواجهة

### 🟡 ألوان مميزة للفوترة غير المنتظمة:
- **إطار برتقالي** للحقول
- **خلفية برتقالية فاتحة** للقسم
- **أيقونات برتقالية** للأزرار

### 📱 تجربة المستخدم:
- **إخفاء/إظهار تلقائي** للإعدادات حسب النوع المختار
- **تنظيف الحقول** عند تغيير نوع الفوترة
- **ملخص فوري** للإعدادات المخصصة
- **تحقق من صحة البيانات** قبل الحفظ

## 🧪 كيفية الاستخدام

### 1. **إنشاء عقد جديد:**
1. أدخل بيانات المنتج الأساسية
2. في قسم "إعدادات الفوترة"، اختر "غير منتظم"
3. اختر طريقة تحديد الفترات:

### 2. **للفترات المنتظمة:**
```
إجمالي عدد الدفعات: 6
الفترة بين الدفعات: 2 شهر
النتيجة: 6 دفعات كل شهرين
```

### 3. **للفترات المخصصة:**
```
من الدفعة 1 إلى 2: 1 شهر
من الدفعة 2 إلى 3: 3 أشهر  
من الدفعة 3 إلى 4: 2 شهر
النتيجة: 4 دفعات بفترات مختلفة
```

## 🔍 التحقق من صحة البيانات

### ✅ للفترات المنتظمة:
- عدد الدفعات > 0
- الفترة بين الدفعات > 0

### ✅ للفترات المخصصة:
- وجود فترة واحدة على الأقل
- كل فترة > 0

### ⚠️ في حالة البيانات غير الصحيحة:
- يتم استخدام "شهري" كافتراضي
- عرض رسالة تحذير للمستخدم

## 🎯 الفوائد

### 📈 مرونة أكبر:
- **عقود موسمية:** دفعات في مواسم معينة
- **عقود مشاريع:** دفعات حسب مراحل المشروع
- **عقود خاصة:** فترات مخصصة حسب اتفاق العميل

### 💼 حالات الاستخدام:
- **عقود زراعية:** دفعات حسب مواسم الحصاد
- **عقود سياحية:** دفعات حسب المواسم السياحية
- **عقود إنشاءات:** دفعات حسب مراحل البناء
- **عقود تعليمية:** دفعات حسب الفصول الدراسية

## 🔮 التطوير المستقبلي

### 🚀 ميزات مقترحة:
1. **قوالب جاهزة** للفترات الشائعة
2. **تقويم بصري** لعرض مواعيد الدفعات
3. **تنبيهات ذكية** للدفعات غير المنتظمة
4. **تقارير مخصصة** للفوترة غير المنتظمة
5. **ربط مع التقويم** لتذكير العملاء

### 📊 تحسينات الحسابات:
- **حساب الفوائد** للفترات المختلفة
- **تعديل التأمين** حسب نوع الفوترة
- **تحليل التدفق النقدي** للفترات غير المنتظمة

## ✅ الحالة الحالية

### 🎉 مكتمل:
- ✅ تحديث قاعدة البيانات
- ✅ واجهة المستخدم
- ✅ منطق التحقق من البيانات
- ✅ دعم الفترات المنتظمة والمخصصة

### 🔄 قيد التطوير:
- 🔄 ربط مع حسابات الاستحقاقات
- 🔄 تقارير الفوترة غير المنتظمة
- 🔄 واجهة إدارة الدفعات

الميزة جاهزة للاستخدام! 🚀
