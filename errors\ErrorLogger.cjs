// ===== ERROR LOGGER =====
// Author: Augment Code
// Description: Centralized error logging system with different levels and detailed information

// Load environment variables
require('dotenv').config();

const fs = require('fs');
const path = require('path');

/**
 * Error Logger Class
 * Handles logging of errors with different levels and formats
 */
class ErrorLogger {
  constructor() {
    this.config = {
      logLevel: process.env.LOG_LEVEL || 'info',
      logFile: process.env.LOG_FILE || './logs/app.log',
      errorLogFile: process.env.ERROR_LOG_FILE || './logs/error.log',
      enableConsoleLog: process.env.ENABLE_CONSOLE_LOG !== 'false',
      enableFileLog: process.env.ENABLE_FILE_LOG !== 'false',
      logFormat: process.env.LOG_FORMAT || 'json', // json or text
      maxLogSize: process.env.LOG_MAX_SIZE || '10mb',
      maxLogFiles: parseInt(process.env.LOG_MAX_FILES) || 5,
      includeStackTrace: process.env.NODE_ENV === 'development'
    };

    // Ensure logs directory exists
    this.ensureLogDirectory();
  }

  /**
   * Ensure logs directory exists
   */
  ensureLogDirectory() {
    const logDir = path.dirname(this.config.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
      console.log(`📁 Created logs directory: ${logDir}`);
    }
  }

  /**
   * Get log level priority
   */
  getLogLevelPriority(level) {
    const levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    return levels[level] || 2;
  }

  /**
   * Check if log level should be logged
   */
  shouldLog(level) {
    const currentPriority = this.getLogLevelPriority(this.config.logLevel);
    const messagePriority = this.getLogLevelPriority(level);
    return messagePriority <= currentPriority;
  }

  /**
   * Format log entry
   */
  formatLogEntry(level, message, error = null, context = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...context
    };

    if (error) {
      logEntry.error = {
        name: error.name,
        message: error.message,
        statusCode: error.statusCode,
        errorCode: error.errorCode,
        details: error.details,
        isOperational: error.isOperational,
        ...(this.config.includeStackTrace && { stack: error.stack })
      };

      // Add request information if available
      if (context.req) {
        logEntry.request = {
          method: context.req.method,
          url: context.req.url,
          userAgent: context.req.get ? context.req.get('User-Agent') : context.req.headers['user-agent'],
          ip: context.req.ip || (context.req.connection ? context.req.connection.remoteAddress : 'unknown'),
          headers: this.sanitizeHeaders(context.req.headers || {})
        };
      }

      // Add user information if available
      if (context.user) {
        logEntry.user = {
          id: context.user.id,
          email: context.user.email,
          role: context.user.role
        };
      }
    }

    if (this.config.logFormat === 'json') {
      try {
        return JSON.stringify(logEntry, null, 2);
      } catch (jsonError) {
        // Fallback to simple format if JSON.stringify fails
        return `[${timestamp}] ${level.toUpperCase()}: ${message}${error ? ` - ${error.message}` : ''} [JSON_ERROR: ${jsonError.message}]`;
      }
    } else {
      return `[${timestamp}] ${level.toUpperCase()}: ${message}${error ? ` - ${error.message}` : ''}`;
    }
  }

  /**
   * Sanitize headers to remove sensitive information
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
    
    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * Write log to file
   */
  writeToFile(logEntry, isError = false) {
    if (!this.config.enableFileLog) return;

    const logFile = isError ? this.config.errorLogFile : this.config.logFile;
    const logDir = path.dirname(logFile);

    // Ensure directory exists
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    try {
      fs.appendFileSync(logFile, logEntry + '\n');
    } catch (writeError) {
      console.error('❌ Failed to write to log file:', writeError.message);
    }
  }

  /**
   * Log to console with colors
   */
  logToConsole(level, logEntry) {
    if (!this.config.enableConsoleLog) return;

    const colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[37m'  // White
    };

    const reset = '\x1b[0m';
    const color = colors[level] || colors.info;

    console.log(`${color}${logEntry}${reset}`);
  }

  /**
   * Log error with full context
   */
  logError(error, context = {}) {
    if (!this.shouldLog('error')) return;

    const message = `Error occurred: ${error.message}`;
    const logEntry = this.formatLogEntry('error', message, error, context);

    this.logToConsole('error', logEntry);
    this.writeToFile(logEntry, true);

    // Also write to general log file
    this.writeToFile(logEntry, false);
  }

  /**
   * Log warning
   */
  logWarning(message, context = {}) {
    if (!this.shouldLog('warn')) return;

    const logEntry = this.formatLogEntry('warn', message, null, context);
    this.logToConsole('warn', logEntry);
    this.writeToFile(logEntry);
  }

  /**
   * Log info
   */
  logInfo(message, context = {}) {
    if (!this.shouldLog('info')) return;

    const logEntry = this.formatLogEntry('info', message, null, context);
    this.logToConsole('info', logEntry);
    this.writeToFile(logEntry);
  }

  /**
   * Log debug
   */
  logDebug(message, context = {}) {
    if (!this.shouldLog('debug')) return;

    const logEntry = this.formatLogEntry('debug', message, null, context);
    this.logToConsole('debug', logEntry);
    this.writeToFile(logEntry);
  }

  /**
   * Log database error specifically
   */
  logDatabaseError(error, query = null, params = null, context = {}) {
    const enhancedContext = {
      ...context,
      database: {
        query: query ? query.substring(0, 200) : null,
        params: params,
        errorCode: error.code,
        errno: error.errno
      }
    };

    this.logError(error, enhancedContext);
  }

  /**
   * Log request error with full request details
   */
  logRequestError(error, req, res = null) {
    const context = {
      req,
      response: res ? {
        statusCode: res.statusCode,
        headers: res.getHeaders()
      } : null
    };

    this.logError(error, context);
  }

  /**
   * Log performance metrics
   */
  logPerformance(operation, duration, context = {}) {
    const message = `Performance: ${operation} took ${duration}ms`;
    const enhancedContext = {
      ...context,
      performance: {
        operation,
        duration,
        timestamp: new Date().toISOString()
      }
    };

    if (duration > (process.env.SLOW_OPERATION_THRESHOLD || 1000)) {
      this.logWarning(`Slow operation detected: ${message}`, enhancedContext);
    } else {
      this.logDebug(message, enhancedContext);
    }
  }

  /**
   * Log security event
   */
  logSecurityEvent(event, severity = 'warn', context = {}) {
    const message = `Security Event: ${event}`;
    const enhancedContext = {
      ...context,
      security: {
        event,
        severity,
        timestamp: new Date().toISOString()
      }
    };

    if (severity === 'critical') {
      this.logError(new Error(message), enhancedContext);
    } else {
      this.logWarning(message, enhancedContext);
    }
  }
}

// Create singleton instance
const errorLogger = new ErrorLogger();

// Export logger instance and class
module.exports = {
  ErrorLogger,
  errorLogger
};
