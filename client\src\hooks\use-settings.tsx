import { useQuery } from "@tanstack/react-query";

interface Settings {
  id: number;
  companyName: string;
  companyNameEn: string;
  currency: string;
  currencySymbol: string;
  dateFormat: string;
  language: string;
  timezone: string;
  fiscalYearStart: string;
  taxRate: number;
  createdAt: string;
  updatedAt: string;
}

export function useSettings() {
  const { data: settings } = useQuery<Settings>({
    queryKey: ['/api/settings'],
    queryFn: async () => {
      const response = await fetch('/api/settings');
      if (!response.ok) throw new Error('Failed to fetch settings');
      return response.json();
    },
    // Default settings if API fails
    initialData: {
      id: 1,
      companyName: "شركة إدارة العقود",
      companyNameEn: "Contract Management Company",
      currency: "EGP",
      currencySymbol: "ج.م",
      language: "ar",
      timezone: "Africa/Cairo",
      fiscalYearStart: "01/01",
      taxRate: 14,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  });

  // Note: formatCurrency is deprecated. Use useCurrency hook instead for consistent currency formatting
  const formatCurrency = (amount: number): string => {
    if (!amount && amount !== 0) return '';

    const currencySymbol = settings?.currencySymbol || 'ج.م';
    const formattedAmount = new Intl.NumberFormat('ar-EG', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);

    return `${formattedAmount} ${currencySymbol}`;
  };

  const formatDate = (dateString: string | Date): string => {
    if (!dateString) return '';

    try {
      const date = dateString instanceof Date ? dateString : new Date(dateString);
      const language = settings?.language || 'ar';

      if (language === 'ar') {
        // العربي: yyyy/mm/dd
        return new Intl.DateTimeFormat('ar-EG', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }).format(date).replace(/(\d{2})\/(\d{2})\/(\d{4})/, '$3/$2/$1');
      } else {
        // الإنجليزي: dd/mm/yyyy
        return new Intl.DateTimeFormat('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        }).format(date);
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString.toString();
    }
  };

  const formatDateTime = (dateString: string): string => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('ar-EG', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting datetime:', error);
      return dateString;
    }
  };

  const formatNumber = (number: number): string => {
    if (!number && number !== 0) return '';
    
    return new Intl.NumberFormat('ar-EG', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(number);
  };

  const formatPercentage = (percentage: number): string => {
    if (!percentage && percentage !== 0) return '';
    
    return `${formatNumber(percentage)}%`;
  };

  return {
    settings,
    formatCurrency,
    formatDate,
    formatDateTime,
    formatNumber,
    formatPercentage
  };
}
